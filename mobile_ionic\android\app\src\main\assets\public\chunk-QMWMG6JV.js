import{a as ie}from"./chunk-NX7QWPJF.js";import{a as ne}from"./chunk-FULEFYAM.js";import{$ as W,$a as $,A as M,Bb as w,C as f,Cb as E,D as g,Db as T,F as i,G as r,H as l,I as P,J as u,Ja as A,K as h,L as O,M as c,Ma as q,N as _,Na as y,O as L,Q as z,R as j,S as Q,V,W as F,Wa as B,X as b,ab as I,cb as G,ea as S,fb as H,gb as U,ha as D,jb as J,ka as R,kb as K,lb as X,oa as N,q as x,qb as Y,r as v,tb as Z,ub as ee,vb as te,y as a,z as m}from"./chunk-QCXYQNJC.js";import"./chunk-6WVAEWPV.js";import"./chunk-HYNAH5QB.js";import"./chunk-5AIHQZWU.js";import"./chunk-4PQ5B4D2.js";import"./chunk-HC6MZPB3.js";import"./chunk-SV2ZKNWA.js";import"./chunk-EPGIQT2W.js";import"./chunk-RS5W3JWO.js";import"./chunk-DY4KE6AI.js";import"./chunk-GIGBYVJT.js";import"./chunk-KGEDUKSE.js";import"./chunk-MCRJI3T3.js";import"./chunk-OBBPMR2I.js";import"./chunk-AMQPVFGX.js";import"./chunk-KKCAABTQ.js";import"./chunk-OFX7WKKZ.js";import"./chunk-F4H6ZFEG.js";import"./chunk-NMYJD6OP.js";import"./chunk-XXJXE6HG.js";import"./chunk-SV7S5NYR.js";import"./chunk-WTCPO44B.js";import"./chunk-QVY4QQUF.js";import"./chunk-2HRRFJKF.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-BAKMWPBW.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import{g as k}from"./chunk-2R6CW7ES.js";var oe=(()=>{class o{constructor(e,n,t){this.modalCtrl=e,this.router=n,this.toastCtrl=t}dismiss(){this.modalCtrl.dismiss()}viewOnMap(){this.modalCtrl.dismiss(),this.router.navigate(["/tabs/map"],{queryParams:{lat:this.center.latitude,lng:this.center.longitude,name:this.center.name,viewOnly:"true"}}),this.toastCtrl.create({message:`Showing ${this.center.name} on map`,duration:2e3,color:"success"}).then(e=>e.present())}getDirections(){this.modalCtrl.dismiss(),this.router.navigate(["/tabs/map"],{queryParams:{lat:this.center.latitude,lng:this.center.longitude,name:this.center.name,directions:"true"}}),this.toastCtrl.create({message:`Getting directions to ${this.center.name}`,duration:2e3,color:"success"}).then(e=>e.present())}getDisasterTypeIcon(e){if(!e)return"alert-circle-outline";let n=e.toLowerCase();return n.includes("earthquake")||n.includes("quake")?"earth-outline":n.includes("flood")||n.includes("flash")?"water-outline":n.includes("typhoon")||n.includes("storm")?"thunderstorm-outline":n.includes("fire")?"flame-outline":"alert-circle-outline"}getStatusColor(e){if(!e)return"medium";let n=e.toLowerCase();return n.includes("active")||n.includes("open")?"success":n.includes("inactive")||n.includes("closed")?"warning":n.includes("full")?"danger":"medium"}static{this.\u0275fac=function(n){return new(n||o)(m(w),m(S),m(E))}}static{this.\u0275cmp=M({type:o,selectors:[["app-evacuation-center-modal"]],inputs:{center:"center"},decls:25,vars:4,consts:[[1,"modal-container"],[1,"close-button",3,"click"],["name","close-circle","color","danger"],[1,"center-name"],[1,"center-image"],["src","assets/evacuation-center.jpg","onerror","this.src='assets/evacuation-placeholder.jpg'",3,"alt"],[1,"info-section"],[1,"info-label"],[1,"info-value","contact"],["name","call-outline"],[1,"info-value","address"],["name","location-outline"],[1,"directions-button"],["expand","block","color","primary",3,"click"],["name","navigate","slot","start"]],template:function(n,t){n&1&&(i(0,"div",0)(1,"div",1),u("click",function(){return t.dismiss()}),l(2,"ion-icon",2),r(),i(3,"h2",3),c(4),r(),i(5,"div",4),l(6,"img",5),r(),i(7,"div",6)(8,"div",7),c(9,"Contact Number"),r(),i(10,"div",8),l(11,"ion-icon",9),i(12,"span"),c(13),r()()(),i(14,"div",6)(15,"div",7),c(16,"Address"),r(),i(17,"div",10),l(18,"ion-icon",11),i(19,"span"),c(20),r()()(),i(21,"div",12)(22,"ion-button",13),u("click",function(){return t.getDirections()}),l(23,"ion-icon",14),c(24," Get Directions "),r()()()),n&2&&(a(4),_(t.center.name),a(2),O("alt",t.center.name),a(7),_(t.center.contact||"No contact available"),a(7),_(t.center.address))},dependencies:[T,y,I,b],styles:[".modal-container[_ngcontent-%COMP%]{background-color:#fff;border-radius:12px;overflow:hidden;width:100%;max-width:350px;margin:0 auto;position:relative;box-shadow:0 4px 12px #0000001a}.close-button[_ngcontent-%COMP%]{position:absolute;top:10px;right:10px;z-index:10}.close-button[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;background:#fff;border-radius:50%}.center-name[_ngcontent-%COMP%]{font-size:18px;font-weight:600;text-align:center;margin:15px 15px 10px;color:#000}.center-image[_ngcontent-%COMP%]{width:100%;height:160px;overflow:hidden}.center-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.info-section[_ngcontent-%COMP%]{padding:10px 15px;border-bottom:1px solid #f0f0f0}.info-section[_ngcontent-%COMP%]:last-of-type{border-bottom:none}.info-label[_ngcontent-%COMP%]{font-size:14px;color:#09f;margin-bottom:5px}.info-value[_ngcontent-%COMP%]{display:flex;align-items:center;font-size:15px;color:#333}.info-value[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{margin-right:8px;font-size:18px;min-width:18px}.info-value.contact[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#333}.info-value.address[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#ff4961}.directions-button[_ngcontent-%COMP%]{padding:10px 15px 15px}.directions-button[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--border-radius: 8px;--background: #0099ff;font-weight:500;margin:0}.directions-button[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{margin-right:5px}"]})}}return o})();function le(o,p){o&1&&(i(0,"ion-text",11)(1,"p"),c(2,"Search by center name, address, or disaster type"),r()())}function de(o,p){o&1&&(i(0,"div",12),l(1,"ion-spinner",13),i(2,"ion-text",14)(3,"p"),c(4,"Loading evacuation centers..."),r()()())}function me(o,p){if(o&1){let e=P();i(0,"div",15),l(1,"ion-icon",16),i(2,"ion-text",17)(3,"p"),c(4),r()(),i(5,"ion-button",18),u("click",function(){x(e);let t=h();return v(t.loadEvacuationCenters())}),l(6,"ion-icon",19),c(7," Try Again "),r()()}if(o&2){let e=h();a(4),_(e.errorMessage)}}function ge(o,p){if(o&1&&(i(0,"ion-badge",25),c(1),r()),o&2){let e=h(2).$implicit;O("color",e.status==="Active"?"success":"warning"),a(),L(" ",e.status," ")}}function pe(o,p){if(o&1&&(i(0,"p")(1,"ion-badge",23),c(2),r(),f(3,ge,2,2,"ion-badge",24),r()),o&2){let e=h().$implicit;a(2),_(e.disaster_type),a(),g("ngIf",e.status)}}function ue(o,p){if(o&1){let e=P();i(0,"ion-item",21),u("click",function(){let t=x(e).$implicit,d=h(2);return v(d.viewOnMap(t))}),l(1,"ion-icon",22),i(2,"ion-label")(3,"h2"),c(4),r(),i(5,"p"),c(6),r(),f(7,pe,4,2,"p",8),r()()}if(o&2){let e=p.$implicit;a(4),_(e.name),a(2),_(e.address),a(),g("ngIf",e.disaster_type)}}function he(o,p){if(o&1&&(i(0,"ion-list"),f(1,ue,8,3,"ion-item",20),r()),o&2){let e=h();a(),g("ngForOf",e.locations)}}function _e(o,p){if(o&1&&(i(0,"div",26),l(1,"ion-icon",27),i(2,"ion-text",14)(3,"p"),c(4),r()()()),o&2){let e=h();a(4),L('No evacuation centers found matching "',e.searchQuery,'"')}}function fe(o,p){if(o&1){let e=P();i(0,"div",28),l(1,"ion-icon",29),i(2,"ion-text",14)(3,"h3"),c(4,"Search for Evacuation Centers"),r(),i(5,"p"),c(6,"Enter a name, address, or disaster type to find evacuation centers"),r()(),i(7,"ion-button",30),u("click",function(){x(e);let t=h();return t.searchQuery="all",v(t.onSearch({target:{value:"all"}}))}),c(8," Show All Centers "),r()()}}var Te=(()=>{class o{constructor(e,n,t,d,s){this.http=e,this.loadingService=n,this.toastCtrl=t,this.router=d,this.modalCtrl=s,this.searchQuery="",this.locations=[],this.allCenters=[],this.isLoading=!1,this.hasError=!1,this.errorMessage=""}ngOnInit(){this.loadEvacuationCenters()}loadEvacuationCenters(){return k(this,null,function*(){yield this.loadingService.showLoading("Loading evacuation centers..."),this.isLoading=!0;try{this.http.get(`${ne.apiUrl}/evacuation-centers`).subscribe({next:e=>{console.log("Loaded evacuation centers:",e),this.allCenters=e||[],this.isLoading=!1,this.loadingService.dismissLoading()},error:e=>{console.error("Error loading evacuation centers:",e),this.hasError=!0,this.errorMessage="Failed to load evacuation centers. Please try again later.",this.isLoading=!1,this.loadingService.dismissLoading(),this.toastCtrl.create({message:"Failed to load evacuation centers. Please try again later.",duration:3e3,color:"danger"}).then(n=>n.present())}})}catch(e){console.error("Exception loading evacuation centers:",e),this.hasError=!0,this.errorMessage="An unexpected error occurred. Please try again later.",this.isLoading=!1,this.loadingService.dismissLoading()}})}onSearch(e){let n=e.target.value.toLowerCase().trim();if(console.log("Searching for:",n),!n){this.locations=[];return}this.locations=this.allCenters.filter(t=>{let d=t.name.toLowerCase().startsWith(n),s=t.name.toLowerCase().includes(n),C=t.address?.toLowerCase().includes(n),re=t.disaster_type?.toLowerCase().includes(n);return d||s||C||re}),this.locations.sort((t,d)=>{let s=t.name.toLowerCase().startsWith(n),C=d.name.toLowerCase().startsWith(n);return s&&!C?-1:!s&&C?1:0})}clearSearch(){this.searchQuery="",this.locations=[]}refreshCenters(e){this.loadEvacuationCenters().then(()=>{e.target.complete()})}viewOnMap(e){return k(this,null,function*(){yield(yield this.modalCtrl.create({component:oe,componentProps:{center:e},cssClass:"evacuation-center-modal"})).present()})}static{this.\u0275fac=function(n){return new(n||o)(m(W),m(ie),m(E),m(S),m(w))}}static{this.\u0275cmp=M({type:o,selectors:[["app-search"]],decls:16,vars:7,consts:[["slot","fixed",3,"ionRefresh"],["pullingIcon","chevron-down-circle-outline","pullingText","Pull to refresh","refreshingSpinner","circles","refreshingText","Refreshing..."],[1,"search-container"],["placeholder","Search evacuation centers by name","animated","true","showCancelButton","focus","debounce","300",3,"ngModelChange","ionInput","ionClear","ngModel"],["color","medium","class","search-hint",4,"ngIf"],[1,"search-results"],["class","loading-container",4,"ngIf"],["class","error-container",4,"ngIf"],[4,"ngIf"],["class","no-results",4,"ngIf"],["class","empty-state",4,"ngIf"],["color","medium",1,"search-hint"],[1,"loading-container"],["name","circles"],["color","medium"],[1,"error-container"],["name","alert-circle-outline","color","danger","size","large"],["color","danger"],["fill","outline","size","small",3,"click"],["name","refresh-outline","slot","start"],["button","","detail","",3,"click",4,"ngFor","ngForOf"],["button","","detail","",3,"click"],["name","location-outline","slot","start","color","primary"],["color","secondary"],[3,"color",4,"ngIf"],[3,"color"],[1,"no-results"],["name","search-outline","color","medium","size","large"],[1,"empty-state"],["name","search","color","primary","size","large"],["fill","outline",3,"click"]],template:function(n,t){n&1&&(i(0,"ion-header")(1,"ion-toolbar")(2,"ion-title"),c(3,"Search Evacuation Centers"),r()()(),i(4,"ion-content")(5,"ion-refresher",0),u("ionRefresh",function(s){return t.refreshCenters(s)}),l(6,"ion-refresher-content",1),r(),i(7,"div",2)(8,"ion-searchbar",3),Q("ngModelChange",function(s){return j(t.searchQuery,s)||(t.searchQuery=s),s}),u("ionInput",function(s){return t.onSearch(s)})("ionClear",function(){return t.clearSearch()}),r(),f(9,le,3,0,"ion-text",4),r(),i(10,"div",5),f(11,de,5,0,"div",6)(12,me,8,1,"div",7)(13,he,2,1,"ion-list",8)(14,_e,5,1,"div",9)(15,fe,9,0,"div",10),r()()),n&2&&(a(8),z("ngModel",t.searchQuery),a(),g("ngIf",!t.searchQuery),a(2),g("ngIf",t.isLoading),a(),g("ngIf",t.hasError),a(),g("ngIf",t.locations.length>0),a(),g("ngIf",t.searchQuery&&t.locations.length===0&&!t.isLoading&&!t.hasError),a(),g("ngIf",!t.searchQuery&&!t.isLoading&&!t.hasError&&t.allCenters.length>0))},dependencies:[T,q,y,B,$,I,G,H,U,J,K,X,Y,Z,ee,te,A,b,V,F,N,D,R],styles:["ion-content[_ngcontent-%COMP%]{--background: #f8f9fa}.search-container[_ngcontent-%COMP%]{padding:10px 16px 0}.search-container[_ngcontent-%COMP%]   ion-searchbar[_ngcontent-%COMP%]{--border-radius: 10px;--box-shadow: 0 2px 6px rgba(0, 0, 0, .1);--placeholder-color: var(--ion-color-medium);--icon-color: var(--ion-color-primary)}.search-container[_ngcontent-%COMP%]   .search-hint[_ngcontent-%COMP%]{font-size:12px;margin:0 0 10px 16px;display:block}.search-results[_ngcontent-%COMP%]{padding:0 16px 16px}.loading-container[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%], .no-results[_ngcontent-%COMP%], .empty-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;text-align:center;padding:32px 16px;min-height:200px}.loading-container[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%], .no-results[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%], .empty-state[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:48px;margin-bottom:16px}.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .no-results[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:8px 0;font-size:16px}.loading-container[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%], .no-results[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%], .empty-state[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{margin-top:16px}ion-list[_ngcontent-%COMP%]{background:transparent;padding:0}ion-item[_ngcontent-%COMP%]{--padding-start: 16px;--inner-padding-end: 16px;--background: white;margin-bottom:10px;border-radius:10px;--border-radius: 10px;box-shadow:0 2px 4px #0000000d}ion-item[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:16px;font-weight:500;margin-bottom:4px;color:var(--ion-color-dark)}ion-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:14px;color:var(--ion-color-medium);margin:2px 0}ion-item[_ngcontent-%COMP%]   ion-badge[_ngcontent-%COMP%]{margin-right:6px;padding:4px 8px;border-radius:4px}.empty-state[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:18px;font-weight:500;margin:8px 0;color:var(--ion-color-dark)}.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-bottom:20px}"]})}}return o})();export{Te as SearchPage};
