<?php

require __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== FCM Token Database Check ===\n";

// Check total tokens
$totalTokens = App\Models\DeviceToken::count();
echo "Total Device Tokens: {$totalTokens}\n";

// Check active tokens
$activeTokens = App\Models\DeviceToken::where('is_active', true)->count();
echo "Active Device Tokens: {$activeTokens}\n";

// Check tokens by project
$projectCounts = App\Models\DeviceToken::selectRaw('project_id, COUNT(*) as count')
    ->groupBy('project_id')
    ->get();

echo "\nTokens by Project:\n";
foreach ($projectCounts as $project) {
    echo "  Project: " . ($project->project_id ?: 'NULL') . " - Count: {$project->count}\n";
}

// Show recent tokens
echo "\nRecent Tokens (last 5):\n";
$recentTokens = App\Models\DeviceToken::latest()->take(5)->get();

if ($recentTokens->count() > 0) {
    foreach ($recentTokens as $token) {
        echo "  ID: {$token->id}\n";
        echo "  Device Type: {$token->device_type}\n";
        echo "  Project ID: " . ($token->project_id ?: 'NULL') . "\n";
        echo "  Active: " . ($token->is_active ? 'Yes' : 'No') . "\n";
        echo "  Token: " . substr($token->token, 0, 20) . "...\n";
        echo "  Created: {$token->created_at}\n";
        echo "  ---\n";
    }
} else {
    echo "  No tokens found.\n";
}

echo "\n=== Environment Check ===\n";
echo "Firebase Project ID (env): " . env('FIREBASE_PROJECT_ID', 'NOT SET') . "\n";
echo "Firebase API Key (env): " . (env('FIREBASE_API_KEY') ? 'SET' : 'NOT SET') . "\n";

// Check service account file
$serviceAccountPath = storage_path('firebase-service-account.json');
echo "Service Account File: " . ($serviceAccountPath && file_exists($serviceAccountPath) ? 'EXISTS' : 'MISSING') . "\n";

if (file_exists($serviceAccountPath)) {
    $serviceAccount = json_decode(file_get_contents($serviceAccountPath), true);
    if ($serviceAccount && isset($serviceAccount['project_id'])) {
        echo "Service Account Project ID: {$serviceAccount['project_id']}\n";
    }
}

echo "\nDone.\n";
