{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm-es5/md.transition-30ce8d1b.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { c as createAnimation } from \"./animation-8b25e105.js\";\nimport { g as getIonPageElement } from \"./index-68c0d151.js\";\nimport \"./index-cfd9c1f2.js\";\nimport \"./index-a5d50daf.js\";\nimport \"./index-527b9e34.js\";\nimport \"./helpers-d94bc8ad.js\";\nvar mdTransitionAnimation = function (i, a) {\n  var n, e, t;\n  var r = \"40px\";\n  var o = \"0px\";\n  var m = a.direction === \"back\";\n  var c = a.enteringEl;\n  var s = a.leavingEl;\n  var l = getIonPageElement(c);\n  var d = l.querySelector(\"ion-toolbar\");\n  var v = createAnimation();\n  v.addElement(l).fill(\"both\").beforeRemoveClass(\"ion-page-invisible\");\n  if (m) {\n    v.duration(((n = a.duration) !== null && n !== void 0 ? n : 0) || 200).easing(\"cubic-bezier(0.47,0,0.745,0.715)\");\n  } else {\n    v.duration(((e = a.duration) !== null && e !== void 0 ? e : 0) || 280).easing(\"cubic-bezier(0.36,0.66,0.04,1)\").fromTo(\"transform\", \"translateY(\".concat(r, \")\"), \"translateY(\".concat(o, \")\")).fromTo(\"opacity\", .01, 1);\n  }\n  if (d) {\n    var p = createAnimation();\n    p.addElement(d);\n    v.addAnimation(p);\n  }\n  if (s && m) {\n    v.duration(((t = a.duration) !== null && t !== void 0 ? t : 0) || 200).easing(\"cubic-bezier(0.47,0,0.745,0.715)\");\n    var b = createAnimation();\n    b.addElement(getIonPageElement(s)).onFinish(function (i) {\n      if (i === 1 && b.elements.length > 0) {\n        b.elements[0].style.setProperty(\"display\", \"none\");\n      }\n    }).fromTo(\"transform\", \"translateY(\".concat(o, \")\"), \"translateY(\".concat(r, \")\")).fromTo(\"opacity\", 1, 0);\n    v.addAnimation(b);\n  }\n  return v;\n};\nexport { mdTransitionAnimation };"], "mappings": ";;;;;;AASA,IAAI,wBAAwB,SAAU,GAAG,GAAG;AAC1C,MAAI,GAAG,GAAG;AACV,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI,EAAE,cAAc;AACxB,MAAI,IAAI,EAAE;AACV,MAAI,IAAI,EAAE;AACV,MAAI,IAAI,kBAAkB,CAAC;AAC3B,MAAI,IAAI,EAAE,cAAc,aAAa;AACrC,MAAI,IAAI,gBAAgB;AACxB,IAAE,WAAW,CAAC,EAAE,KAAK,MAAM,EAAE,kBAAkB,oBAAoB;AACnE,MAAI,GAAG;AACL,MAAE,WAAW,IAAI,EAAE,cAAc,QAAQ,MAAM,SAAS,IAAI,MAAM,GAAG,EAAE,OAAO,kCAAkC;AAAA,EAClH,OAAO;AACL,MAAE,WAAW,IAAI,EAAE,cAAc,QAAQ,MAAM,SAAS,IAAI,MAAM,GAAG,EAAE,OAAO,gCAAgC,EAAE,OAAO,aAAa,cAAc,OAAO,GAAG,GAAG,GAAG,cAAc,OAAO,GAAG,GAAG,CAAC,EAAE,OAAO,WAAW,MAAK,CAAC;AAAA,EAC1N;AACA,MAAI,GAAG;AACL,QAAI,IAAI,gBAAgB;AACxB,MAAE,WAAW,CAAC;AACd,MAAE,aAAa,CAAC;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,MAAE,WAAW,IAAI,EAAE,cAAc,QAAQ,MAAM,SAAS,IAAI,MAAM,GAAG,EAAE,OAAO,kCAAkC;AAChH,QAAI,IAAI,gBAAgB;AACxB,MAAE,WAAW,kBAAkB,CAAC,CAAC,EAAE,SAAS,SAAUA,IAAG;AACvD,UAAIA,OAAM,KAAK,EAAE,SAAS,SAAS,GAAG;AACpC,UAAE,SAAS,CAAC,EAAE,MAAM,YAAY,WAAW,MAAM;AAAA,MACnD;AAAA,IACF,CAAC,EAAE,OAAO,aAAa,cAAc,OAAO,GAAG,GAAG,GAAG,cAAc,OAAO,GAAG,GAAG,CAAC,EAAE,OAAO,WAAW,GAAG,CAAC;AACzG,MAAE,aAAa,CAAC;AAAA,EAClB;AACA,SAAO;AACT;", "names": ["i"]}