{"version": 3, "sources": ["../../../../../../node_modules/@capacitor/geolocation/dist/esm/web.js"], "sourcesContent": ["import { WebPlugin } from '@capacitor/core';\nexport class GeolocationWeb extends WebPlugin {\n  async getCurrentPosition(options) {\n    return new Promise((resolve, reject) => {\n      navigator.geolocation.getCurrentPosition(pos => {\n        resolve(pos);\n      }, err => {\n        reject(err);\n      }, Object.assign({\n        enableHighAccuracy: false,\n        timeout: 10000,\n        maximumAge: 0\n      }, options));\n    });\n  }\n  async watchPosition(options, callback) {\n    const id = navigator.geolocation.watchPosition(pos => {\n      callback(pos);\n    }, err => {\n      callback(null, err);\n    }, Object.assign({\n      enableHighAccuracy: false,\n      timeout: 10000,\n      maximumAge: 0,\n      minimumUpdateInterval: 5000\n    }, options));\n    return `${id}`;\n  }\n  async clearWatch(options) {\n    navigator.geolocation.clearWatch(parseInt(options.id, 10));\n  }\n  async checkPermissions() {\n    if (typeof navigator === 'undefined' || !navigator.permissions) {\n      throw this.unavailable('Permissions API not available in this browser');\n    }\n    const permission = await navigator.permissions.query({\n      name: 'geolocation'\n    });\n    return {\n      location: permission.state,\n      coarseLocation: permission.state\n    };\n  }\n  async requestPermissions() {\n    throw this.unimplemented('Not implemented on web.');\n  }\n}\nconst Geolocation = new GeolocationWeb();\nexport { Geolocation };\n"], "mappings": ";;;;;;;;AACO,IAAM,iBAAN,cAA6B,UAAU;AAAA,EACtC,mBAAmB,SAAS;AAAA;AAChC,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,kBAAU,YAAY,mBAAmB,SAAO;AAC9C,kBAAQ,GAAG;AAAA,QACb,GAAG,SAAO;AACR,iBAAO,GAAG;AAAA,QACZ,GAAG,OAAO,OAAO;AAAA,UACf,oBAAoB;AAAA,UACpB,SAAS;AAAA,UACT,YAAY;AAAA,QACd,GAAG,OAAO,CAAC;AAAA,MACb,CAAC;AAAA,IACH;AAAA;AAAA,EACM,cAAc,SAAS,UAAU;AAAA;AACrC,YAAM,KAAK,UAAU,YAAY,cAAc,SAAO;AACpD,iBAAS,GAAG;AAAA,MACd,GAAG,SAAO;AACR,iBAAS,MAAM,GAAG;AAAA,MACpB,GAAG,OAAO,OAAO;AAAA,QACf,oBAAoB;AAAA,QACpB,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,uBAAuB;AAAA,MACzB,GAAG,OAAO,CAAC;AACX,aAAO,GAAG,EAAE;AAAA,IACd;AAAA;AAAA,EACM,WAAW,SAAS;AAAA;AACxB,gBAAU,YAAY,WAAW,SAAS,QAAQ,IAAI,EAAE,CAAC;AAAA,IAC3D;AAAA;AAAA,EACM,mBAAmB;AAAA;AACvB,UAAI,OAAO,cAAc,eAAe,CAAC,UAAU,aAAa;AAC9D,cAAM,KAAK,YAAY,+CAA+C;AAAA,MACxE;AACA,YAAM,aAAa,MAAM,UAAU,YAAY,MAAM;AAAA,QACnD,MAAM;AAAA,MACR,CAAC;AACD,aAAO;AAAA,QACL,UAAU,WAAW;AAAA,QACrB,gBAAgB,WAAW;AAAA,MAC7B;AAAA,IACF;AAAA;AAAA,EACM,qBAAqB;AAAA;AACzB,YAAM,KAAK,cAAc,yBAAyB;AAAA,IACpD;AAAA;AACF;AACA,IAAM,cAAc,IAAI,eAAe;", "names": []}