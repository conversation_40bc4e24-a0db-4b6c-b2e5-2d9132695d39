<?php

/**
 * This script sends a test notification using the FCM API v1 (HTTP v1)
 * Run it with: php test-fcm-notification.php <fcm_token>
 */

// Load the Laravel application
require __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

// Get the FCM service
$fcmService = app(App\Services\FCMService::class);

// Get the FCM token from the command line or use a default
$token = $argv[1] ?? null;

if (!$token) {
    echo "Usage: php test-fcm-notification.php <fcm_token>\n";
    echo "FCM token not provided. Checking database for tokens...\n";

    // Get the most recent token from the database using Laravel's DB facade
    try {
        $deviceToken = App\Models\DeviceToken::where('is_active', true)
            ->orderBy('created_at', 'desc')
            ->first();

        if ($deviceToken) {
            $token = $deviceToken->token;
            echo "Using token from database: " . substr($token, 0, 10) . "...\n";
        } else {
            echo "No active tokens found in the database.\n";
            exit(1);
        }
    } catch (Exception $e) {
        echo "Database query failed: " . $e->getMessage() . "\n";
        exit(1);
    }
}

// Verify the Firebase service account file
$serviceAccountPath = storage_path('firebase-service-account.json');
echo "Checking Firebase service account file...\n";

if (!file_exists($serviceAccountPath)) {
    echo "ERROR: Firebase service account file not found at: {$serviceAccountPath}\n";
    exit(1);
}

echo "Firebase service account file exists.\n";

// Parse the service account file to verify its contents
try {
    $serviceAccount = json_decode(file_get_contents($serviceAccountPath), true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        echo "ERROR: Firebase service account file is not valid JSON: " . json_last_error_msg() . "\n";
        exit(1);
    }

    // Check required fields
    $requiredFields = ['type', 'project_id', 'private_key_id', 'private_key', 'client_email'];
    $missingFields = [];

    foreach ($requiredFields as $field) {
        if (!isset($serviceAccount[$field]) || empty($serviceAccount[$field])) {
            $missingFields[] = $field;
        }
    }

    if (!empty($missingFields)) {
        echo "ERROR: Firebase service account file is missing required fields: " . implode(', ', $missingFields) . "\n";
        exit(1);
    }

    echo "Firebase service account file is valid.\n";
    echo "Project ID: " . $serviceAccount['project_id'] . "\n";

} catch (Exception $e) {
    echo "ERROR: Failed to parse Firebase service account file: " . $e->getMessage() . "\n";
    exit(1);
}

// Create a test notification
echo "Creating test notification...\n";

$notification = new App\Models\Notification([
    'title' => 'Test Notification ' . date('H:i:s'),
    'message' => 'This is a test notification sent at ' . date('Y-m-d H:i:s'),
    'category' => 'General',
    'severity' => 'high',
    'sent' => false
]);

$notification->save();

echo "Test notification created with ID: " . $notification->id . "\n";

// Send the notification
echo "Sending notification to token: " . substr($token, 0, 10) . "...\n";

try {
    // Try to send the notification using the FCM service
    $result = $fcmService->send($token, [
        'title' => $notification->title,
        'message' => $notification->message,
        'data' => [
            'notification_id' => $notification->id,
            'category' => $notification->category,
            'severity' => $notification->severity,
            'time' => date('Y-m-d H:i:s')
        ]
    ]);

    echo "SUCCESS: Notification sent!\n";
    echo "Message ID: " . (is_array($result) ? json_encode($result) : $result) . "\n";

    // Update notification status
    $notification->sent = true;
    $notification->save();

} catch (Exception $e) {
    echo "ERROR: Failed to send notification: " . $e->getMessage() . "\n";

    // Check for common errors
    if (strpos($e->getMessage(), 'The caller does not have permission') !== false) {
        echo "This is likely a permission issue with your Firebase service account.\n";
        echo "Make sure the service account has the Firebase Admin SDK Admin role.\n";
    }

    if (strpos($e->getMessage(), 'Invalid registration') !== false) {
        echo "The FCM token is invalid or not registered with Firebase.\n";
        echo "Make sure the token is from the same Firebase project as your service account.\n";
    }

    if (strpos($e->getMessage(), 'not a valid FCM') !== false) {
        echo "The provided string is not a valid FCM token.\n";
        echo "Make sure you're using the correct token from your app.\n";
    }

    // Log the error
    \Illuminate\Support\Facades\Log::error("FCM Test Error", [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
}

echo "\nTest completed.\n";
