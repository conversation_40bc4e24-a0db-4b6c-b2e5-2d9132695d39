<?php

require __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== FCM Token Registration Monitor ===\n";
echo "Monitoring for new FCM token registrations...\n";
echo "Press Ctrl+C to stop monitoring.\n\n";

$lastCount = 0;
$lastTokenId = 0;

// Get initial state
$initialCount = App\Models\DeviceToken::count();
$lastToken = App\Models\DeviceToken::latest()->first();
if ($lastToken) {
    $lastTokenId = $lastToken->id;
}

echo "Initial token count: {$initialCount}\n";
if ($lastToken) {
    echo "Latest token ID: {$lastTokenId}\n";
}
echo "Waiting for new registrations...\n\n";

while (true) {
    try {
        // Check for new tokens
        $currentCount = App\Models\DeviceToken::count();
        
        if ($currentCount > $lastCount) {
            echo "[" . date('Y-m-d H:i:s') . "] NEW TOKEN REGISTERED!\n";
            
            // Get the new tokens
            $newTokens = App\Models\DeviceToken::where('id', '>', $lastTokenId)
                ->orderBy('id', 'desc')
                ->get();
            
            foreach ($newTokens as $token) {
                echo "  Token ID: {$token->id}\n";
                echo "  Device Type: {$token->device_type}\n";
                echo "  Project ID: " . ($token->project_id ?: 'NULL') . "\n";
                echo "  User ID: " . ($token->user_id ?: 'NULL') . "\n";
                echo "  Active: " . ($token->is_active ? 'Yes' : 'No') . "\n";
                echo "  Token: " . substr($token->token, 0, 30) . "...\n";
                echo "  Created: {$token->created_at}\n";
                echo "  ---\n";
                
                $lastTokenId = max($lastTokenId, $token->id);
            }
            
            $lastCount = $currentCount;
            echo "Total tokens now: {$currentCount}\n\n";
        }
        
        // Check for token updates (like user_id associations)
        $recentlyUpdated = App\Models\DeviceToken::where('updated_at', '>', now()->subMinutes(1))
            ->where('updated_at', '>', 'created_at')
            ->get();
            
        foreach ($recentlyUpdated as $token) {
            echo "[" . date('Y-m-d H:i:s') . "] TOKEN UPDATED!\n";
            echo "  Token ID: {$token->id}\n";
            echo "  User ID: " . ($token->user_id ?: 'NULL') . "\n";
            echo "  Updated: {$token->updated_at}\n";
            echo "  ---\n";
        }
        
        // Sleep for 2 seconds before checking again
        sleep(2);
        
    } catch (Exception $e) {
        echo "Error: " . $e->getMessage() . "\n";
        sleep(5);
    }
}
