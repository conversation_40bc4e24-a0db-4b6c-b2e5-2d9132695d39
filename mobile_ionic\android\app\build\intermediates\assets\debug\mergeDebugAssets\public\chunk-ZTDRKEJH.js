import{a as mi}from"./chunk-NX7QWPJF.js";import{a as Fi}from"./chunk-P7IGRJGJ.js";import{a as pA,b as $t,c as jB}from"./chunk-LEWYLK2X.js";import{a as Ui}from"./chunk-EOTJZDZP.js";import{a as ee}from"./chunk-FULEFYAM.js";import"./chunk-AGHLTJ5J.js";import{$ as Xt,$a as fi,A as me,Bb as Yt,C as wA,Cb as je,D as k,Db as be,E as Ai,F as v,G as M,H as P,I as Ae,J as nA,K as eA,L as qe,M as V,N as IA,Na as He,O as yA,Oa as ni,P as ei,Pa as oi,Qa as ii,Ra as ai,Ta as si,V as Vt,Va as ci,W as ve,Wa as Jt,X as Ee,Xa as li,Ya as Bi,Za as gi,_a as ui,ab as Ie,ca as ti,cb as Wt,fb as ye,g as Ze,gb as wi,hb as di,oa as ri,p as LA,q as GA,qb as hi,r as RA,u as qo,ub as Ci,vb as Qi,w as jo,y as K,z as ze,zb as pi}from"./chunk-QCXYQNJC.js";import"./chunk-6WVAEWPV.js";import"./chunk-HYNAH5QB.js";import"./chunk-5AIHQZWU.js";import"./chunk-4PQ5B4D2.js";import"./chunk-HC6MZPB3.js";import"./chunk-SV2ZKNWA.js";import"./chunk-EPGIQT2W.js";import"./chunk-RS5W3JWO.js";import"./chunk-DY4KE6AI.js";import"./chunk-GIGBYVJT.js";import"./chunk-KGEDUKSE.js";import"./chunk-MCRJI3T3.js";import"./chunk-OBBPMR2I.js";import"./chunk-AMQPVFGX.js";import"./chunk-KKCAABTQ.js";import"./chunk-OFX7WKKZ.js";import"./chunk-F4H6ZFEG.js";import"./chunk-NMYJD6OP.js";import"./chunk-XXJXE6HG.js";import"./chunk-SV7S5NYR.js";import"./chunk-WTCPO44B.js";import"./chunk-QVY4QQUF.js";import"./chunk-2HRRFJKF.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-BAKMWPBW.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import{e as qB,f as zo,g as X}from"./chunk-2R6CW7ES.js";var bi=qB((Wr,Yr)=>{"use strict";(function(m,R){typeof Wr=="object"&&typeof Yr<"u"?Yr.exports=R():typeof define=="function"&&define.amd?define(R):(m=typeof globalThis<"u"?globalThis:m||self,m.html2canvas=R())})(Wr,function(){"use strict";var m=function(e,A){return m=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,t){r.__proto__=t}||function(r,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(r[n]=t[n])},m(e,A)};function R(e,A){if(typeof A!="function"&&A!==null)throw new TypeError("Class extends value "+String(A)+" is not a constructor or null");m(e,A);function r(){this.constructor=e}e.prototype=A===null?Object.create(A):(r.prototype=A.prototype,new r)}var i=function(){return i=Object.assign||function(A){for(var r,t=1,n=arguments.length;t<n;t++){r=arguments[t];for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(A[o]=r[o])}return A},i.apply(this,arguments)};function l(e,A,r,t){function n(o){return o instanceof r?o:new r(function(a){a(o)})}return new(r||(r=Promise))(function(o,a){function c(f){try{u(t.next(f))}catch(w){a(w)}}function s(f){try{u(t.throw(f))}catch(w){a(w)}}function u(f){f.done?o(f.value):n(f.value).then(c,s)}u((t=t.apply(e,A||[])).next())})}function B(e,A){var r={label:0,sent:function(){if(o[0]&1)throw o[1];return o[1]},trys:[],ops:[]},t,n,o,a;return a={next:c(0),throw:c(1),return:c(2)},typeof Symbol=="function"&&(a[Symbol.iterator]=function(){return this}),a;function c(u){return function(f){return s([u,f])}}function s(u){if(t)throw new TypeError("Generator is already executing.");for(;r;)try{if(t=1,n&&(o=u[0]&2?n.return:u[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,u[1])).done)return o;switch(n=0,o&&(u=[u[0]&2,o.value]),u[0]){case 0:case 1:o=u;break;case 4:return r.label++,{value:u[1],done:!1};case 5:r.label++,n=u[1],u=[0];continue;case 7:u=r.ops.pop(),r.trys.pop();continue;default:if(o=r.trys,!(o=o.length>0&&o[o.length-1])&&(u[0]===6||u[0]===2)){r=0;continue}if(u[0]===3&&(!o||u[1]>o[0]&&u[1]<o[3])){r.label=u[1];break}if(u[0]===6&&r.label<o[1]){r.label=o[1],o=u;break}if(o&&r.label<o[2]){r.label=o[2],r.ops.push(u);break}o[2]&&r.ops.pop(),r.trys.pop();continue}u=A.call(e,r)}catch(f){u=[6,f],n=0}finally{t=o=0}if(u[0]&5)throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}function g(e,A,r){if(r||arguments.length===2)for(var t=0,n=A.length,o;t<n;t++)(o||!(t in A))&&(o||(o=Array.prototype.slice.call(A,0,t)),o[t]=A[t]);return e.concat(o||A)}for(var Q=function(){function e(A,r,t,n){this.left=A,this.top=r,this.width=t,this.height=n}return e.prototype.add=function(A,r,t,n){return new e(this.left+A,this.top+r,this.width+t,this.height+n)},e.fromClientRect=function(A,r){return new e(r.left+A.windowBounds.left,r.top+A.windowBounds.top,r.width,r.height)},e.fromDOMRectList=function(A,r){var t=Array.from(r).find(function(n){return n.width!==0});return t?new e(t.left+A.windowBounds.left,t.top+A.windowBounds.top,t.width,t.height):e.EMPTY},e.EMPTY=new e(0,0,0,0),e}(),h=function(e,A){return Q.fromClientRect(e,A.getBoundingClientRect())},F=function(e){var A=e.body,r=e.documentElement;if(!A||!r)throw new Error("Unable to get document size");var t=Math.max(Math.max(A.scrollWidth,r.scrollWidth),Math.max(A.offsetWidth,r.offsetWidth),Math.max(A.clientWidth,r.clientWidth)),n=Math.max(Math.max(A.scrollHeight,r.scrollHeight),Math.max(A.offsetHeight,r.offsetHeight),Math.max(A.clientHeight,r.clientHeight));return new Q(0,0,t,n)},d=function(e){for(var A=[],r=0,t=e.length;r<t;){var n=e.charCodeAt(r++);if(n>=55296&&n<=56319&&r<t){var o=e.charCodeAt(r++);(o&64512)===56320?A.push(((n&1023)<<10)+(o&1023)+65536):(A.push(n),r--)}else A.push(n)}return A},p=function(){for(var e=[],A=0;A<arguments.length;A++)e[A]=arguments[A];if(String.fromCodePoint)return String.fromCodePoint.apply(String,e);var r=e.length;if(!r)return"";for(var t=[],n=-1,o="";++n<r;){var a=e[n];a<=65535?t.push(a):(a-=65536,t.push((a>>10)+55296,a%1024+56320)),(n+1===r||t.length>16384)&&(o+=String.fromCharCode.apply(String,t),t.length=0)}return o},T="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",D=typeof Uint8Array>"u"?[]:new Uint8Array(256),W=0;W<T.length;W++)D[T.charCodeAt(W)]=W;for(var z="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",xA=typeof Uint8Array>"u"?[]:new Uint8Array(256),fA=0;fA<z.length;fA++)xA[z.charCodeAt(fA)]=fA;for(var Zt=function(e){var A=e.length*.75,r=e.length,t,n=0,o,a,c,s;e[e.length-1]==="="&&(A--,e[e.length-2]==="="&&A--);var u=typeof ArrayBuffer<"u"&&typeof Uint8Array<"u"&&typeof Uint8Array.prototype.slice<"u"?new ArrayBuffer(A):new Array(A),f=Array.isArray(u)?u:new Uint8Array(u);for(t=0;t<r;t+=4)o=xA[e.charCodeAt(t)],a=xA[e.charCodeAt(t+1)],c=xA[e.charCodeAt(t+2)],s=xA[e.charCodeAt(t+3)],f[n++]=o<<2|a>>4,f[n++]=(a&15)<<4|c>>2,f[n++]=(c&3)<<6|s&63;return u},At=function(e){for(var A=e.length,r=[],t=0;t<A;t+=2)r.push(e[t+1]<<8|e[t]);return r},xi=function(e){for(var A=e.length,r=[],t=0;t<A;t+=4)r.push(e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]);return r},te=5,zt=11,qt=2,Mi=zt-te,$r=65536>>te,Si=1<<te,jt=Si-1,Ki=1024>>te,Di=$r+Ki,Ti=Di,Oi=32,_i=Ti+Oi,Pi=65536>>zt,Ni=1<<Mi,Gi=Ni-1,Zr=function(e,A,r){return e.slice?e.slice(A,r):new Uint16Array(Array.prototype.slice.call(e,A,r))},Ri=function(e,A,r){return e.slice?e.slice(A,r):new Uint32Array(Array.prototype.slice.call(e,A,r))},ki=function(e,A){var r=Zt(e),t=Array.isArray(r)?xi(r):new Uint32Array(r),n=Array.isArray(r)?At(r):new Uint16Array(r),o=24,a=Zr(n,o/2,t[4]/2),c=t[5]===2?Zr(n,(o+t[4])/2):Ri(t,Math.ceil((o+t[4])/4));return new Vi(t[0],t[1],t[2],t[3],a,c)},Vi=function(){function e(A,r,t,n,o,a){this.initialValue=A,this.errorValue=r,this.highStart=t,this.highValueIndex=n,this.index=o,this.data=a}return e.prototype.get=function(A){var r;if(A>=0){if(A<55296||A>56319&&A<=65535)return r=this.index[A>>te],r=(r<<qt)+(A&jt),this.data[r];if(A<=65535)return r=this.index[$r+(A-55296>>te)],r=(r<<qt)+(A&jt),this.data[r];if(A<this.highStart)return r=_i-Pi+(A>>zt),r=this.index[r],r+=A>>te&Gi,r=this.index[r],r=(r<<qt)+(A&jt),this.data[r];if(A<=1114111)return this.data[this.highValueIndex]}return this.errorValue},e}(),zr="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Xi=typeof Uint8Array>"u"?[]:new Uint8Array(256),et=0;et<zr.length;et++)Xi[zr.charCodeAt(et)]=et;var Ji="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",qr=50,Wi=1,jr=2,An=3,Yi=4,$i=5,en=7,tn=8,rn=9,kA=10,Ar=11,nn=12,er=13,Zi=14,Le=15,tr=16,tt=17,xe=18,zi=19,on=20,rr=21,Me=22,nr=23,Be=24,UA=25,Se=26,Ke=27,ge=28,qi=29,re=30,ji=31,rt=32,nt=33,or=34,ir=35,ar=36,De=37,sr=38,ot=39,it=40,cr=41,an=42,Aa=43,ea=[9001,65288],sn="!",J="\xD7",at="\xF7",lr=ki(Ji),TA=[re,ar],Br=[Wi,jr,An,$i],cn=[kA,tn],ln=[Ke,Se],ta=Br.concat(cn),Bn=[sr,ot,it,or,ir],ra=[Le,er],na=function(e,A){A===void 0&&(A="strict");var r=[],t=[],n=[];return e.forEach(function(o,a){var c=lr.get(o);if(c>qr?(n.push(!0),c-=qr):n.push(!1),["normal","auto","loose"].indexOf(A)!==-1&&[8208,8211,12316,12448].indexOf(o)!==-1)return t.push(a),r.push(tr);if(c===Yi||c===Ar){if(a===0)return t.push(a),r.push(re);var s=r[a-1];return ta.indexOf(s)===-1?(t.push(t[a-1]),r.push(s)):(t.push(a),r.push(re))}if(t.push(a),c===ji)return r.push(A==="strict"?rr:De);if(c===an||c===qi)return r.push(re);if(c===Aa)return o>=131072&&o<=196605||o>=196608&&o<=262141?r.push(De):r.push(re);r.push(c)}),[t,r,n]},gr=function(e,A,r,t){var n=t[r];if(Array.isArray(e)?e.indexOf(n)!==-1:e===n)for(var o=r;o<=t.length;){o++;var a=t[o];if(a===A)return!0;if(a!==kA)break}if(n===kA)for(var o=r;o>0;){o--;var c=t[o];if(Array.isArray(e)?e.indexOf(c)!==-1:e===c)for(var s=r;s<=t.length;){s++;var a=t[s];if(a===A)return!0;if(a!==kA)break}if(c!==kA)break}return!1},gn=function(e,A){for(var r=e;r>=0;){var t=A[r];if(t===kA)r--;else return t}return 0},oa=function(e,A,r,t,n){if(r[t]===0)return J;var o=t-1;if(Array.isArray(n)&&n[o]===!0)return J;var a=o-1,c=o+1,s=A[o],u=a>=0?A[a]:0,f=A[c];if(s===jr&&f===An)return J;if(Br.indexOf(s)!==-1)return sn;if(Br.indexOf(f)!==-1||cn.indexOf(f)!==-1)return J;if(gn(o,A)===tn)return at;if(lr.get(e[o])===Ar||(s===rt||s===nt)&&lr.get(e[c])===Ar||s===en||f===en||s===rn||[kA,er,Le].indexOf(s)===-1&&f===rn||[tt,xe,zi,Be,ge].indexOf(f)!==-1||gn(o,A)===Me||gr(nr,Me,o,A)||gr([tt,xe],rr,o,A)||gr(nn,nn,o,A))return J;if(s===kA)return at;if(s===nr||f===nr)return J;if(f===tr||s===tr)return at;if([er,Le,rr].indexOf(f)!==-1||s===Zi||u===ar&&ra.indexOf(s)!==-1||s===ge&&f===ar||f===on||TA.indexOf(f)!==-1&&s===UA||TA.indexOf(s)!==-1&&f===UA||s===Ke&&[De,rt,nt].indexOf(f)!==-1||[De,rt,nt].indexOf(s)!==-1&&f===Se||TA.indexOf(s)!==-1&&ln.indexOf(f)!==-1||ln.indexOf(s)!==-1&&TA.indexOf(f)!==-1||[Ke,Se].indexOf(s)!==-1&&(f===UA||[Me,Le].indexOf(f)!==-1&&A[c+1]===UA)||[Me,Le].indexOf(s)!==-1&&f===UA||s===UA&&[UA,ge,Be].indexOf(f)!==-1)return J;if([UA,ge,Be,tt,xe].indexOf(f)!==-1)for(var w=o;w>=0;){var C=A[w];if(C===UA)return J;if([ge,Be].indexOf(C)!==-1)w--;else break}if([Ke,Se].indexOf(f)!==-1)for(var w=[tt,xe].indexOf(s)!==-1?a:o;w>=0;){var C=A[w];if(C===UA)return J;if([ge,Be].indexOf(C)!==-1)w--;else break}if(sr===s&&[sr,ot,or,ir].indexOf(f)!==-1||[ot,or].indexOf(s)!==-1&&[ot,it].indexOf(f)!==-1||[it,ir].indexOf(s)!==-1&&f===it||Bn.indexOf(s)!==-1&&[on,Se].indexOf(f)!==-1||Bn.indexOf(f)!==-1&&s===Ke||TA.indexOf(s)!==-1&&TA.indexOf(f)!==-1||s===Be&&TA.indexOf(f)!==-1||TA.concat(UA).indexOf(s)!==-1&&f===Me&&ea.indexOf(e[c])===-1||TA.concat(UA).indexOf(f)!==-1&&s===xe)return J;if(s===cr&&f===cr){for(var b=r[o],U=1;b>0&&(b--,A[b]===cr);)U++;if(U%2!==0)return J}return s===rt&&f===nt?J:at},ia=function(e,A){A||(A={lineBreak:"normal",wordBreak:"normal"});var r=na(e,A.lineBreak),t=r[0],n=r[1],o=r[2];(A.wordBreak==="break-all"||A.wordBreak==="break-word")&&(n=n.map(function(c){return[UA,re,an].indexOf(c)!==-1?De:c}));var a=A.wordBreak==="keep-all"?o.map(function(c,s){return c&&e[s]>=19968&&e[s]<=40959}):void 0;return[t,n,a]},aa=function(){function e(A,r,t,n){this.codePoints=A,this.required=r===sn,this.start=t,this.end=n}return e.prototype.slice=function(){return p.apply(void 0,this.codePoints.slice(this.start,this.end))},e}(),sa=function(e,A){var r=d(e),t=ia(r,A),n=t[0],o=t[1],a=t[2],c=r.length,s=0,u=0;return{next:function(){if(u>=c)return{done:!0,value:null};for(var f=J;u<c&&(f=oa(r,o,n,++u,a))===J;);if(f!==J||u===c){var w=new aa(r,f,s,u);return s=u,{value:w,done:!1}}return{done:!0,value:null}}}},ca=1,la=2,Te=4,un=8,st=10,fn=47,Oe=92,Ba=9,ga=32,ct=34,_e=61,ua=35,fa=36,wa=37,lt=39,Bt=40,Pe=41,da=95,dA=45,ha=33,Ca=60,Qa=62,pa=64,Ua=91,Fa=93,ma=61,va=123,gt=63,Ea=125,wn=124,Ha=126,Ia=128,dn=65533,ur=42,ne=43,ya=44,ba=58,La=59,Ne=46,xa=0,Ma=8,Sa=11,Ka=14,Da=31,Ta=127,MA=-1,hn=48,Cn=97,Qn=101,Oa=102,_a=117,Pa=122,pn=65,Un=69,Fn=70,Na=85,Ga=90,uA=function(e){return e>=hn&&e<=57},Ra=function(e){return e>=55296&&e<=57343},ue=function(e){return uA(e)||e>=pn&&e<=Fn||e>=Cn&&e<=Oa},ka=function(e){return e>=Cn&&e<=Pa},Va=function(e){return e>=pn&&e<=Ga},Xa=function(e){return ka(e)||Va(e)},Ja=function(e){return e>=Ia},ut=function(e){return e===st||e===Ba||e===ga},ft=function(e){return Xa(e)||Ja(e)||e===da},mn=function(e){return ft(e)||uA(e)||e===dA},Wa=function(e){return e>=xa&&e<=Ma||e===Sa||e>=Ka&&e<=Da||e===Ta},VA=function(e,A){return e!==Oe?!1:A!==st},wt=function(e,A,r){return e===dA?ft(A)||VA(A,r):ft(e)?!0:!!(e===Oe&&VA(e,A))},fr=function(e,A,r){return e===ne||e===dA?uA(A)?!0:A===Ne&&uA(r):uA(e===Ne?A:e)},Ya=function(e){var A=0,r=1;(e[A]===ne||e[A]===dA)&&(e[A]===dA&&(r=-1),A++);for(var t=[];uA(e[A]);)t.push(e[A++]);var n=t.length?parseInt(p.apply(void 0,t),10):0;e[A]===Ne&&A++;for(var o=[];uA(e[A]);)o.push(e[A++]);var a=o.length,c=a?parseInt(p.apply(void 0,o),10):0;(e[A]===Un||e[A]===Qn)&&A++;var s=1;(e[A]===ne||e[A]===dA)&&(e[A]===dA&&(s=-1),A++);for(var u=[];uA(e[A]);)u.push(e[A++]);var f=u.length?parseInt(p.apply(void 0,u),10):0;return r*(n+c*Math.pow(10,-a))*Math.pow(10,s*f)},$a={type:2},Za={type:3},za={type:4},qa={type:13},ja={type:8},As={type:21},es={type:9},ts={type:10},rs={type:11},ns={type:12},os={type:14},dt={type:23},is={type:1},as={type:25},ss={type:24},cs={type:26},ls={type:27},Bs={type:28},gs={type:29},us={type:31},wr={type:32},vn=function(){function e(){this._value=[]}return e.prototype.write=function(A){this._value=this._value.concat(d(A))},e.prototype.read=function(){for(var A=[],r=this.consumeToken();r!==wr;)A.push(r),r=this.consumeToken();return A},e.prototype.consumeToken=function(){var A=this.consumeCodePoint();switch(A){case ct:return this.consumeStringToken(ct);case ua:var r=this.peekCodePoint(0),t=this.peekCodePoint(1),n=this.peekCodePoint(2);if(mn(r)||VA(t,n)){var o=wt(r,t,n)?la:ca,a=this.consumeName();return{type:5,value:a,flags:o}}break;case fa:if(this.peekCodePoint(0)===_e)return this.consumeCodePoint(),qa;break;case lt:return this.consumeStringToken(lt);case Bt:return $a;case Pe:return Za;case ur:if(this.peekCodePoint(0)===_e)return this.consumeCodePoint(),os;break;case ne:if(fr(A,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(A),this.consumeNumericToken();break;case ya:return za;case dA:var c=A,s=this.peekCodePoint(0),u=this.peekCodePoint(1);if(fr(c,s,u))return this.reconsumeCodePoint(A),this.consumeNumericToken();if(wt(c,s,u))return this.reconsumeCodePoint(A),this.consumeIdentLikeToken();if(s===dA&&u===Qa)return this.consumeCodePoint(),this.consumeCodePoint(),ss;break;case Ne:if(fr(A,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(A),this.consumeNumericToken();break;case fn:if(this.peekCodePoint(0)===ur)for(this.consumeCodePoint();;){var f=this.consumeCodePoint();if(f===ur&&(f=this.consumeCodePoint(),f===fn))return this.consumeToken();if(f===MA)return this.consumeToken()}break;case ba:return cs;case La:return ls;case Ca:if(this.peekCodePoint(0)===ha&&this.peekCodePoint(1)===dA&&this.peekCodePoint(2)===dA)return this.consumeCodePoint(),this.consumeCodePoint(),as;break;case pa:var w=this.peekCodePoint(0),C=this.peekCodePoint(1),b=this.peekCodePoint(2);if(wt(w,C,b)){var a=this.consumeName();return{type:7,value:a}}break;case Ua:return Bs;case Oe:if(VA(A,this.peekCodePoint(0)))return this.reconsumeCodePoint(A),this.consumeIdentLikeToken();break;case Fa:return gs;case ma:if(this.peekCodePoint(0)===_e)return this.consumeCodePoint(),ja;break;case va:return rs;case Ea:return ns;case _a:case Na:var U=this.peekCodePoint(0),E=this.peekCodePoint(1);return U===ne&&(ue(E)||E===gt)&&(this.consumeCodePoint(),this.consumeUnicodeRangeToken()),this.reconsumeCodePoint(A),this.consumeIdentLikeToken();case wn:if(this.peekCodePoint(0)===_e)return this.consumeCodePoint(),es;if(this.peekCodePoint(0)===wn)return this.consumeCodePoint(),As;break;case Ha:if(this.peekCodePoint(0)===_e)return this.consumeCodePoint(),ts;break;case MA:return wr}return ut(A)?(this.consumeWhiteSpace(),us):uA(A)?(this.reconsumeCodePoint(A),this.consumeNumericToken()):ft(A)?(this.reconsumeCodePoint(A),this.consumeIdentLikeToken()):{type:6,value:p(A)}},e.prototype.consumeCodePoint=function(){var A=this._value.shift();return typeof A>"u"?-1:A},e.prototype.reconsumeCodePoint=function(A){this._value.unshift(A)},e.prototype.peekCodePoint=function(A){return A>=this._value.length?-1:this._value[A]},e.prototype.consumeUnicodeRangeToken=function(){for(var A=[],r=this.consumeCodePoint();ue(r)&&A.length<6;)A.push(r),r=this.consumeCodePoint();for(var t=!1;r===gt&&A.length<6;)A.push(r),r=this.consumeCodePoint(),t=!0;if(t){var n=parseInt(p.apply(void 0,A.map(function(s){return s===gt?hn:s})),16),o=parseInt(p.apply(void 0,A.map(function(s){return s===gt?Fn:s})),16);return{type:30,start:n,end:o}}var a=parseInt(p.apply(void 0,A),16);if(this.peekCodePoint(0)===dA&&ue(this.peekCodePoint(1))){this.consumeCodePoint(),r=this.consumeCodePoint();for(var c=[];ue(r)&&c.length<6;)c.push(r),r=this.consumeCodePoint();var o=parseInt(p.apply(void 0,c),16);return{type:30,start:a,end:o}}else return{type:30,start:a,end:a}},e.prototype.consumeIdentLikeToken=function(){var A=this.consumeName();return A.toLowerCase()==="url"&&this.peekCodePoint(0)===Bt?(this.consumeCodePoint(),this.consumeUrlToken()):this.peekCodePoint(0)===Bt?(this.consumeCodePoint(),{type:19,value:A}):{type:20,value:A}},e.prototype.consumeUrlToken=function(){var A=[];if(this.consumeWhiteSpace(),this.peekCodePoint(0)===MA)return{type:22,value:""};var r=this.peekCodePoint(0);if(r===lt||r===ct){var t=this.consumeStringToken(this.consumeCodePoint());return t.type===0&&(this.consumeWhiteSpace(),this.peekCodePoint(0)===MA||this.peekCodePoint(0)===Pe)?(this.consumeCodePoint(),{type:22,value:t.value}):(this.consumeBadUrlRemnants(),dt)}for(;;){var n=this.consumeCodePoint();if(n===MA||n===Pe)return{type:22,value:p.apply(void 0,A)};if(ut(n))return this.consumeWhiteSpace(),this.peekCodePoint(0)===MA||this.peekCodePoint(0)===Pe?(this.consumeCodePoint(),{type:22,value:p.apply(void 0,A)}):(this.consumeBadUrlRemnants(),dt);if(n===ct||n===lt||n===Bt||Wa(n))return this.consumeBadUrlRemnants(),dt;if(n===Oe)if(VA(n,this.peekCodePoint(0)))A.push(this.consumeEscapedCodePoint());else return this.consumeBadUrlRemnants(),dt;else A.push(n)}},e.prototype.consumeWhiteSpace=function(){for(;ut(this.peekCodePoint(0));)this.consumeCodePoint()},e.prototype.consumeBadUrlRemnants=function(){for(;;){var A=this.consumeCodePoint();if(A===Pe||A===MA)return;VA(A,this.peekCodePoint(0))&&this.consumeEscapedCodePoint()}},e.prototype.consumeStringSlice=function(A){for(var r=5e4,t="";A>0;){var n=Math.min(r,A);t+=p.apply(void 0,this._value.splice(0,n)),A-=n}return this._value.shift(),t},e.prototype.consumeStringToken=function(A){var r="",t=0;do{var n=this._value[t];if(n===MA||n===void 0||n===A)return r+=this.consumeStringSlice(t),{type:0,value:r};if(n===st)return this._value.splice(0,t),is;if(n===Oe){var o=this._value[t+1];o!==MA&&o!==void 0&&(o===st?(r+=this.consumeStringSlice(t),t=-1,this._value.shift()):VA(n,o)&&(r+=this.consumeStringSlice(t),r+=p(this.consumeEscapedCodePoint()),t=-1))}t++}while(!0)},e.prototype.consumeNumber=function(){var A=[],r=Te,t=this.peekCodePoint(0);for((t===ne||t===dA)&&A.push(this.consumeCodePoint());uA(this.peekCodePoint(0));)A.push(this.consumeCodePoint());t=this.peekCodePoint(0);var n=this.peekCodePoint(1);if(t===Ne&&uA(n))for(A.push(this.consumeCodePoint(),this.consumeCodePoint()),r=un;uA(this.peekCodePoint(0));)A.push(this.consumeCodePoint());t=this.peekCodePoint(0),n=this.peekCodePoint(1);var o=this.peekCodePoint(2);if((t===Un||t===Qn)&&((n===ne||n===dA)&&uA(o)||uA(n)))for(A.push(this.consumeCodePoint(),this.consumeCodePoint()),r=un;uA(this.peekCodePoint(0));)A.push(this.consumeCodePoint());return[Ya(A),r]},e.prototype.consumeNumericToken=function(){var A=this.consumeNumber(),r=A[0],t=A[1],n=this.peekCodePoint(0),o=this.peekCodePoint(1),a=this.peekCodePoint(2);if(wt(n,o,a)){var c=this.consumeName();return{type:15,number:r,flags:t,unit:c}}return n===wa?(this.consumeCodePoint(),{type:16,number:r,flags:t}):{type:17,number:r,flags:t}},e.prototype.consumeEscapedCodePoint=function(){var A=this.consumeCodePoint();if(ue(A)){for(var r=p(A);ue(this.peekCodePoint(0))&&r.length<6;)r+=p(this.consumeCodePoint());ut(this.peekCodePoint(0))&&this.consumeCodePoint();var t=parseInt(r,16);return t===0||Ra(t)||t>1114111?dn:t}return A===MA?dn:A},e.prototype.consumeName=function(){for(var A="";;){var r=this.consumeCodePoint();if(mn(r))A+=p(r);else if(VA(r,this.peekCodePoint(0)))A+=p(this.consumeEscapedCodePoint());else return this.reconsumeCodePoint(r),A}},e}(),En=function(){function e(A){this._tokens=A}return e.create=function(A){var r=new vn;return r.write(A),new e(r.read())},e.parseValue=function(A){return e.create(A).parseComponentValue()},e.parseValues=function(A){return e.create(A).parseComponentValues()},e.prototype.parseComponentValue=function(){for(var A=this.consumeToken();A.type===31;)A=this.consumeToken();if(A.type===32)throw new SyntaxError("Error parsing CSS component value, unexpected EOF");this.reconsumeToken(A);var r=this.consumeComponentValue();do A=this.consumeToken();while(A.type===31);if(A.type===32)return r;throw new SyntaxError("Error parsing CSS component value, multiple values found when expecting only one")},e.prototype.parseComponentValues=function(){for(var A=[];;){var r=this.consumeComponentValue();if(r.type===32)return A;A.push(r),A.push()}},e.prototype.consumeComponentValue=function(){var A=this.consumeToken();switch(A.type){case 11:case 28:case 2:return this.consumeSimpleBlock(A.type);case 19:return this.consumeFunction(A)}return A},e.prototype.consumeSimpleBlock=function(A){for(var r={type:A,values:[]},t=this.consumeToken();;){if(t.type===32||ws(t,A))return r;this.reconsumeToken(t),r.values.push(this.consumeComponentValue()),t=this.consumeToken()}},e.prototype.consumeFunction=function(A){for(var r={name:A.value,values:[],type:18};;){var t=this.consumeToken();if(t.type===32||t.type===3)return r;this.reconsumeToken(t),r.values.push(this.consumeComponentValue())}},e.prototype.consumeToken=function(){var A=this._tokens.shift();return typeof A>"u"?wr:A},e.prototype.reconsumeToken=function(A){this._tokens.unshift(A)},e}(),Ge=function(e){return e.type===15},fe=function(e){return e.type===17},j=function(e){return e.type===20},fs=function(e){return e.type===0},dr=function(e,A){return j(e)&&e.value===A},Hn=function(e){return e.type!==31},we=function(e){return e.type!==31&&e.type!==4},SA=function(e){var A=[],r=[];return e.forEach(function(t){if(t.type===4){if(r.length===0)throw new Error("Error parsing function args, zero tokens for arg");A.push(r),r=[];return}t.type!==31&&r.push(t)}),r.length&&A.push(r),A},ws=function(e,A){return A===11&&e.type===12||A===28&&e.type===29?!0:A===2&&e.type===3},XA=function(e){return e.type===17||e.type===15},iA=function(e){return e.type===16||XA(e)},In=function(e){return e.length>1?[e[0],e[1]]:[e[0]]},lA={type:17,number:0,flags:Te},hr={type:16,number:50,flags:Te},JA={type:16,number:100,flags:Te},Re=function(e,A,r){var t=e[0],n=e[1];return[tA(t,A),tA(typeof n<"u"?n:t,r)]},tA=function(e,A){if(e.type===16)return e.number/100*A;if(Ge(e))switch(e.unit){case"rem":case"em":return 16*e.number;case"px":default:return e.number}return e.number},yn="deg",bn="grad",Ln="rad",xn="turn",ht={name:"angle",parse:function(e,A){if(A.type===15)switch(A.unit){case yn:return Math.PI*A.number/180;case bn:return Math.PI/200*A.number;case Ln:return A.number;case xn:return Math.PI*2*A.number}throw new Error("Unsupported angle type")}},Mn=function(e){return e.type===15&&(e.unit===yn||e.unit===bn||e.unit===Ln||e.unit===xn)},Sn=function(e){var A=e.filter(j).map(function(r){return r.value}).join(" ");switch(A){case"to bottom right":case"to right bottom":case"left top":case"top left":return[lA,lA];case"to top":case"bottom":return mA(0);case"to bottom left":case"to left bottom":case"right top":case"top right":return[lA,JA];case"to right":case"left":return mA(90);case"to top left":case"to left top":case"right bottom":case"bottom right":return[JA,JA];case"to bottom":case"top":return mA(180);case"to top right":case"to right top":case"left bottom":case"bottom left":return[JA,lA];case"to left":case"right":return mA(270)}return 0},mA=function(e){return Math.PI*e/180},WA={name:"color",parse:function(e,A){if(A.type===18){var r=ds[A.name];if(typeof r>"u")throw new Error('Attempting to parse an unsupported color function "'+A.name+'"');return r(e,A.values)}if(A.type===5){if(A.value.length===3){var t=A.value.substring(0,1),n=A.value.substring(1,2),o=A.value.substring(2,3);return $A(parseInt(t+t,16),parseInt(n+n,16),parseInt(o+o,16),1)}if(A.value.length===4){var t=A.value.substring(0,1),n=A.value.substring(1,2),o=A.value.substring(2,3),a=A.value.substring(3,4);return $A(parseInt(t+t,16),parseInt(n+n,16),parseInt(o+o,16),parseInt(a+a,16)/255)}if(A.value.length===6){var t=A.value.substring(0,2),n=A.value.substring(2,4),o=A.value.substring(4,6);return $A(parseInt(t,16),parseInt(n,16),parseInt(o,16),1)}if(A.value.length===8){var t=A.value.substring(0,2),n=A.value.substring(2,4),o=A.value.substring(4,6),a=A.value.substring(6,8);return $A(parseInt(t,16),parseInt(n,16),parseInt(o,16),parseInt(a,16)/255)}}if(A.type===20){var c=OA[A.value.toUpperCase()];if(typeof c<"u")return c}return OA.TRANSPARENT}},YA=function(e){return(255&e)===0},cA=function(e){var A=255&e,r=255&e>>8,t=255&e>>16,n=255&e>>24;return A<255?"rgba("+n+","+t+","+r+","+A/255+")":"rgb("+n+","+t+","+r+")"},$A=function(e,A,r,t){return(e<<24|A<<16|r<<8|Math.round(t*255)<<0)>>>0},Kn=function(e,A){if(e.type===17)return e.number;if(e.type===16){var r=A===3?1:255;return A===3?e.number/100*r:Math.round(e.number/100*r)}return 0},Dn=function(e,A){var r=A.filter(we);if(r.length===3){var t=r.map(Kn),n=t[0],o=t[1],a=t[2];return $A(n,o,a,1)}if(r.length===4){var c=r.map(Kn),n=c[0],o=c[1],a=c[2],s=c[3];return $A(n,o,a,s)}return 0};function Cr(e,A,r){return r<0&&(r+=1),r>=1&&(r-=1),r<1/6?(A-e)*r*6+e:r<1/2?A:r<2/3?(A-e)*6*(2/3-r)+e:e}var Tn=function(e,A){var r=A.filter(we),t=r[0],n=r[1],o=r[2],a=r[3],c=(t.type===17?mA(t.number):ht.parse(e,t))/(Math.PI*2),s=iA(n)?n.number/100:0,u=iA(o)?o.number/100:0,f=typeof a<"u"&&iA(a)?tA(a,1):1;if(s===0)return $A(u*255,u*255,u*255,1);var w=u<=.5?u*(s+1):u+s-u*s,C=u*2-w,b=Cr(C,w,c+1/3),U=Cr(C,w,c),E=Cr(C,w,c-1/3);return $A(b*255,U*255,E*255,f)},ds={hsl:Tn,hsla:Tn,rgb:Dn,rgba:Dn},ke=function(e,A){return WA.parse(e,En.create(A).parseComponentValue())},OA={ALICEBLUE:4042850303,ANTIQUEWHITE:4209760255,AQUA:16777215,AQUAMARINE:2147472639,AZURE:4043309055,BEIGE:4126530815,BISQUE:4293182719,BLACK:255,BLANCHEDALMOND:4293643775,BLUE:65535,BLUEVIOLET:2318131967,BROWN:2771004159,BURLYWOOD:3736635391,CADETBLUE:1604231423,CHARTREUSE:2147418367,CHOCOLATE:3530104575,CORAL:4286533887,CORNFLOWERBLUE:1687547391,CORNSILK:4294499583,CRIMSON:3692313855,CYAN:16777215,DARKBLUE:35839,DARKCYAN:9145343,DARKGOLDENROD:3095837695,DARKGRAY:2846468607,DARKGREEN:6553855,DARKGREY:2846468607,DARKKHAKI:3182914559,DARKMAGENTA:2332068863,DARKOLIVEGREEN:1433087999,DARKORANGE:4287365375,DARKORCHID:2570243327,DARKRED:2332033279,DARKSALMON:3918953215,DARKSEAGREEN:2411499519,DARKSLATEBLUE:1211993087,DARKSLATEGRAY:793726975,DARKSLATEGREY:793726975,DARKTURQUOISE:13554175,DARKVIOLET:2483082239,DEEPPINK:4279538687,DEEPSKYBLUE:12582911,DIMGRAY:1768516095,DIMGREY:1768516095,DODGERBLUE:512819199,FIREBRICK:2988581631,FLORALWHITE:4294635775,FORESTGREEN:579543807,FUCHSIA:4278255615,GAINSBORO:3705462015,GHOSTWHITE:4177068031,GOLD:4292280575,GOLDENROD:3668254975,GRAY:2155905279,GREEN:8388863,GREENYELLOW:2919182335,GREY:2155905279,HONEYDEW:4043305215,HOTPINK:4285117695,INDIANRED:3445382399,INDIGO:1258324735,IVORY:4294963455,KHAKI:4041641215,LAVENDER:3873897215,LAVENDERBLUSH:4293981695,LAWNGREEN:2096890111,LEMONCHIFFON:4294626815,LIGHTBLUE:2916673279,LIGHTCORAL:4034953471,LIGHTCYAN:3774873599,LIGHTGOLDENRODYELLOW:4210742015,LIGHTGRAY:3553874943,LIGHTGREEN:2431553791,LIGHTGREY:3553874943,LIGHTPINK:4290167295,LIGHTSALMON:4288707327,LIGHTSEAGREEN:548580095,LIGHTSKYBLUE:2278488831,LIGHTSLATEGRAY:2005441023,LIGHTSLATEGREY:2005441023,LIGHTSTEELBLUE:2965692159,LIGHTYELLOW:4294959359,LIME:16711935,LIMEGREEN:852308735,LINEN:4210091775,MAGENTA:4278255615,MAROON:2147483903,MEDIUMAQUAMARINE:1724754687,MEDIUMBLUE:52735,MEDIUMORCHID:3126187007,MEDIUMPURPLE:2473647103,MEDIUMSEAGREEN:1018393087,MEDIUMSLATEBLUE:2070474495,MEDIUMSPRINGGREEN:16423679,MEDIUMTURQUOISE:1221709055,MEDIUMVIOLETRED:3340076543,MIDNIGHTBLUE:421097727,MINTCREAM:4127193855,MISTYROSE:4293190143,MOCCASIN:4293178879,NAVAJOWHITE:4292783615,NAVY:33023,OLDLACE:4260751103,OLIVE:2155872511,OLIVEDRAB:1804477439,ORANGE:4289003775,ORANGERED:4282712319,ORCHID:3664828159,PALEGOLDENROD:4008225535,PALEGREEN:2566625535,PALETURQUOISE:2951671551,PALEVIOLETRED:3681588223,PAPAYAWHIP:4293907967,PEACHPUFF:4292524543,PERU:3448061951,PINK:4290825215,PLUM:3718307327,POWDERBLUE:2967529215,PURPLE:2147516671,REBECCAPURPLE:1714657791,RED:4278190335,ROSYBROWN:3163525119,ROYALBLUE:1097458175,SADDLEBROWN:2336560127,SALMON:4202722047,SANDYBROWN:4104413439,SEAGREEN:780883967,SEASHELL:4294307583,SIENNA:2689740287,SILVER:3233857791,SKYBLUE:2278484991,SLATEBLUE:1784335871,SLATEGRAY:1887473919,SLATEGREY:1887473919,SNOW:4294638335,SPRINGGREEN:16744447,STEELBLUE:1182971135,TAN:3535047935,TEAL:8421631,THISTLE:3636451583,TOMATO:4284696575,TRANSPARENT:0,TURQUOISE:1088475391,VIOLET:4001558271,WHEAT:4125012991,WHITE:4294967295,WHITESMOKE:4126537215,YELLOW:4294902015,YELLOWGREEN:2597139199},hs={name:"background-clip",initialValue:"border-box",prefix:!1,type:1,parse:function(e,A){return A.map(function(r){if(j(r))switch(r.value){case"padding-box":return 1;case"content-box":return 2}return 0})}},Cs={name:"background-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},Ct=function(e,A){var r=WA.parse(e,A[0]),t=A[1];return t&&iA(t)?{color:r,stop:t}:{color:r,stop:null}},On=function(e,A){var r=e[0],t=e[e.length-1];r.stop===null&&(r.stop=lA),t.stop===null&&(t.stop=JA);for(var n=[],o=0,a=0;a<e.length;a++){var c=e[a].stop;if(c!==null){var s=tA(c,A);s>o?n.push(s):n.push(o),o=s}else n.push(null)}for(var u=null,a=0;a<n.length;a++){var f=n[a];if(f===null)u===null&&(u=a);else if(u!==null){for(var w=a-u,C=n[u-1],b=(f-C)/(w+1),U=1;U<=w;U++)n[u+U-1]=b*U;u=null}}return e.map(function(E,N){var S=E.color;return{color:S,stop:Math.max(Math.min(1,n[N]/A),0)}})},Qs=function(e,A,r){var t=A/2,n=r/2,o=tA(e[0],A)-t,a=n-tA(e[1],r);return(Math.atan2(a,o)+Math.PI*2)%(Math.PI*2)},ps=function(e,A,r){var t=typeof e=="number"?e:Qs(e,A,r),n=Math.abs(A*Math.sin(t))+Math.abs(r*Math.cos(t)),o=A/2,a=r/2,c=n/2,s=Math.sin(t-Math.PI/2)*c,u=Math.cos(t-Math.PI/2)*c;return[n,o-u,o+u,a-s,a+s]},bA=function(e,A){return Math.sqrt(e*e+A*A)},_n=function(e,A,r,t,n){var o=[[0,0],[0,A],[e,0],[e,A]];return o.reduce(function(a,c){var s=c[0],u=c[1],f=bA(r-s,t-u);return(n?f<a.optimumDistance:f>a.optimumDistance)?{optimumCorner:c,optimumDistance:f}:a},{optimumDistance:n?1/0:-1/0,optimumCorner:null}).optimumCorner},Us=function(e,A,r,t,n){var o=0,a=0;switch(e.size){case 0:e.shape===0?o=a=Math.min(Math.abs(A),Math.abs(A-t),Math.abs(r),Math.abs(r-n)):e.shape===1&&(o=Math.min(Math.abs(A),Math.abs(A-t)),a=Math.min(Math.abs(r),Math.abs(r-n)));break;case 2:if(e.shape===0)o=a=Math.min(bA(A,r),bA(A,r-n),bA(A-t,r),bA(A-t,r-n));else if(e.shape===1){var c=Math.min(Math.abs(r),Math.abs(r-n))/Math.min(Math.abs(A),Math.abs(A-t)),s=_n(t,n,A,r,!0),u=s[0],f=s[1];o=bA(u-A,(f-r)/c),a=c*o}break;case 1:e.shape===0?o=a=Math.max(Math.abs(A),Math.abs(A-t),Math.abs(r),Math.abs(r-n)):e.shape===1&&(o=Math.max(Math.abs(A),Math.abs(A-t)),a=Math.max(Math.abs(r),Math.abs(r-n)));break;case 3:if(e.shape===0)o=a=Math.max(bA(A,r),bA(A,r-n),bA(A-t,r),bA(A-t,r-n));else if(e.shape===1){var c=Math.max(Math.abs(r),Math.abs(r-n))/Math.max(Math.abs(A),Math.abs(A-t)),w=_n(t,n,A,r,!1),u=w[0],f=w[1];o=bA(u-A,(f-r)/c),a=c*o}break}return Array.isArray(e.size)&&(o=tA(e.size[0],t),a=e.size.length===2?tA(e.size[1],n):o),[o,a]},Fs=function(e,A){var r=mA(180),t=[];return SA(A).forEach(function(n,o){if(o===0){var a=n[0];if(a.type===20&&a.value==="to"){r=Sn(n);return}else if(Mn(a)){r=ht.parse(e,a);return}}var c=Ct(e,n);t.push(c)}),{angle:r,stops:t,type:1}},Qt=function(e,A){var r=mA(180),t=[];return SA(A).forEach(function(n,o){if(o===0){var a=n[0];if(a.type===20&&["top","left","right","bottom"].indexOf(a.value)!==-1){r=Sn(n);return}else if(Mn(a)){r=(ht.parse(e,a)+mA(270))%mA(360);return}}var c=Ct(e,n);t.push(c)}),{angle:r,stops:t,type:1}},ms=function(e,A){var r=mA(180),t=[],n=1,o=0,a=3,c=[];return SA(A).forEach(function(s,u){var f=s[0];if(u===0){if(j(f)&&f.value==="linear"){n=1;return}else if(j(f)&&f.value==="radial"){n=2;return}}if(f.type===18){if(f.name==="from"){var w=WA.parse(e,f.values[0]);t.push({stop:lA,color:w})}else if(f.name==="to"){var w=WA.parse(e,f.values[0]);t.push({stop:JA,color:w})}else if(f.name==="color-stop"){var C=f.values.filter(we);if(C.length===2){var w=WA.parse(e,C[1]),b=C[0];fe(b)&&t.push({stop:{type:16,number:b.number*100,flags:b.flags},color:w})}}}}),n===1?{angle:(r+mA(180))%mA(360),stops:t,type:n}:{size:a,shape:o,stops:t,position:c,type:n}},Pn="closest-side",Nn="farthest-side",Gn="closest-corner",Rn="farthest-corner",kn="circle",Vn="ellipse",Xn="cover",Jn="contain",vs=function(e,A){var r=0,t=3,n=[],o=[];return SA(A).forEach(function(a,c){var s=!0;if(c===0){var u=!1;s=a.reduce(function(w,C){if(u)if(j(C))switch(C.value){case"center":return o.push(hr),w;case"top":case"left":return o.push(lA),w;case"right":case"bottom":return o.push(JA),w}else(iA(C)||XA(C))&&o.push(C);else if(j(C))switch(C.value){case kn:return r=0,!1;case Vn:return r=1,!1;case"at":return u=!0,!1;case Pn:return t=0,!1;case Xn:case Nn:return t=1,!1;case Jn:case Gn:return t=2,!1;case Rn:return t=3,!1}else if(XA(C)||iA(C))return Array.isArray(t)||(t=[]),t.push(C),!1;return w},s)}if(s){var f=Ct(e,a);n.push(f)}}),{size:t,shape:r,stops:n,position:o,type:2}},pt=function(e,A){var r=0,t=3,n=[],o=[];return SA(A).forEach(function(a,c){var s=!0;if(c===0?s=a.reduce(function(f,w){if(j(w))switch(w.value){case"center":return o.push(hr),!1;case"top":case"left":return o.push(lA),!1;case"right":case"bottom":return o.push(JA),!1}else if(iA(w)||XA(w))return o.push(w),!1;return f},s):c===1&&(s=a.reduce(function(f,w){if(j(w))switch(w.value){case kn:return r=0,!1;case Vn:return r=1,!1;case Jn:case Pn:return t=0,!1;case Nn:return t=1,!1;case Gn:return t=2,!1;case Xn:case Rn:return t=3,!1}else if(XA(w)||iA(w))return Array.isArray(t)||(t=[]),t.push(w),!1;return f},s)),s){var u=Ct(e,a);n.push(u)}}),{size:t,shape:r,stops:n,position:o,type:2}},Es=function(e){return e.type===1},Hs=function(e){return e.type===2},Qr={name:"image",parse:function(e,A){if(A.type===22){var r={url:A.value,type:0};return e.cache.addImage(A.value),r}if(A.type===18){var t=Wn[A.name];if(typeof t>"u")throw new Error('Attempting to parse an unsupported image function "'+A.name+'"');return t(e,A.values)}throw new Error("Unsupported image type "+A.type)}};function Is(e){return!(e.type===20&&e.value==="none")&&(e.type!==18||!!Wn[e.name])}for(var Wn={"linear-gradient":Fs,"-moz-linear-gradient":Qt,"-ms-linear-gradient":Qt,"-o-linear-gradient":Qt,"-webkit-linear-gradient":Qt,"radial-gradient":vs,"-moz-radial-gradient":pt,"-ms-radial-gradient":pt,"-o-radial-gradient":pt,"-webkit-radial-gradient":pt,"-webkit-gradient":ms},ys={name:"background-image",initialValue:"none",type:1,prefix:!1,parse:function(e,A){if(A.length===0)return[];var r=A[0];return r.type===20&&r.value==="none"?[]:A.filter(function(t){return we(t)&&Is(t)}).map(function(t){return Qr.parse(e,t)})}},bs={name:"background-origin",initialValue:"border-box",prefix:!1,type:1,parse:function(e,A){return A.map(function(r){if(j(r))switch(r.value){case"padding-box":return 1;case"content-box":return 2}return 0})}},Ls={name:"background-position",initialValue:"0% 0%",type:1,prefix:!1,parse:function(e,A){return SA(A).map(function(r){return r.filter(iA)}).map(In)}},xs={name:"background-repeat",initialValue:"repeat",prefix:!1,type:1,parse:function(e,A){return SA(A).map(function(r){return r.filter(j).map(function(t){return t.value}).join(" ")}).map(Ms)}},Ms=function(e){switch(e){case"no-repeat":return 1;case"repeat-x":case"repeat no-repeat":return 2;case"repeat-y":case"no-repeat repeat":return 3;case"repeat":default:return 0}},Ve=function(e){return e.AUTO="auto",e.CONTAIN="contain",e.COVER="cover",e}(Ve||{}),Ss={name:"background-size",initialValue:"0",prefix:!1,type:1,parse:function(e,A){return SA(A).map(function(r){return r.filter(Ks)})}},Ks=function(e){return j(e)||iA(e)},Ut=function(e){return{name:"border-"+e+"-color",initialValue:"transparent",prefix:!1,type:3,format:"color"}},Ds=Ut("top"),Ts=Ut("right"),Os=Ut("bottom"),_s=Ut("left"),Ft=function(e){return{name:"border-radius-"+e,initialValue:"0 0",prefix:!1,type:1,parse:function(A,r){return In(r.filter(iA))}}},Ps=Ft("top-left"),Ns=Ft("top-right"),Gs=Ft("bottom-right"),Rs=Ft("bottom-left"),mt=function(e){return{name:"border-"+e+"-style",initialValue:"solid",prefix:!1,type:2,parse:function(A,r){switch(r){case"none":return 0;case"dashed":return 2;case"dotted":return 3;case"double":return 4}return 1}}},ks=mt("top"),Vs=mt("right"),Xs=mt("bottom"),Js=mt("left"),vt=function(e){return{name:"border-"+e+"-width",initialValue:"0",type:0,prefix:!1,parse:function(A,r){return Ge(r)?r.number:0}}},Ws=vt("top"),Ys=vt("right"),$s=vt("bottom"),Zs=vt("left"),zs={name:"color",initialValue:"transparent",prefix:!1,type:3,format:"color"},qs={name:"direction",initialValue:"ltr",prefix:!1,type:2,parse:function(e,A){switch(A){case"rtl":return 1;case"ltr":default:return 0}}},js={name:"display",initialValue:"inline-block",prefix:!1,type:1,parse:function(e,A){return A.filter(j).reduce(function(r,t){return r|Ac(t.value)},0)}},Ac=function(e){switch(e){case"block":case"-webkit-box":return 2;case"inline":return 4;case"run-in":return 8;case"flow":return 16;case"flow-root":return 32;case"table":return 64;case"flex":case"-webkit-flex":return 128;case"grid":case"-ms-grid":return 256;case"ruby":return 512;case"subgrid":return 1024;case"list-item":return 2048;case"table-row-group":return 4096;case"table-header-group":return 8192;case"table-footer-group":return 16384;case"table-row":return 32768;case"table-cell":return 65536;case"table-column-group":return 131072;case"table-column":return 262144;case"table-caption":return 524288;case"ruby-base":return 1048576;case"ruby-text":return 2097152;case"ruby-base-container":return 4194304;case"ruby-text-container":return 8388608;case"contents":return 16777216;case"inline-block":return 33554432;case"inline-list-item":return 67108864;case"inline-table":return 134217728;case"inline-flex":return 268435456;case"inline-grid":return 536870912}return 0},ec={name:"float",initialValue:"none",prefix:!1,type:2,parse:function(e,A){switch(A){case"left":return 1;case"right":return 2;case"inline-start":return 3;case"inline-end":return 4}return 0}},tc={name:"letter-spacing",initialValue:"0",prefix:!1,type:0,parse:function(e,A){return A.type===20&&A.value==="normal"?0:A.type===17||A.type===15?A.number:0}},pr=function(e){return e.NORMAL="normal",e.STRICT="strict",e}(pr||{}),rc={name:"line-break",initialValue:"normal",prefix:!1,type:2,parse:function(e,A){switch(A){case"strict":return pr.STRICT;case"normal":default:return pr.NORMAL}}},nc={name:"line-height",initialValue:"normal",prefix:!1,type:4},Yn=function(e,A){return j(e)&&e.value==="normal"?1.2*A:e.type===17?A*e.number:iA(e)?tA(e,A):A},oc={name:"list-style-image",initialValue:"none",type:0,prefix:!1,parse:function(e,A){return A.type===20&&A.value==="none"?null:Qr.parse(e,A)}},ic={name:"list-style-position",initialValue:"outside",prefix:!1,type:2,parse:function(e,A){switch(A){case"inside":return 0;case"outside":default:return 1}}},Ur={name:"list-style-type",initialValue:"none",prefix:!1,type:2,parse:function(e,A){switch(A){case"disc":return 0;case"circle":return 1;case"square":return 2;case"decimal":return 3;case"cjk-decimal":return 4;case"decimal-leading-zero":return 5;case"lower-roman":return 6;case"upper-roman":return 7;case"lower-greek":return 8;case"lower-alpha":return 9;case"upper-alpha":return 10;case"arabic-indic":return 11;case"armenian":return 12;case"bengali":return 13;case"cambodian":return 14;case"cjk-earthly-branch":return 15;case"cjk-heavenly-stem":return 16;case"cjk-ideographic":return 17;case"devanagari":return 18;case"ethiopic-numeric":return 19;case"georgian":return 20;case"gujarati":return 21;case"gurmukhi":return 22;case"hebrew":return 22;case"hiragana":return 23;case"hiragana-iroha":return 24;case"japanese-formal":return 25;case"japanese-informal":return 26;case"kannada":return 27;case"katakana":return 28;case"katakana-iroha":return 29;case"khmer":return 30;case"korean-hangul-formal":return 31;case"korean-hanja-formal":return 32;case"korean-hanja-informal":return 33;case"lao":return 34;case"lower-armenian":return 35;case"malayalam":return 36;case"mongolian":return 37;case"myanmar":return 38;case"oriya":return 39;case"persian":return 40;case"simp-chinese-formal":return 41;case"simp-chinese-informal":return 42;case"tamil":return 43;case"telugu":return 44;case"thai":return 45;case"tibetan":return 46;case"trad-chinese-formal":return 47;case"trad-chinese-informal":return 48;case"upper-armenian":return 49;case"disclosure-open":return 50;case"disclosure-closed":return 51;case"none":default:return-1}}},Et=function(e){return{name:"margin-"+e,initialValue:"0",prefix:!1,type:4}},ac=Et("top"),sc=Et("right"),cc=Et("bottom"),lc=Et("left"),Bc={name:"overflow",initialValue:"visible",prefix:!1,type:1,parse:function(e,A){return A.filter(j).map(function(r){switch(r.value){case"hidden":return 1;case"scroll":return 2;case"clip":return 3;case"auto":return 4;case"visible":default:return 0}})}},gc={name:"overflow-wrap",initialValue:"normal",prefix:!1,type:2,parse:function(e,A){switch(A){case"break-word":return"break-word";case"normal":default:return"normal"}}},Ht=function(e){return{name:"padding-"+e,initialValue:"0",prefix:!1,type:3,format:"length-percentage"}},uc=Ht("top"),fc=Ht("right"),wc=Ht("bottom"),dc=Ht("left"),hc={name:"text-align",initialValue:"left",prefix:!1,type:2,parse:function(e,A){switch(A){case"right":return 2;case"center":case"justify":return 1;case"left":default:return 0}}},Cc={name:"position",initialValue:"static",prefix:!1,type:2,parse:function(e,A){switch(A){case"relative":return 1;case"absolute":return 2;case"fixed":return 3;case"sticky":return 4}return 0}},Qc={name:"text-shadow",initialValue:"none",type:1,prefix:!1,parse:function(e,A){return A.length===1&&dr(A[0],"none")?[]:SA(A).map(function(r){for(var t={color:OA.TRANSPARENT,offsetX:lA,offsetY:lA,blur:lA},n=0,o=0;o<r.length;o++){var a=r[o];XA(a)?(n===0?t.offsetX=a:n===1?t.offsetY=a:t.blur=a,n++):t.color=WA.parse(e,a)}return t})}},pc={name:"text-transform",initialValue:"none",prefix:!1,type:2,parse:function(e,A){switch(A){case"uppercase":return 2;case"lowercase":return 1;case"capitalize":return 3}return 0}},Uc={name:"transform",initialValue:"none",prefix:!0,type:0,parse:function(e,A){if(A.type===20&&A.value==="none")return null;if(A.type===18){var r=vc[A.name];if(typeof r>"u")throw new Error('Attempting to parse an unsupported transform function "'+A.name+'"');return r(A.values)}return null}},Fc=function(e){var A=e.filter(function(r){return r.type===17}).map(function(r){return r.number});return A.length===6?A:null},mc=function(e){var A=e.filter(function(s){return s.type===17}).map(function(s){return s.number}),r=A[0],t=A[1];A[2],A[3];var n=A[4],o=A[5];A[6],A[7],A[8],A[9],A[10],A[11];var a=A[12],c=A[13];return A[14],A[15],A.length===16?[r,t,n,o,a,c]:null},vc={matrix:Fc,matrix3d:mc},$n={type:16,number:50,flags:Te},Ec=[$n,$n],Hc={name:"transform-origin",initialValue:"50% 50%",prefix:!0,type:1,parse:function(e,A){var r=A.filter(iA);return r.length!==2?Ec:[r[0],r[1]]}},Ic={name:"visible",initialValue:"none",prefix:!1,type:2,parse:function(e,A){switch(A){case"hidden":return 1;case"collapse":return 2;case"visible":default:return 0}}},It=function(e){return e.NORMAL="normal",e.BREAK_ALL="break-all",e.KEEP_ALL="keep-all",e}(It||{}),yc={name:"word-break",initialValue:"normal",prefix:!1,type:2,parse:function(e,A){switch(A){case"break-all":return It.BREAK_ALL;case"keep-all":return It.KEEP_ALL;case"normal":default:return It.NORMAL}}},bc={name:"z-index",initialValue:"auto",prefix:!1,type:0,parse:function(e,A){if(A.type===20)return{auto:!0,order:0};if(fe(A))return{auto:!1,order:A.number};throw new Error("Invalid z-index number parsed")}},Zn={name:"time",parse:function(e,A){if(A.type===15)switch(A.unit.toLowerCase()){case"s":return 1e3*A.number;case"ms":return A.number}throw new Error("Unsupported time type")}},Lc={name:"opacity",initialValue:"1",type:0,prefix:!1,parse:function(e,A){return fe(A)?A.number:1}},xc={name:"text-decoration-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},Mc={name:"text-decoration-line",initialValue:"none",prefix:!1,type:1,parse:function(e,A){return A.filter(j).map(function(r){switch(r.value){case"underline":return 1;case"overline":return 2;case"line-through":return 3;case"none":return 4}return 0}).filter(function(r){return r!==0})}},Sc={name:"font-family",initialValue:"",prefix:!1,type:1,parse:function(e,A){var r=[],t=[];return A.forEach(function(n){switch(n.type){case 20:case 0:r.push(n.value);break;case 17:r.push(n.number.toString());break;case 4:t.push(r.join(" ")),r.length=0;break}}),r.length&&t.push(r.join(" ")),t.map(function(n){return n.indexOf(" ")===-1?n:"'"+n+"'"})}},Kc={name:"font-size",initialValue:"0",prefix:!1,type:3,format:"length"},Dc={name:"font-weight",initialValue:"normal",type:0,prefix:!1,parse:function(e,A){if(fe(A))return A.number;if(j(A))switch(A.value){case"bold":return 700;case"normal":default:return 400}return 400}},Tc={name:"font-variant",initialValue:"none",type:1,prefix:!1,parse:function(e,A){return A.filter(j).map(function(r){return r.value})}},Oc={name:"font-style",initialValue:"normal",prefix:!1,type:2,parse:function(e,A){switch(A){case"oblique":return"oblique";case"italic":return"italic";case"normal":default:return"normal"}}},sA=function(e,A){return(e&A)!==0},_c={name:"content",initialValue:"none",type:1,prefix:!1,parse:function(e,A){if(A.length===0)return[];var r=A[0];return r.type===20&&r.value==="none"?[]:A}},Pc={name:"counter-increment",initialValue:"none",prefix:!0,type:1,parse:function(e,A){if(A.length===0)return null;var r=A[0];if(r.type===20&&r.value==="none")return null;for(var t=[],n=A.filter(Hn),o=0;o<n.length;o++){var a=n[o],c=n[o+1];if(a.type===20){var s=c&&fe(c)?c.number:1;t.push({counter:a.value,increment:s})}}return t}},Nc={name:"counter-reset",initialValue:"none",prefix:!0,type:1,parse:function(e,A){if(A.length===0)return[];for(var r=[],t=A.filter(Hn),n=0;n<t.length;n++){var o=t[n],a=t[n+1];if(j(o)&&o.value!=="none"){var c=a&&fe(a)?a.number:0;r.push({counter:o.value,reset:c})}}return r}},Gc={name:"duration",initialValue:"0s",prefix:!1,type:1,parse:function(e,A){return A.filter(Ge).map(function(r){return Zn.parse(e,r)})}},Rc={name:"quotes",initialValue:"none",prefix:!0,type:1,parse:function(e,A){if(A.length===0)return null;var r=A[0];if(r.type===20&&r.value==="none")return null;var t=[],n=A.filter(fs);if(n.length%2!==0)return null;for(var o=0;o<n.length;o+=2){var a=n[o].value,c=n[o+1].value;t.push({open:a,close:c})}return t}},zn=function(e,A,r){if(!e)return"";var t=e[Math.min(A,e.length-1)];return t?r?t.open:t.close:""},kc={name:"box-shadow",initialValue:"none",type:1,prefix:!1,parse:function(e,A){return A.length===1&&dr(A[0],"none")?[]:SA(A).map(function(r){for(var t={color:255,offsetX:lA,offsetY:lA,blur:lA,spread:lA,inset:!1},n=0,o=0;o<r.length;o++){var a=r[o];dr(a,"inset")?t.inset=!0:XA(a)?(n===0?t.offsetX=a:n===1?t.offsetY=a:n===2?t.blur=a:t.spread=a,n++):t.color=WA.parse(e,a)}return t})}},Vc={name:"paint-order",initialValue:"normal",prefix:!1,type:1,parse:function(e,A){var r=[0,1,2],t=[];return A.filter(j).forEach(function(n){switch(n.value){case"stroke":t.push(1);break;case"fill":t.push(0);break;case"markers":t.push(2);break}}),r.forEach(function(n){t.indexOf(n)===-1&&t.push(n)}),t}},Xc={name:"-webkit-text-stroke-color",initialValue:"currentcolor",prefix:!1,type:3,format:"color"},Jc={name:"-webkit-text-stroke-width",initialValue:"0",type:0,prefix:!1,parse:function(e,A){return Ge(A)?A.number:0}},Wc=function(){function e(A,r){var t,n;this.animationDuration=L(A,Gc,r.animationDuration),this.backgroundClip=L(A,hs,r.backgroundClip),this.backgroundColor=L(A,Cs,r.backgroundColor),this.backgroundImage=L(A,ys,r.backgroundImage),this.backgroundOrigin=L(A,bs,r.backgroundOrigin),this.backgroundPosition=L(A,Ls,r.backgroundPosition),this.backgroundRepeat=L(A,xs,r.backgroundRepeat),this.backgroundSize=L(A,Ss,r.backgroundSize),this.borderTopColor=L(A,Ds,r.borderTopColor),this.borderRightColor=L(A,Ts,r.borderRightColor),this.borderBottomColor=L(A,Os,r.borderBottomColor),this.borderLeftColor=L(A,_s,r.borderLeftColor),this.borderTopLeftRadius=L(A,Ps,r.borderTopLeftRadius),this.borderTopRightRadius=L(A,Ns,r.borderTopRightRadius),this.borderBottomRightRadius=L(A,Gs,r.borderBottomRightRadius),this.borderBottomLeftRadius=L(A,Rs,r.borderBottomLeftRadius),this.borderTopStyle=L(A,ks,r.borderTopStyle),this.borderRightStyle=L(A,Vs,r.borderRightStyle),this.borderBottomStyle=L(A,Xs,r.borderBottomStyle),this.borderLeftStyle=L(A,Js,r.borderLeftStyle),this.borderTopWidth=L(A,Ws,r.borderTopWidth),this.borderRightWidth=L(A,Ys,r.borderRightWidth),this.borderBottomWidth=L(A,$s,r.borderBottomWidth),this.borderLeftWidth=L(A,Zs,r.borderLeftWidth),this.boxShadow=L(A,kc,r.boxShadow),this.color=L(A,zs,r.color),this.direction=L(A,qs,r.direction),this.display=L(A,js,r.display),this.float=L(A,ec,r.cssFloat),this.fontFamily=L(A,Sc,r.fontFamily),this.fontSize=L(A,Kc,r.fontSize),this.fontStyle=L(A,Oc,r.fontStyle),this.fontVariant=L(A,Tc,r.fontVariant),this.fontWeight=L(A,Dc,r.fontWeight),this.letterSpacing=L(A,tc,r.letterSpacing),this.lineBreak=L(A,rc,r.lineBreak),this.lineHeight=L(A,nc,r.lineHeight),this.listStyleImage=L(A,oc,r.listStyleImage),this.listStylePosition=L(A,ic,r.listStylePosition),this.listStyleType=L(A,Ur,r.listStyleType),this.marginTop=L(A,ac,r.marginTop),this.marginRight=L(A,sc,r.marginRight),this.marginBottom=L(A,cc,r.marginBottom),this.marginLeft=L(A,lc,r.marginLeft),this.opacity=L(A,Lc,r.opacity);var o=L(A,Bc,r.overflow);this.overflowX=o[0],this.overflowY=o[o.length>1?1:0],this.overflowWrap=L(A,gc,r.overflowWrap),this.paddingTop=L(A,uc,r.paddingTop),this.paddingRight=L(A,fc,r.paddingRight),this.paddingBottom=L(A,wc,r.paddingBottom),this.paddingLeft=L(A,dc,r.paddingLeft),this.paintOrder=L(A,Vc,r.paintOrder),this.position=L(A,Cc,r.position),this.textAlign=L(A,hc,r.textAlign),this.textDecorationColor=L(A,xc,(t=r.textDecorationColor)!==null&&t!==void 0?t:r.color),this.textDecorationLine=L(A,Mc,(n=r.textDecorationLine)!==null&&n!==void 0?n:r.textDecoration),this.textShadow=L(A,Qc,r.textShadow),this.textTransform=L(A,pc,r.textTransform),this.transform=L(A,Uc,r.transform),this.transformOrigin=L(A,Hc,r.transformOrigin),this.visibility=L(A,Ic,r.visibility),this.webkitTextStrokeColor=L(A,Xc,r.webkitTextStrokeColor),this.webkitTextStrokeWidth=L(A,Jc,r.webkitTextStrokeWidth),this.wordBreak=L(A,yc,r.wordBreak),this.zIndex=L(A,bc,r.zIndex)}return e.prototype.isVisible=function(){return this.display>0&&this.opacity>0&&this.visibility===0},e.prototype.isTransparent=function(){return YA(this.backgroundColor)},e.prototype.isTransformed=function(){return this.transform!==null},e.prototype.isPositioned=function(){return this.position!==0},e.prototype.isPositionedWithZIndex=function(){return this.isPositioned()&&!this.zIndex.auto},e.prototype.isFloating=function(){return this.float!==0},e.prototype.isInlineLevel=function(){return sA(this.display,4)||sA(this.display,33554432)||sA(this.display,268435456)||sA(this.display,536870912)||sA(this.display,67108864)||sA(this.display,134217728)},e}(),Yc=function(){function e(A,r){this.content=L(A,_c,r.content),this.quotes=L(A,Rc,r.quotes)}return e}(),qn=function(){function e(A,r){this.counterIncrement=L(A,Pc,r.counterIncrement),this.counterReset=L(A,Nc,r.counterReset)}return e}(),L=function(e,A,r){var t=new vn,n=r!==null&&typeof r<"u"?r.toString():A.initialValue;t.write(n);var o=new En(t.read());switch(A.type){case 2:var a=o.parseComponentValue();return A.parse(e,j(a)?a.value:A.initialValue);case 0:return A.parse(e,o.parseComponentValue());case 1:return A.parse(e,o.parseComponentValues());case 4:return o.parseComponentValue();case 3:switch(A.format){case"angle":return ht.parse(e,o.parseComponentValue());case"color":return WA.parse(e,o.parseComponentValue());case"image":return Qr.parse(e,o.parseComponentValue());case"length":var c=o.parseComponentValue();return XA(c)?c:lA;case"length-percentage":var s=o.parseComponentValue();return iA(s)?s:lA;case"time":return Zn.parse(e,o.parseComponentValue())}break}},$c="data-html2canvas-debug",Zc=function(e){var A=e.getAttribute($c);switch(A){case"all":return 1;case"clone":return 2;case"parse":return 3;case"render":return 4;default:return 0}},Fr=function(e,A){var r=Zc(e);return r===1||A===r},KA=function(){function e(A,r){if(this.context=A,this.textNodes=[],this.elements=[],this.flags=0,Fr(r,3))debugger;this.styles=new Wc(A,window.getComputedStyle(r,null)),Tr(r)&&(this.styles.animationDuration.some(function(t){return t>0})&&(r.style.animationDuration="0s"),this.styles.transform!==null&&(r.style.transform="none")),this.bounds=h(this.context,r),Fr(r,4)&&(this.flags|=16)}return e}(),zc="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",jn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Xe=typeof Uint8Array>"u"?[]:new Uint8Array(256),yt=0;yt<jn.length;yt++)Xe[jn.charCodeAt(yt)]=yt;for(var qc=function(e){var A=e.length*.75,r=e.length,t,n=0,o,a,c,s;e[e.length-1]==="="&&(A--,e[e.length-2]==="="&&A--);var u=typeof ArrayBuffer<"u"&&typeof Uint8Array<"u"&&typeof Uint8Array.prototype.slice<"u"?new ArrayBuffer(A):new Array(A),f=Array.isArray(u)?u:new Uint8Array(u);for(t=0;t<r;t+=4)o=Xe[e.charCodeAt(t)],a=Xe[e.charCodeAt(t+1)],c=Xe[e.charCodeAt(t+2)],s=Xe[e.charCodeAt(t+3)],f[n++]=o<<2|a>>4,f[n++]=(a&15)<<4|c>>2,f[n++]=(c&3)<<6|s&63;return u},jc=function(e){for(var A=e.length,r=[],t=0;t<A;t+=2)r.push(e[t+1]<<8|e[t]);return r},Al=function(e){for(var A=e.length,r=[],t=0;t<A;t+=4)r.push(e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]);return r},oe=5,mr=11,vr=2,el=mr-oe,Ao=65536>>oe,tl=1<<oe,Er=tl-1,rl=1024>>oe,nl=Ao+rl,ol=nl,il=32,al=ol+il,sl=65536>>mr,cl=1<<el,ll=cl-1,eo=function(e,A,r){return e.slice?e.slice(A,r):new Uint16Array(Array.prototype.slice.call(e,A,r))},Bl=function(e,A,r){return e.slice?e.slice(A,r):new Uint32Array(Array.prototype.slice.call(e,A,r))},gl=function(e,A){var r=qc(e),t=Array.isArray(r)?Al(r):new Uint32Array(r),n=Array.isArray(r)?jc(r):new Uint16Array(r),o=24,a=eo(n,o/2,t[4]/2),c=t[5]===2?eo(n,(o+t[4])/2):Bl(t,Math.ceil((o+t[4])/4));return new ul(t[0],t[1],t[2],t[3],a,c)},ul=function(){function e(A,r,t,n,o,a){this.initialValue=A,this.errorValue=r,this.highStart=t,this.highValueIndex=n,this.index=o,this.data=a}return e.prototype.get=function(A){var r;if(A>=0){if(A<55296||A>56319&&A<=65535)return r=this.index[A>>oe],r=(r<<vr)+(A&Er),this.data[r];if(A<=65535)return r=this.index[Ao+(A-55296>>oe)],r=(r<<vr)+(A&Er),this.data[r];if(A<this.highStart)return r=al-sl+(A>>mr),r=this.index[r],r+=A>>oe&ll,r=this.index[r],r=(r<<vr)+(A&Er),this.data[r];if(A<=1114111)return this.data[this.highValueIndex]}return this.errorValue},e}(),to="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",fl=typeof Uint8Array>"u"?[]:new Uint8Array(256),bt=0;bt<to.length;bt++)fl[to.charCodeAt(bt)]=bt;var wl=1,Hr=2,Ir=3,ro=4,no=5,dl=7,oo=8,yr=9,br=10,io=11,ao=12,so=13,co=14,Lr=15,hl=function(e){for(var A=[],r=0,t=e.length;r<t;){var n=e.charCodeAt(r++);if(n>=55296&&n<=56319&&r<t){var o=e.charCodeAt(r++);(o&64512)===56320?A.push(((n&1023)<<10)+(o&1023)+65536):(A.push(n),r--)}else A.push(n)}return A},Cl=function(){for(var e=[],A=0;A<arguments.length;A++)e[A]=arguments[A];if(String.fromCodePoint)return String.fromCodePoint.apply(String,e);var r=e.length;if(!r)return"";for(var t=[],n=-1,o="";++n<r;){var a=e[n];a<=65535?t.push(a):(a-=65536,t.push((a>>10)+55296,a%1024+56320)),(n+1===r||t.length>16384)&&(o+=String.fromCharCode.apply(String,t),t.length=0)}return o},Ql=gl(zc),vA="\xD7",xr="\xF7",pl=function(e){return Ql.get(e)},Ul=function(e,A,r){var t=r-2,n=A[t],o=A[r-1],a=A[r];if(o===Hr&&a===Ir)return vA;if(o===Hr||o===Ir||o===ro||a===Hr||a===Ir||a===ro)return xr;if(o===oo&&[oo,yr,io,ao].indexOf(a)!==-1||(o===io||o===yr)&&(a===yr||a===br)||(o===ao||o===br)&&a===br||a===so||a===no||a===dl||o===wl)return vA;if(o===so&&a===co){for(;n===no;)n=A[--t];if(n===co)return vA}if(o===Lr&&a===Lr){for(var c=0;n===Lr;)c++,n=A[--t];if(c%2===0)return vA}return xr},Fl=function(e){var A=hl(e),r=A.length,t=0,n=0,o=A.map(pl);return{next:function(){if(t>=r)return{done:!0,value:null};for(var a=vA;t<r&&(a=Ul(A,o,++t))===vA;);if(a!==vA||t===r){var c=Cl.apply(null,A.slice(n,t));return n=t,{value:c,done:!1}}return{done:!0,value:null}}}},ml=function(e){for(var A=Fl(e),r=[],t;!(t=A.next()).done;)t.value&&r.push(t.value.slice());return r},vl=function(e){var A=123;if(e.createRange){var r=e.createRange();if(r.getBoundingClientRect){var t=e.createElement("boundtest");t.style.height=A+"px",t.style.display="block",e.body.appendChild(t),r.selectNode(t);var n=r.getBoundingClientRect(),o=Math.round(n.height);if(e.body.removeChild(t),o===A)return!0}}return!1},El=function(e){var A=e.createElement("boundtest");A.style.width="50px",A.style.display="block",A.style.fontSize="12px",A.style.letterSpacing="0px",A.style.wordSpacing="0px",e.body.appendChild(A);var r=e.createRange();A.innerHTML=typeof"".repeat=="function"?"&#128104;".repeat(10):"";var t=A.firstChild,n=d(t.data).map(function(s){return p(s)}),o=0,a={},c=n.every(function(s,u){r.setStart(t,o),r.setEnd(t,o+s.length);var f=r.getBoundingClientRect();o+=s.length;var w=f.x>a.x||f.y>a.y;return a=f,u===0?!0:w});return e.body.removeChild(A),c},Hl=function(){return typeof new Image().crossOrigin<"u"},Il=function(){return typeof new XMLHttpRequest().responseType=="string"},yl=function(e){var A=new Image,r=e.createElement("canvas"),t=r.getContext("2d");if(!t)return!1;A.src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg'></svg>";try{t.drawImage(A,0,0),r.toDataURL()}catch{return!1}return!0},lo=function(e){return e[0]===0&&e[1]===255&&e[2]===0&&e[3]===255},bl=function(e){var A=e.createElement("canvas"),r=100;A.width=r,A.height=r;var t=A.getContext("2d");if(!t)return Promise.reject(!1);t.fillStyle="rgb(0, 255, 0)",t.fillRect(0,0,r,r);var n=new Image,o=A.toDataURL();n.src=o;var a=Mr(r,r,0,0,n);return t.fillStyle="red",t.fillRect(0,0,r,r),Bo(a).then(function(c){t.drawImage(c,0,0);var s=t.getImageData(0,0,r,r).data;t.fillStyle="red",t.fillRect(0,0,r,r);var u=e.createElement("div");return u.style.backgroundImage="url("+o+")",u.style.height=r+"px",lo(s)?Bo(Mr(r,r,0,0,u)):Promise.reject(!1)}).then(function(c){return t.drawImage(c,0,0),lo(t.getImageData(0,0,r,r).data)}).catch(function(){return!1})},Mr=function(e,A,r,t,n){var o="http://www.w3.org/2000/svg",a=document.createElementNS(o,"svg"),c=document.createElementNS(o,"foreignObject");return a.setAttributeNS(null,"width",e.toString()),a.setAttributeNS(null,"height",A.toString()),c.setAttributeNS(null,"width","100%"),c.setAttributeNS(null,"height","100%"),c.setAttributeNS(null,"x",r.toString()),c.setAttributeNS(null,"y",t.toString()),c.setAttributeNS(null,"externalResourcesRequired","true"),a.appendChild(c),c.appendChild(n),a},Bo=function(e){return new Promise(function(A,r){var t=new Image;t.onload=function(){return A(t)},t.onerror=r,t.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent(new XMLSerializer().serializeToString(e))})},BA={get SUPPORT_RANGE_BOUNDS(){var e=vl(document);return Object.defineProperty(BA,"SUPPORT_RANGE_BOUNDS",{value:e}),e},get SUPPORT_WORD_BREAKING(){var e=BA.SUPPORT_RANGE_BOUNDS&&El(document);return Object.defineProperty(BA,"SUPPORT_WORD_BREAKING",{value:e}),e},get SUPPORT_SVG_DRAWING(){var e=yl(document);return Object.defineProperty(BA,"SUPPORT_SVG_DRAWING",{value:e}),e},get SUPPORT_FOREIGNOBJECT_DRAWING(){var e=typeof Array.from=="function"&&typeof window.fetch=="function"?bl(document):Promise.resolve(!1);return Object.defineProperty(BA,"SUPPORT_FOREIGNOBJECT_DRAWING",{value:e}),e},get SUPPORT_CORS_IMAGES(){var e=Hl();return Object.defineProperty(BA,"SUPPORT_CORS_IMAGES",{value:e}),e},get SUPPORT_RESPONSE_TYPE(){var e=Il();return Object.defineProperty(BA,"SUPPORT_RESPONSE_TYPE",{value:e}),e},get SUPPORT_CORS_XHR(){var e="withCredentials"in new XMLHttpRequest;return Object.defineProperty(BA,"SUPPORT_CORS_XHR",{value:e}),e},get SUPPORT_NATIVE_TEXT_SEGMENTATION(){var e=!!(typeof Intl<"u"&&Intl.Segmenter);return Object.defineProperty(BA,"SUPPORT_NATIVE_TEXT_SEGMENTATION",{value:e}),e}},Je=function(){function e(A,r){this.text=A,this.bounds=r}return e}(),Ll=function(e,A,r,t){var n=Sl(A,r),o=[],a=0;return n.forEach(function(c){if(r.textDecorationLine.length||c.trim().length>0)if(BA.SUPPORT_RANGE_BOUNDS){var s=go(t,a,c.length).getClientRects();if(s.length>1){var u=Sr(c),f=0;u.forEach(function(C){o.push(new Je(C,Q.fromDOMRectList(e,go(t,f+a,C.length).getClientRects()))),f+=C.length})}else o.push(new Je(c,Q.fromDOMRectList(e,s)))}else{var w=t.splitText(c.length);o.push(new Je(c,xl(e,t))),t=w}else BA.SUPPORT_RANGE_BOUNDS||(t=t.splitText(c.length));a+=c.length}),o},xl=function(e,A){var r=A.ownerDocument;if(r){var t=r.createElement("html2canvaswrapper");t.appendChild(A.cloneNode(!0));var n=A.parentNode;if(n){n.replaceChild(t,A);var o=h(e,t);return t.firstChild&&n.replaceChild(t.firstChild,t),o}}return Q.EMPTY},go=function(e,A,r){var t=e.ownerDocument;if(!t)throw new Error("Node has no owner document");var n=t.createRange();return n.setStart(e,A),n.setEnd(e,A+r),n},Sr=function(e){if(BA.SUPPORT_NATIVE_TEXT_SEGMENTATION){var A=new Intl.Segmenter(void 0,{granularity:"grapheme"});return Array.from(A.segment(e)).map(function(r){return r.segment})}return ml(e)},Ml=function(e,A){if(BA.SUPPORT_NATIVE_TEXT_SEGMENTATION){var r=new Intl.Segmenter(void 0,{granularity:"word"});return Array.from(r.segment(e)).map(function(t){return t.segment})}return Dl(e,A)},Sl=function(e,A){return A.letterSpacing!==0?Sr(e):Ml(e,A)},Kl=[32,160,4961,65792,65793,4153,4241],Dl=function(e,A){for(var r=sa(e,{lineBreak:A.lineBreak,wordBreak:A.overflowWrap==="break-word"?"break-word":A.wordBreak}),t=[],n,o=function(){if(n.value){var a=n.value.slice(),c=d(a),s="";c.forEach(function(u){Kl.indexOf(u)===-1?s+=p(u):(s.length&&t.push(s),t.push(p(u)),s="")}),s.length&&t.push(s)}};!(n=r.next()).done;)o();return t},Tl=function(){function e(A,r,t){this.text=Ol(r.data,t.textTransform),this.textBounds=Ll(A,this.text,t,r)}return e}(),Ol=function(e,A){switch(A){case 1:return e.toLowerCase();case 3:return e.replace(_l,Pl);case 2:return e.toUpperCase();default:return e}},_l=/(^|\s|:|-|\(|\))([a-z])/g,Pl=function(e,A,r){return e.length>0?A+r.toUpperCase():e},uo=function(e){R(A,e);function A(r,t){var n=e.call(this,r,t)||this;return n.src=t.currentSrc||t.src,n.intrinsicWidth=t.naturalWidth,n.intrinsicHeight=t.naturalHeight,n.context.cache.addImage(n.src),n}return A}(KA),fo=function(e){R(A,e);function A(r,t){var n=e.call(this,r,t)||this;return n.canvas=t,n.intrinsicWidth=t.width,n.intrinsicHeight=t.height,n}return A}(KA),wo=function(e){R(A,e);function A(r,t){var n=e.call(this,r,t)||this,o=new XMLSerializer,a=h(r,t);return t.setAttribute("width",a.width+"px"),t.setAttribute("height",a.height+"px"),n.svg="data:image/svg+xml,"+encodeURIComponent(o.serializeToString(t)),n.intrinsicWidth=t.width.baseVal.value,n.intrinsicHeight=t.height.baseVal.value,n.context.cache.addImage(n.svg),n}return A}(KA),ho=function(e){R(A,e);function A(r,t){var n=e.call(this,r,t)||this;return n.value=t.value,n}return A}(KA),Kr=function(e){R(A,e);function A(r,t){var n=e.call(this,r,t)||this;return n.start=t.start,n.reversed=typeof t.reversed=="boolean"&&t.reversed===!0,n}return A}(KA),Nl=[{type:15,flags:0,unit:"px",number:3}],Gl=[{type:16,flags:0,number:50}],Rl=function(e){return e.width>e.height?new Q(e.left+(e.width-e.height)/2,e.top,e.height,e.height):e.width<e.height?new Q(e.left,e.top+(e.height-e.width)/2,e.width,e.width):e},kl=function(e){var A=e.type===Vl?new Array(e.value.length+1).join("\u2022"):e.value;return A.length===0?e.placeholder||"":A},Lt="checkbox",xt="radio",Vl="password",Co=707406591,Dr=function(e){R(A,e);function A(r,t){var n=e.call(this,r,t)||this;switch(n.type=t.type.toLowerCase(),n.checked=t.checked,n.value=kl(t),(n.type===Lt||n.type===xt)&&(n.styles.backgroundColor=3739148031,n.styles.borderTopColor=n.styles.borderRightColor=n.styles.borderBottomColor=n.styles.borderLeftColor=2779096575,n.styles.borderTopWidth=n.styles.borderRightWidth=n.styles.borderBottomWidth=n.styles.borderLeftWidth=1,n.styles.borderTopStyle=n.styles.borderRightStyle=n.styles.borderBottomStyle=n.styles.borderLeftStyle=1,n.styles.backgroundClip=[0],n.styles.backgroundOrigin=[0],n.bounds=Rl(n.bounds)),n.type){case Lt:n.styles.borderTopRightRadius=n.styles.borderTopLeftRadius=n.styles.borderBottomRightRadius=n.styles.borderBottomLeftRadius=Nl;break;case xt:n.styles.borderTopRightRadius=n.styles.borderTopLeftRadius=n.styles.borderBottomRightRadius=n.styles.borderBottomLeftRadius=Gl;break}return n}return A}(KA),Qo=function(e){R(A,e);function A(r,t){var n=e.call(this,r,t)||this,o=t.options[t.selectedIndex||0];return n.value=o&&o.text||"",n}return A}(KA),po=function(e){R(A,e);function A(r,t){var n=e.call(this,r,t)||this;return n.value=t.value,n}return A}(KA),Uo=function(e){R(A,e);function A(r,t){var n=e.call(this,r,t)||this;n.src=t.src,n.width=parseInt(t.width,10)||0,n.height=parseInt(t.height,10)||0,n.backgroundColor=n.styles.backgroundColor;try{if(t.contentWindow&&t.contentWindow.document&&t.contentWindow.document.documentElement){n.tree=mo(r,t.contentWindow.document.documentElement);var o=t.contentWindow.document.documentElement?ke(r,getComputedStyle(t.contentWindow.document.documentElement).backgroundColor):OA.TRANSPARENT,a=t.contentWindow.document.body?ke(r,getComputedStyle(t.contentWindow.document.body).backgroundColor):OA.TRANSPARENT;n.backgroundColor=YA(o)?YA(a)?n.styles.backgroundColor:a:o}}catch{}return n}return A}(KA),Xl=["OL","UL","MENU"],Mt=function(e,A,r,t){for(var n=A.firstChild,o=void 0;n;n=o)if(o=n.nextSibling,vo(n)&&n.data.trim().length>0)r.textNodes.push(new Tl(e,n,r.styles));else if(de(n))if(Lo(n)&&n.assignedNodes)n.assignedNodes().forEach(function(c){return Mt(e,c,r,t)});else{var a=Fo(e,n);a.styles.isVisible()&&(Jl(n,a,t)?a.flags|=4:Wl(a.styles)&&(a.flags|=2),Xl.indexOf(n.tagName)!==-1&&(a.flags|=8),r.elements.push(a),n.slot,n.shadowRoot?Mt(e,n.shadowRoot,a,t):!Kt(n)&&!Eo(n)&&!Dt(n)&&Mt(e,n,a,t))}},Fo=function(e,A){return _r(A)?new uo(e,A):Ho(A)?new fo(e,A):Eo(A)?new wo(e,A):Yl(A)?new ho(e,A):$l(A)?new Kr(e,A):Zl(A)?new Dr(e,A):Dt(A)?new Qo(e,A):Kt(A)?new po(e,A):yo(A)?new Uo(e,A):new KA(e,A)},mo=function(e,A){var r=Fo(e,A);return r.flags|=4,Mt(e,A,r,r),r},Jl=function(e,A,r){return A.styles.isPositionedWithZIndex()||A.styles.opacity<1||A.styles.isTransformed()||Or(e)&&r.styles.isTransparent()},Wl=function(e){return e.isPositioned()||e.isFloating()},vo=function(e){return e.nodeType===Node.TEXT_NODE},de=function(e){return e.nodeType===Node.ELEMENT_NODE},Tr=function(e){return de(e)&&typeof e.style<"u"&&!St(e)},St=function(e){return typeof e.className=="object"},Yl=function(e){return e.tagName==="LI"},$l=function(e){return e.tagName==="OL"},Zl=function(e){return e.tagName==="INPUT"},zl=function(e){return e.tagName==="HTML"},Eo=function(e){return e.tagName==="svg"},Or=function(e){return e.tagName==="BODY"},Ho=function(e){return e.tagName==="CANVAS"},Io=function(e){return e.tagName==="VIDEO"},_r=function(e){return e.tagName==="IMG"},yo=function(e){return e.tagName==="IFRAME"},bo=function(e){return e.tagName==="STYLE"},ql=function(e){return e.tagName==="SCRIPT"},Kt=function(e){return e.tagName==="TEXTAREA"},Dt=function(e){return e.tagName==="SELECT"},Lo=function(e){return e.tagName==="SLOT"},xo=function(e){return e.tagName.indexOf("-")>0},jl=function(){function e(){this.counters={}}return e.prototype.getCounterValue=function(A){var r=this.counters[A];return r&&r.length?r[r.length-1]:1},e.prototype.getCounterValues=function(A){var r=this.counters[A];return r||[]},e.prototype.pop=function(A){var r=this;A.forEach(function(t){return r.counters[t].pop()})},e.prototype.parse=function(A){var r=this,t=A.counterIncrement,n=A.counterReset,o=!0;t!==null&&t.forEach(function(c){var s=r.counters[c.counter];s&&c.increment!==0&&(o=!1,s.length||s.push(1),s[Math.max(0,s.length-1)]+=c.increment)});var a=[];return o&&n.forEach(function(c){var s=r.counters[c.counter];a.push(c.counter),s||(s=r.counters[c.counter]=[]),s.push(c.reset)}),a},e}(),Mo={integers:[1e3,900,500,400,100,90,50,40,10,9,5,4,1],values:["M","CM","D","CD","C","XC","L","XL","X","IX","V","IV","I"]},So={integers:[9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["\u0554","\u0553","\u0552","\u0551","\u0550","\u054F","\u054E","\u054D","\u054C","\u054B","\u054A","\u0549","\u0548","\u0547","\u0546","\u0545","\u0544","\u0543","\u0542","\u0541","\u0540","\u053F","\u053E","\u053D","\u053C","\u053B","\u053A","\u0539","\u0538","\u0537","\u0536","\u0535","\u0534","\u0533","\u0532","\u0531"]},AB={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,400,300,200,100,90,80,70,60,50,40,30,20,19,18,17,16,15,10,9,8,7,6,5,4,3,2,1],values:["\u05D9\u05F3","\u05D8\u05F3","\u05D7\u05F3","\u05D6\u05F3","\u05D5\u05F3","\u05D4\u05F3","\u05D3\u05F3","\u05D2\u05F3","\u05D1\u05F3","\u05D0\u05F3","\u05EA","\u05E9","\u05E8","\u05E7","\u05E6","\u05E4","\u05E2","\u05E1","\u05E0","\u05DE","\u05DC","\u05DB","\u05D9\u05D8","\u05D9\u05D7","\u05D9\u05D6","\u05D8\u05D6","\u05D8\u05D5","\u05D9","\u05D8","\u05D7","\u05D6","\u05D5","\u05D4","\u05D3","\u05D2","\u05D1","\u05D0"]},eB={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["\u10F5","\u10F0","\u10EF","\u10F4","\u10EE","\u10ED","\u10EC","\u10EB","\u10EA","\u10E9","\u10E8","\u10E7","\u10E6","\u10E5","\u10E4","\u10F3","\u10E2","\u10E1","\u10E0","\u10DF","\u10DE","\u10DD","\u10F2","\u10DC","\u10DB","\u10DA","\u10D9","\u10D8","\u10D7","\u10F1","\u10D6","\u10D5","\u10D4","\u10D3","\u10D2","\u10D1","\u10D0"]},he=function(e,A,r,t,n,o){return e<A||e>r?Ye(e,n,o.length>0):t.integers.reduce(function(a,c,s){for(;e>=c;)e-=c,a+=t.values[s];return a},"")+o},Ko=function(e,A,r,t){var n="";do r||e--,n=t(e)+n,e/=A;while(e*A>=A);return n},oA=function(e,A,r,t,n){var o=r-A+1;return(e<0?"-":"")+(Ko(Math.abs(e),o,t,function(a){return p(Math.floor(a%o)+A)})+n)},ie=function(e,A,r){r===void 0&&(r=". ");var t=A.length;return Ko(Math.abs(e),t,!1,function(n){return A[Math.floor(n%t)]})+r},Ce=1,ZA=2,zA=4,We=8,_A=function(e,A,r,t,n,o){if(e<-9999||e>9999)return Ye(e,4,n.length>0);var a=Math.abs(e),c=n;if(a===0)return A[0]+c;for(var s=0;a>0&&s<=4;s++){var u=a%10;u===0&&sA(o,Ce)&&c!==""?c=A[u]+c:u>1||u===1&&s===0||u===1&&s===1&&sA(o,ZA)||u===1&&s===1&&sA(o,zA)&&e>100||u===1&&s>1&&sA(o,We)?c=A[u]+(s>0?r[s-1]:"")+c:u===1&&s>0&&(c=r[s-1]+c),a=Math.floor(a/10)}return(e<0?t:"")+c},Do="\u5341\u767E\u5343\u842C",To="\u62FE\u4F70\u4EDF\u842C",Oo="\u30DE\u30A4\u30CA\u30B9",Pr="\uB9C8\uC774\uB108\uC2A4",Ye=function(e,A,r){var t=r?". ":"",n=r?"\u3001":"",o=r?", ":"",a=r?" ":"";switch(A){case 0:return"\u2022"+a;case 1:return"\u25E6"+a;case 2:return"\u25FE"+a;case 5:var c=oA(e,48,57,!0,t);return c.length<4?"0"+c:c;case 4:return ie(e,"\u3007\u4E00\u4E8C\u4E09\u56DB\u4E94\u516D\u4E03\u516B\u4E5D",n);case 6:return he(e,1,3999,Mo,3,t).toLowerCase();case 7:return he(e,1,3999,Mo,3,t);case 8:return oA(e,945,969,!1,t);case 9:return oA(e,97,122,!1,t);case 10:return oA(e,65,90,!1,t);case 11:return oA(e,1632,1641,!0,t);case 12:case 49:return he(e,1,9999,So,3,t);case 35:return he(e,1,9999,So,3,t).toLowerCase();case 13:return oA(e,2534,2543,!0,t);case 14:case 30:return oA(e,6112,6121,!0,t);case 15:return ie(e,"\u5B50\u4E11\u5BC5\u536F\u8FB0\u5DF3\u5348\u672A\u7533\u9149\u620C\u4EA5",n);case 16:return ie(e,"\u7532\u4E59\u4E19\u4E01\u620A\u5DF1\u5E9A\u8F9B\u58EC\u7678",n);case 17:case 48:return _A(e,"\u96F6\u4E00\u4E8C\u4E09\u56DB\u4E94\u516D\u4E03\u516B\u4E5D",Do,"\u8CA0",n,ZA|zA|We);case 47:return _A(e,"\u96F6\u58F9\u8CB3\u53C3\u8086\u4F0D\u9678\u67D2\u634C\u7396",To,"\u8CA0",n,Ce|ZA|zA|We);case 42:return _A(e,"\u96F6\u4E00\u4E8C\u4E09\u56DB\u4E94\u516D\u4E03\u516B\u4E5D",Do,"\u8D1F",n,ZA|zA|We);case 41:return _A(e,"\u96F6\u58F9\u8D30\u53C1\u8086\u4F0D\u9646\u67D2\u634C\u7396",To,"\u8D1F",n,Ce|ZA|zA|We);case 26:return _A(e,"\u3007\u4E00\u4E8C\u4E09\u56DB\u4E94\u516D\u4E03\u516B\u4E5D","\u5341\u767E\u5343\u4E07",Oo,n,0);case 25:return _A(e,"\u96F6\u58F1\u5F10\u53C2\u56DB\u4F0D\u516D\u4E03\u516B\u4E5D","\u62FE\u767E\u5343\u4E07",Oo,n,Ce|ZA|zA);case 31:return _A(e,"\uC601\uC77C\uC774\uC0BC\uC0AC\uC624\uC721\uCE60\uD314\uAD6C","\uC2ED\uBC31\uCC9C\uB9CC",Pr,o,Ce|ZA|zA);case 33:return _A(e,"\u96F6\u4E00\u4E8C\u4E09\u56DB\u4E94\u516D\u4E03\u516B\u4E5D","\u5341\u767E\u5343\u842C",Pr,o,0);case 32:return _A(e,"\u96F6\u58F9\u8CB3\u53C3\u56DB\u4E94\u516D\u4E03\u516B\u4E5D","\u62FE\u767E\u5343",Pr,o,Ce|ZA|zA);case 18:return oA(e,2406,2415,!0,t);case 20:return he(e,1,19999,eB,3,t);case 21:return oA(e,2790,2799,!0,t);case 22:return oA(e,2662,2671,!0,t);case 22:return he(e,1,10999,AB,3,t);case 23:return ie(e,"\u3042\u3044\u3046\u3048\u304A\u304B\u304D\u304F\u3051\u3053\u3055\u3057\u3059\u305B\u305D\u305F\u3061\u3064\u3066\u3068\u306A\u306B\u306C\u306D\u306E\u306F\u3072\u3075\u3078\u307B\u307E\u307F\u3080\u3081\u3082\u3084\u3086\u3088\u3089\u308A\u308B\u308C\u308D\u308F\u3090\u3091\u3092\u3093");case 24:return ie(e,"\u3044\u308D\u306F\u306B\u307B\u3078\u3068\u3061\u308A\u306C\u308B\u3092\u308F\u304B\u3088\u305F\u308C\u305D\u3064\u306D\u306A\u3089\u3080\u3046\u3090\u306E\u304A\u304F\u3084\u307E\u3051\u3075\u3053\u3048\u3066\u3042\u3055\u304D\u3086\u3081\u307F\u3057\u3091\u3072\u3082\u305B\u3059");case 27:return oA(e,3302,3311,!0,t);case 28:return ie(e,"\u30A2\u30A4\u30A6\u30A8\u30AA\u30AB\u30AD\u30AF\u30B1\u30B3\u30B5\u30B7\u30B9\u30BB\u30BD\u30BF\u30C1\u30C4\u30C6\u30C8\u30CA\u30CB\u30CC\u30CD\u30CE\u30CF\u30D2\u30D5\u30D8\u30DB\u30DE\u30DF\u30E0\u30E1\u30E2\u30E4\u30E6\u30E8\u30E9\u30EA\u30EB\u30EC\u30ED\u30EF\u30F0\u30F1\u30F2\u30F3",n);case 29:return ie(e,"\u30A4\u30ED\u30CF\u30CB\u30DB\u30D8\u30C8\u30C1\u30EA\u30CC\u30EB\u30F2\u30EF\u30AB\u30E8\u30BF\u30EC\u30BD\u30C4\u30CD\u30CA\u30E9\u30E0\u30A6\u30F0\u30CE\u30AA\u30AF\u30E4\u30DE\u30B1\u30D5\u30B3\u30A8\u30C6\u30A2\u30B5\u30AD\u30E6\u30E1\u30DF\u30B7\u30F1\u30D2\u30E2\u30BB\u30B9",n);case 34:return oA(e,3792,3801,!0,t);case 37:return oA(e,6160,6169,!0,t);case 38:return oA(e,4160,4169,!0,t);case 39:return oA(e,2918,2927,!0,t);case 40:return oA(e,1776,1785,!0,t);case 43:return oA(e,3046,3055,!0,t);case 44:return oA(e,3174,3183,!0,t);case 45:return oA(e,3664,3673,!0,t);case 46:return oA(e,3872,3881,!0,t);case 3:default:return oA(e,48,57,!0,t)}},_o="data-html2canvas-ignore",Po=function(){function e(A,r,t){if(this.context=A,this.options=t,this.scrolledElements=[],this.referenceElement=r,this.counters=new jl,this.quoteDepth=0,!r.ownerDocument)throw new Error("Cloned element does not have an owner document");this.documentElement=this.cloneNode(r.ownerDocument.documentElement,!1)}return e.prototype.toIFrame=function(A,r){var t=this,n=tB(A,r);if(!n.contentWindow)return Promise.reject("Unable to find iframe window");var o=A.defaultView.pageXOffset,a=A.defaultView.pageYOffset,c=n.contentWindow,s=c.document,u=oB(n).then(function(){return l(t,void 0,void 0,function(){var f,w;return B(this,function(C){switch(C.label){case 0:return this.scrolledElements.forEach(cB),c&&(c.scrollTo(r.left,r.top),/(iPad|iPhone|iPod)/g.test(navigator.userAgent)&&(c.scrollY!==r.top||c.scrollX!==r.left)&&(this.context.logger.warn("Unable to restore scroll position for cloned document"),this.context.windowBounds=this.context.windowBounds.add(c.scrollX-r.left,c.scrollY-r.top,0,0))),f=this.options.onclone,w=this.clonedReferenceElement,typeof w>"u"?[2,Promise.reject("Error finding the "+this.referenceElement.nodeName+" in the cloned document")]:s.fonts&&s.fonts.ready?[4,s.fonts.ready]:[3,2];case 1:C.sent(),C.label=2;case 2:return/(AppleWebKit)/g.test(navigator.userAgent)?[4,nB(s)]:[3,4];case 3:C.sent(),C.label=4;case 4:return typeof f=="function"?[2,Promise.resolve().then(function(){return f(s,w)}).then(function(){return n})]:[2,n]}})})});return s.open(),s.write(aB(document.doctype)+"<html></html>"),sB(this.referenceElement.ownerDocument,o,a),s.replaceChild(s.adoptNode(this.documentElement),s.documentElement),s.close(),u},e.prototype.createElementClone=function(A){if(Fr(A,2))debugger;if(Ho(A))return this.createCanvasClone(A);if(Io(A))return this.createVideoClone(A);if(bo(A))return this.createStyleClone(A);var r=A.cloneNode(!1);return _r(r)&&(_r(A)&&A.currentSrc&&A.currentSrc!==A.src&&(r.src=A.currentSrc,r.srcset=""),r.loading==="lazy"&&(r.loading="eager")),xo(r)?this.createCustomElementClone(r):r},e.prototype.createCustomElementClone=function(A){var r=document.createElement("html2canvascustomelement");return Nr(A.style,r),r},e.prototype.createStyleClone=function(A){try{var r=A.sheet;if(r&&r.cssRules){var t=[].slice.call(r.cssRules,0).reduce(function(o,a){return a&&typeof a.cssText=="string"?o+a.cssText:o},""),n=A.cloneNode(!1);return n.textContent=t,n}}catch(o){if(this.context.logger.error("Unable to access cssRules property",o),o.name!=="SecurityError")throw o}return A.cloneNode(!1)},e.prototype.createCanvasClone=function(A){var r;if(this.options.inlineImages&&A.ownerDocument){var t=A.ownerDocument.createElement("img");try{return t.src=A.toDataURL(),t}catch{this.context.logger.info("Unable to inline canvas contents, canvas is tainted",A)}}var n=A.cloneNode(!1);try{n.width=A.width,n.height=A.height;var o=A.getContext("2d"),a=n.getContext("2d");if(a)if(!this.options.allowTaint&&o)a.putImageData(o.getImageData(0,0,A.width,A.height),0,0);else{var c=(r=A.getContext("webgl2"))!==null&&r!==void 0?r:A.getContext("webgl");if(c){var s=c.getContextAttributes();s?.preserveDrawingBuffer===!1&&this.context.logger.warn("Unable to clone WebGL context as it has preserveDrawingBuffer=false",A)}a.drawImage(A,0,0)}return n}catch{this.context.logger.info("Unable to clone canvas as it is tainted",A)}return n},e.prototype.createVideoClone=function(A){var r=A.ownerDocument.createElement("canvas");r.width=A.offsetWidth,r.height=A.offsetHeight;var t=r.getContext("2d");try{return t&&(t.drawImage(A,0,0,r.width,r.height),this.options.allowTaint||t.getImageData(0,0,r.width,r.height)),r}catch{this.context.logger.info("Unable to clone video as it is tainted",A)}var n=A.ownerDocument.createElement("canvas");return n.width=A.offsetWidth,n.height=A.offsetHeight,n},e.prototype.appendChildNode=function(A,r,t){(!de(r)||!ql(r)&&!r.hasAttribute(_o)&&(typeof this.options.ignoreElements!="function"||!this.options.ignoreElements(r)))&&(!this.options.copyStyles||!de(r)||!bo(r))&&A.appendChild(this.cloneNode(r,t))},e.prototype.cloneChildNodes=function(A,r,t){for(var n=this,o=A.shadowRoot?A.shadowRoot.firstChild:A.firstChild;o;o=o.nextSibling)if(de(o)&&Lo(o)&&typeof o.assignedNodes=="function"){var a=o.assignedNodes();a.length&&a.forEach(function(c){return n.appendChildNode(r,c,t)})}else this.appendChildNode(r,o,t)},e.prototype.cloneNode=function(A,r){if(vo(A))return document.createTextNode(A.data);if(!A.ownerDocument)return A.cloneNode(!1);var t=A.ownerDocument.defaultView;if(t&&de(A)&&(Tr(A)||St(A))){var n=this.createElementClone(A);n.style.transitionProperty="none";var o=t.getComputedStyle(A),a=t.getComputedStyle(A,":before"),c=t.getComputedStyle(A,":after");this.referenceElement===A&&Tr(n)&&(this.clonedReferenceElement=n),Or(n)&&gB(n);var s=this.counters.parse(new qn(this.context,o)),u=this.resolvePseudoContent(A,n,a,Tt.BEFORE);xo(A)&&(r=!0),Io(A)||this.cloneChildNodes(A,n,r),u&&n.insertBefore(u,n.firstChild);var f=this.resolvePseudoContent(A,n,c,Tt.AFTER);return f&&n.appendChild(f),this.counters.pop(s),(o&&(this.options.copyStyles||St(A))&&!yo(A)||r)&&Nr(o,n),(A.scrollTop!==0||A.scrollLeft!==0)&&this.scrolledElements.push([n,A.scrollLeft,A.scrollTop]),(Kt(A)||Dt(A))&&(Kt(n)||Dt(n))&&(n.value=A.value),n}return A.cloneNode(!1)},e.prototype.resolvePseudoContent=function(A,r,t,n){var o=this;if(t){var a=t.content,c=r.ownerDocument;if(!(!c||!a||a==="none"||a==="-moz-alt-content"||t.display==="none")){this.counters.parse(new qn(this.context,t));var s=new Yc(this.context,t),u=c.createElement("html2canvaspseudoelement");Nr(t,u),s.content.forEach(function(w){if(w.type===0)u.appendChild(c.createTextNode(w.value));else if(w.type===22){var C=c.createElement("img");C.src=w.value,C.style.opacity="1",u.appendChild(C)}else if(w.type===18){if(w.name==="attr"){var b=w.values.filter(j);b.length&&u.appendChild(c.createTextNode(A.getAttribute(b[0].value)||""))}else if(w.name==="counter"){var U=w.values.filter(we),E=U[0],N=U[1];if(E&&j(E)){var S=o.counters.getCounterValue(E.value),x=N&&j(N)?Ur.parse(o.context,N.value):3;u.appendChild(c.createTextNode(Ye(S,x,!1)))}}else if(w.name==="counters"){var Z=w.values.filter(we),E=Z[0],G=Z[1],N=Z[2];if(E&&j(E)){var O=o.counters.getCounterValues(E.value),y=N&&j(N)?Ur.parse(o.context,N.value):3,Y=G&&G.type===0?G.value:"",$=O.map(function(hA){return Ye(hA,y,!1)}).join(Y);u.appendChild(c.createTextNode($))}}}else if(w.type===20)switch(w.value){case"open-quote":u.appendChild(c.createTextNode(zn(s.quotes,o.quoteDepth++,!0)));break;case"close-quote":u.appendChild(c.createTextNode(zn(s.quotes,--o.quoteDepth,!1)));break;default:u.appendChild(c.createTextNode(w.value))}}),u.className=Gr+" "+Rr;var f=n===Tt.BEFORE?" "+Gr:" "+Rr;return St(r)?r.className.baseValue+=f:r.className+=f,u}}},e.destroy=function(A){return A.parentNode?(A.parentNode.removeChild(A),!0):!1},e}(),Tt=function(e){return e[e.BEFORE=0]="BEFORE",e[e.AFTER=1]="AFTER",e}(Tt||{}),tB=function(e,A){var r=e.createElement("iframe");return r.className="html2canvas-container",r.style.visibility="hidden",r.style.position="fixed",r.style.left="-10000px",r.style.top="0px",r.style.border="0",r.width=A.width.toString(),r.height=A.height.toString(),r.scrolling="no",r.setAttribute(_o,"true"),e.body.appendChild(r),r},rB=function(e){return new Promise(function(A){if(e.complete){A();return}if(!e.src){A();return}e.onload=A,e.onerror=A})},nB=function(e){return Promise.all([].slice.call(e.images,0).map(rB))},oB=function(e){return new Promise(function(A,r){var t=e.contentWindow;if(!t)return r("No window assigned for iframe");var n=t.document;t.onload=e.onload=function(){t.onload=e.onload=null;var o=setInterval(function(){n.body.childNodes.length>0&&n.readyState==="complete"&&(clearInterval(o),A(e))},50)}})},iB=["all","d","content"],Nr=function(e,A){for(var r=e.length-1;r>=0;r--){var t=e.item(r);iB.indexOf(t)===-1&&A.style.setProperty(t,e.getPropertyValue(t))}return A},aB=function(e){var A="";return e&&(A+="<!DOCTYPE ",e.name&&(A+=e.name),e.internalSubset&&(A+=e.internalSubset),e.publicId&&(A+='"'+e.publicId+'"'),e.systemId&&(A+='"'+e.systemId+'"'),A+=">"),A},sB=function(e,A,r){e&&e.defaultView&&(A!==e.defaultView.pageXOffset||r!==e.defaultView.pageYOffset)&&e.defaultView.scrollTo(A,r)},cB=function(e){var A=e[0],r=e[1],t=e[2];A.scrollLeft=r,A.scrollTop=t},lB=":before",BB=":after",Gr="___html2canvas___pseudoelement_before",Rr="___html2canvas___pseudoelement_after",No=`{
    content: "" !important;
    display: none !important;
}`,gB=function(e){uB(e,"."+Gr+lB+No+`
         .`+Rr+BB+No)},uB=function(e,A){var r=e.ownerDocument;if(r){var t=r.createElement("style");t.textContent=A,e.appendChild(t)}},Go=function(){function e(){}return e.getOrigin=function(A){var r=e._link;return r?(r.href=A,r.href=r.href,r.protocol+r.hostname+r.port):"about:blank"},e.isSameOrigin=function(A){return e.getOrigin(A)===e._origin},e.setContext=function(A){e._link=A.document.createElement("a"),e._origin=e.getOrigin(A.location.href)},e._origin="about:blank",e}(),fB=function(){function e(A,r){this.context=A,this._options=r,this._cache={}}return e.prototype.addImage=function(A){var r=Promise.resolve();return this.has(A)||(Vr(A)||CB(A))&&(this._cache[A]=this.loadImage(A)).catch(function(){}),r},e.prototype.match=function(A){return this._cache[A]},e.prototype.loadImage=function(A){return l(this,void 0,void 0,function(){var r,t,n,o,a=this;return B(this,function(c){switch(c.label){case 0:return r=Go.isSameOrigin(A),t=!kr(A)&&this._options.useCORS===!0&&BA.SUPPORT_CORS_IMAGES&&!r,n=!kr(A)&&!r&&!Vr(A)&&typeof this._options.proxy=="string"&&BA.SUPPORT_CORS_XHR&&!t,!r&&this._options.allowTaint===!1&&!kr(A)&&!Vr(A)&&!n&&!t?[2]:(o=A,n?[4,this.proxy(o)]:[3,2]);case 1:o=c.sent(),c.label=2;case 2:return this.context.logger.debug("Added image "+A.substring(0,256)),[4,new Promise(function(s,u){var f=new Image;f.onload=function(){return s(f)},f.onerror=u,(QB(o)||t)&&(f.crossOrigin="anonymous"),f.src=o,f.complete===!0&&setTimeout(function(){return s(f)},500),a._options.imageTimeout>0&&setTimeout(function(){return u("Timed out ("+a._options.imageTimeout+"ms) loading image")},a._options.imageTimeout)})];case 3:return[2,c.sent()]}})})},e.prototype.has=function(A){return typeof this._cache[A]<"u"},e.prototype.keys=function(){return Promise.resolve(Object.keys(this._cache))},e.prototype.proxy=function(A){var r=this,t=this._options.proxy;if(!t)throw new Error("No proxy defined");var n=A.substring(0,256);return new Promise(function(o,a){var c=BA.SUPPORT_RESPONSE_TYPE?"blob":"text",s=new XMLHttpRequest;s.onload=function(){if(s.status===200)if(c==="text")o(s.response);else{var w=new FileReader;w.addEventListener("load",function(){return o(w.result)},!1),w.addEventListener("error",function(C){return a(C)},!1),w.readAsDataURL(s.response)}else a("Failed to proxy resource "+n+" with status code "+s.status)},s.onerror=a;var u=t.indexOf("?")>-1?"&":"?";if(s.open("GET",""+t+u+"url="+encodeURIComponent(A)+"&responseType="+c),c!=="text"&&s instanceof XMLHttpRequest&&(s.responseType=c),r._options.imageTimeout){var f=r._options.imageTimeout;s.timeout=f,s.ontimeout=function(){return a("Timed out ("+f+"ms) proxying "+n)}}s.send()})},e}(),wB=/^data:image\/svg\+xml/i,dB=/^data:image\/.*;base64,/i,hB=/^data:image\/.*/i,CB=function(e){return BA.SUPPORT_SVG_DRAWING||!pB(e)},kr=function(e){return hB.test(e)},QB=function(e){return dB.test(e)},Vr=function(e){return e.substr(0,4)==="blob"},pB=function(e){return e.substr(-3).toLowerCase()==="svg"||wB.test(e)},H=function(){function e(A,r){this.type=0,this.x=A,this.y=r}return e.prototype.add=function(A,r){return new e(this.x+A,this.y+r)},e}(),Qe=function(e,A,r){return new H(e.x+(A.x-e.x)*r,e.y+(A.y-e.y)*r)},Ot=function(){function e(A,r,t,n){this.type=1,this.start=A,this.startControl=r,this.endControl=t,this.end=n}return e.prototype.subdivide=function(A,r){var t=Qe(this.start,this.startControl,A),n=Qe(this.startControl,this.endControl,A),o=Qe(this.endControl,this.end,A),a=Qe(t,n,A),c=Qe(n,o,A),s=Qe(a,c,A);return r?new e(this.start,t,a,s):new e(s,c,o,this.end)},e.prototype.add=function(A,r){return new e(this.start.add(A,r),this.startControl.add(A,r),this.endControl.add(A,r),this.end.add(A,r))},e.prototype.reverse=function(){return new e(this.end,this.endControl,this.startControl,this.start)},e}(),EA=function(e){return e.type===1},UB=function(){function e(A){var r=A.styles,t=A.bounds,n=Re(r.borderTopLeftRadius,t.width,t.height),o=n[0],a=n[1],c=Re(r.borderTopRightRadius,t.width,t.height),s=c[0],u=c[1],f=Re(r.borderBottomRightRadius,t.width,t.height),w=f[0],C=f[1],b=Re(r.borderBottomLeftRadius,t.width,t.height),U=b[0],E=b[1],N=[];N.push((o+s)/t.width),N.push((U+w)/t.width),N.push((a+E)/t.height),N.push((u+C)/t.height);var S=Math.max.apply(Math,N);S>1&&(o/=S,a/=S,s/=S,u/=S,w/=S,C/=S,U/=S,E/=S);var x=t.width-s,Z=t.height-C,G=t.width-w,O=t.height-E,y=r.borderTopWidth,Y=r.borderRightWidth,$=r.borderBottomWidth,_=r.borderLeftWidth,aA=tA(r.paddingTop,A.bounds.width),hA=tA(r.paddingRight,A.bounds.width),FA=tA(r.paddingBottom,A.bounds.width),AA=tA(r.paddingLeft,A.bounds.width);this.topLeftBorderDoubleOuterBox=o>0||a>0?rA(t.left+_/3,t.top+y/3,o-_/3,a-y/3,q.TOP_LEFT):new H(t.left+_/3,t.top+y/3),this.topRightBorderDoubleOuterBox=o>0||a>0?rA(t.left+x,t.top+y/3,s-Y/3,u-y/3,q.TOP_RIGHT):new H(t.left+t.width-Y/3,t.top+y/3),this.bottomRightBorderDoubleOuterBox=w>0||C>0?rA(t.left+G,t.top+Z,w-Y/3,C-$/3,q.BOTTOM_RIGHT):new H(t.left+t.width-Y/3,t.top+t.height-$/3),this.bottomLeftBorderDoubleOuterBox=U>0||E>0?rA(t.left+_/3,t.top+O,U-_/3,E-$/3,q.BOTTOM_LEFT):new H(t.left+_/3,t.top+t.height-$/3),this.topLeftBorderDoubleInnerBox=o>0||a>0?rA(t.left+_*2/3,t.top+y*2/3,o-_*2/3,a-y*2/3,q.TOP_LEFT):new H(t.left+_*2/3,t.top+y*2/3),this.topRightBorderDoubleInnerBox=o>0||a>0?rA(t.left+x,t.top+y*2/3,s-Y*2/3,u-y*2/3,q.TOP_RIGHT):new H(t.left+t.width-Y*2/3,t.top+y*2/3),this.bottomRightBorderDoubleInnerBox=w>0||C>0?rA(t.left+G,t.top+Z,w-Y*2/3,C-$*2/3,q.BOTTOM_RIGHT):new H(t.left+t.width-Y*2/3,t.top+t.height-$*2/3),this.bottomLeftBorderDoubleInnerBox=U>0||E>0?rA(t.left+_*2/3,t.top+O,U-_*2/3,E-$*2/3,q.BOTTOM_LEFT):new H(t.left+_*2/3,t.top+t.height-$*2/3),this.topLeftBorderStroke=o>0||a>0?rA(t.left+_/2,t.top+y/2,o-_/2,a-y/2,q.TOP_LEFT):new H(t.left+_/2,t.top+y/2),this.topRightBorderStroke=o>0||a>0?rA(t.left+x,t.top+y/2,s-Y/2,u-y/2,q.TOP_RIGHT):new H(t.left+t.width-Y/2,t.top+y/2),this.bottomRightBorderStroke=w>0||C>0?rA(t.left+G,t.top+Z,w-Y/2,C-$/2,q.BOTTOM_RIGHT):new H(t.left+t.width-Y/2,t.top+t.height-$/2),this.bottomLeftBorderStroke=U>0||E>0?rA(t.left+_/2,t.top+O,U-_/2,E-$/2,q.BOTTOM_LEFT):new H(t.left+_/2,t.top+t.height-$/2),this.topLeftBorderBox=o>0||a>0?rA(t.left,t.top,o,a,q.TOP_LEFT):new H(t.left,t.top),this.topRightBorderBox=s>0||u>0?rA(t.left+x,t.top,s,u,q.TOP_RIGHT):new H(t.left+t.width,t.top),this.bottomRightBorderBox=w>0||C>0?rA(t.left+G,t.top+Z,w,C,q.BOTTOM_RIGHT):new H(t.left+t.width,t.top+t.height),this.bottomLeftBorderBox=U>0||E>0?rA(t.left,t.top+O,U,E,q.BOTTOM_LEFT):new H(t.left,t.top+t.height),this.topLeftPaddingBox=o>0||a>0?rA(t.left+_,t.top+y,Math.max(0,o-_),Math.max(0,a-y),q.TOP_LEFT):new H(t.left+_,t.top+y),this.topRightPaddingBox=s>0||u>0?rA(t.left+Math.min(x,t.width-Y),t.top+y,x>t.width+Y?0:Math.max(0,s-Y),Math.max(0,u-y),q.TOP_RIGHT):new H(t.left+t.width-Y,t.top+y),this.bottomRightPaddingBox=w>0||C>0?rA(t.left+Math.min(G,t.width-_),t.top+Math.min(Z,t.height-$),Math.max(0,w-Y),Math.max(0,C-$),q.BOTTOM_RIGHT):new H(t.left+t.width-Y,t.top+t.height-$),this.bottomLeftPaddingBox=U>0||E>0?rA(t.left+_,t.top+Math.min(O,t.height-$),Math.max(0,U-_),Math.max(0,E-$),q.BOTTOM_LEFT):new H(t.left+_,t.top+t.height-$),this.topLeftContentBox=o>0||a>0?rA(t.left+_+AA,t.top+y+aA,Math.max(0,o-(_+AA)),Math.max(0,a-(y+aA)),q.TOP_LEFT):new H(t.left+_+AA,t.top+y+aA),this.topRightContentBox=s>0||u>0?rA(t.left+Math.min(x,t.width+_+AA),t.top+y+aA,x>t.width+_+AA?0:s-_+AA,u-(y+aA),q.TOP_RIGHT):new H(t.left+t.width-(Y+hA),t.top+y+aA),this.bottomRightContentBox=w>0||C>0?rA(t.left+Math.min(G,t.width-(_+AA)),t.top+Math.min(Z,t.height+y+aA),Math.max(0,w-(Y+hA)),C-($+FA),q.BOTTOM_RIGHT):new H(t.left+t.width-(Y+hA),t.top+t.height-($+FA)),this.bottomLeftContentBox=U>0||E>0?rA(t.left+_+AA,t.top+O,Math.max(0,U-(_+AA)),E-($+FA),q.BOTTOM_LEFT):new H(t.left+_+AA,t.top+t.height-($+FA))}return e}(),q=function(e){return e[e.TOP_LEFT=0]="TOP_LEFT",e[e.TOP_RIGHT=1]="TOP_RIGHT",e[e.BOTTOM_RIGHT=2]="BOTTOM_RIGHT",e[e.BOTTOM_LEFT=3]="BOTTOM_LEFT",e}(q||{}),rA=function(e,A,r,t,n){var o=4*((Math.sqrt(2)-1)/3),a=r*o,c=t*o,s=e+r,u=A+t;switch(n){case q.TOP_LEFT:return new Ot(new H(e,u),new H(e,u-c),new H(s-a,A),new H(s,A));case q.TOP_RIGHT:return new Ot(new H(e,A),new H(e+a,A),new H(s,u-c),new H(s,u));case q.BOTTOM_RIGHT:return new Ot(new H(s,A),new H(s,A+c),new H(e+a,u),new H(e,u));case q.BOTTOM_LEFT:default:return new Ot(new H(s,u),new H(s-a,u),new H(e,A+c),new H(e,A))}},_t=function(e){return[e.topLeftBorderBox,e.topRightBorderBox,e.bottomRightBorderBox,e.bottomLeftBorderBox]},FB=function(e){return[e.topLeftContentBox,e.topRightContentBox,e.bottomRightContentBox,e.bottomLeftContentBox]},Pt=function(e){return[e.topLeftPaddingBox,e.topRightPaddingBox,e.bottomRightPaddingBox,e.bottomLeftPaddingBox]},mB=function(){function e(A,r,t){this.offsetX=A,this.offsetY=r,this.matrix=t,this.type=0,this.target=6}return e}(),Nt=function(){function e(A,r){this.path=A,this.target=r,this.type=1}return e}(),vB=function(){function e(A){this.opacity=A,this.type=2,this.target=6}return e}(),EB=function(e){return e.type===0},Ro=function(e){return e.type===1},HB=function(e){return e.type===2},ko=function(e,A){return e.length===A.length?e.some(function(r,t){return r===A[t]}):!1},IB=function(e,A,r,t,n){return e.map(function(o,a){switch(a){case 0:return o.add(A,r);case 1:return o.add(A+t,r);case 2:return o.add(A+t,r+n);case 3:return o.add(A,r+n)}return o})},Vo=function(){function e(A){this.element=A,this.inlineLevel=[],this.nonInlineLevel=[],this.negativeZIndex=[],this.zeroOrAutoZIndexOrTransformedOrOpacity=[],this.positiveZIndex=[],this.nonPositionedFloats=[],this.nonPositionedInlineLevel=[]}return e}(),Xo=function(){function e(A,r){if(this.container=A,this.parent=r,this.effects=[],this.curves=new UB(this.container),this.container.styles.opacity<1&&this.effects.push(new vB(this.container.styles.opacity)),this.container.styles.transform!==null){var t=this.container.bounds.left+this.container.styles.transformOrigin[0].number,n=this.container.bounds.top+this.container.styles.transformOrigin[1].number,o=this.container.styles.transform;this.effects.push(new mB(t,n,o))}if(this.container.styles.overflowX!==0){var a=_t(this.curves),c=Pt(this.curves);ko(a,c)?this.effects.push(new Nt(a,6)):(this.effects.push(new Nt(a,2)),this.effects.push(new Nt(c,4)))}}return e.prototype.getEffects=function(A){for(var r=[2,3].indexOf(this.container.styles.position)===-1,t=this.parent,n=this.effects.slice(0);t;){var o=t.effects.filter(function(s){return!Ro(s)});if(r||t.container.styles.position!==0||!t.parent){if(n.unshift.apply(n,o),r=[2,3].indexOf(t.container.styles.position)===-1,t.container.styles.overflowX!==0){var a=_t(t.curves),c=Pt(t.curves);ko(a,c)||n.unshift(new Nt(c,6))}}else n.unshift.apply(n,o);t=t.parent}return n.filter(function(s){return sA(s.target,A)})},e}(),Xr=function(e,A,r,t){e.container.elements.forEach(function(n){var o=sA(n.flags,4),a=sA(n.flags,2),c=new Xo(n,e);sA(n.styles.display,2048)&&t.push(c);var s=sA(n.flags,8)?[]:t;if(o||a){var u=o||n.styles.isPositioned()?r:A,f=new Vo(c);if(n.styles.isPositioned()||n.styles.opacity<1||n.styles.isTransformed()){var w=n.styles.zIndex.order;if(w<0){var C=0;u.negativeZIndex.some(function(U,E){return w>U.element.container.styles.zIndex.order?(C=E,!1):C>0}),u.negativeZIndex.splice(C,0,f)}else if(w>0){var b=0;u.positiveZIndex.some(function(U,E){return w>=U.element.container.styles.zIndex.order?(b=E+1,!1):b>0}),u.positiveZIndex.splice(b,0,f)}else u.zeroOrAutoZIndexOrTransformedOrOpacity.push(f)}else n.styles.isFloating()?u.nonPositionedFloats.push(f):u.nonPositionedInlineLevel.push(f);Xr(c,f,o?f:r,s)}else n.styles.isInlineLevel()?A.inlineLevel.push(c):A.nonInlineLevel.push(c),Xr(c,A,r,s);sA(n.flags,8)&&Jo(n,s)})},Jo=function(e,A){for(var r=e instanceof Kr?e.start:1,t=e instanceof Kr?e.reversed:!1,n=0;n<A.length;n++){var o=A[n];o.container instanceof ho&&typeof o.container.value=="number"&&o.container.value!==0&&(r=o.container.value),o.listValue=Ye(r,o.container.styles.listStyleType,!0),r+=t?-1:1}},yB=function(e){var A=new Xo(e,null),r=new Vo(A),t=[];return Xr(A,r,r,t),Jo(A.container,t),r},Wo=function(e,A){switch(A){case 0:return HA(e.topLeftBorderBox,e.topLeftPaddingBox,e.topRightBorderBox,e.topRightPaddingBox);case 1:return HA(e.topRightBorderBox,e.topRightPaddingBox,e.bottomRightBorderBox,e.bottomRightPaddingBox);case 2:return HA(e.bottomRightBorderBox,e.bottomRightPaddingBox,e.bottomLeftBorderBox,e.bottomLeftPaddingBox);case 3:default:return HA(e.bottomLeftBorderBox,e.bottomLeftPaddingBox,e.topLeftBorderBox,e.topLeftPaddingBox)}},bB=function(e,A){switch(A){case 0:return HA(e.topLeftBorderBox,e.topLeftBorderDoubleOuterBox,e.topRightBorderBox,e.topRightBorderDoubleOuterBox);case 1:return HA(e.topRightBorderBox,e.topRightBorderDoubleOuterBox,e.bottomRightBorderBox,e.bottomRightBorderDoubleOuterBox);case 2:return HA(e.bottomRightBorderBox,e.bottomRightBorderDoubleOuterBox,e.bottomLeftBorderBox,e.bottomLeftBorderDoubleOuterBox);case 3:default:return HA(e.bottomLeftBorderBox,e.bottomLeftBorderDoubleOuterBox,e.topLeftBorderBox,e.topLeftBorderDoubleOuterBox)}},LB=function(e,A){switch(A){case 0:return HA(e.topLeftBorderDoubleInnerBox,e.topLeftPaddingBox,e.topRightBorderDoubleInnerBox,e.topRightPaddingBox);case 1:return HA(e.topRightBorderDoubleInnerBox,e.topRightPaddingBox,e.bottomRightBorderDoubleInnerBox,e.bottomRightPaddingBox);case 2:return HA(e.bottomRightBorderDoubleInnerBox,e.bottomRightPaddingBox,e.bottomLeftBorderDoubleInnerBox,e.bottomLeftPaddingBox);case 3:default:return HA(e.bottomLeftBorderDoubleInnerBox,e.bottomLeftPaddingBox,e.topLeftBorderDoubleInnerBox,e.topLeftPaddingBox)}},xB=function(e,A){switch(A){case 0:return Gt(e.topLeftBorderStroke,e.topRightBorderStroke);case 1:return Gt(e.topRightBorderStroke,e.bottomRightBorderStroke);case 2:return Gt(e.bottomRightBorderStroke,e.bottomLeftBorderStroke);case 3:default:return Gt(e.bottomLeftBorderStroke,e.topLeftBorderStroke)}},Gt=function(e,A){var r=[];return EA(e)?r.push(e.subdivide(.5,!1)):r.push(e),EA(A)?r.push(A.subdivide(.5,!0)):r.push(A),r},HA=function(e,A,r,t){var n=[];return EA(e)?n.push(e.subdivide(.5,!1)):n.push(e),EA(r)?n.push(r.subdivide(.5,!0)):n.push(r),EA(t)?n.push(t.subdivide(.5,!0).reverse()):n.push(t),EA(A)?n.push(A.subdivide(.5,!1).reverse()):n.push(A),n},Yo=function(e){var A=e.bounds,r=e.styles;return A.add(r.borderLeftWidth,r.borderTopWidth,-(r.borderRightWidth+r.borderLeftWidth),-(r.borderTopWidth+r.borderBottomWidth))},Rt=function(e){var A=e.styles,r=e.bounds,t=tA(A.paddingLeft,r.width),n=tA(A.paddingRight,r.width),o=tA(A.paddingTop,r.width),a=tA(A.paddingBottom,r.width);return r.add(t+A.borderLeftWidth,o+A.borderTopWidth,-(A.borderRightWidth+A.borderLeftWidth+t+n),-(A.borderTopWidth+A.borderBottomWidth+o+a))},MB=function(e,A){return e===0?A.bounds:e===2?Rt(A):Yo(A)},SB=function(e,A){return e===0?A.bounds:e===2?Rt(A):Yo(A)},Jr=function(e,A,r){var t=MB(Ue(e.styles.backgroundOrigin,A),e),n=SB(Ue(e.styles.backgroundClip,A),e),o=KB(Ue(e.styles.backgroundSize,A),r,t),a=o[0],c=o[1],s=Re(Ue(e.styles.backgroundPosition,A),t.width-a,t.height-c),u=DB(Ue(e.styles.backgroundRepeat,A),s,o,t,n),f=Math.round(t.left+s[0]),w=Math.round(t.top+s[1]);return[u,f,w,a,c]},pe=function(e){return j(e)&&e.value===Ve.AUTO},kt=function(e){return typeof e=="number"},KB=function(e,A,r){var t=A[0],n=A[1],o=A[2],a=e[0],c=e[1];if(!a)return[0,0];if(iA(a)&&c&&iA(c))return[tA(a,r.width),tA(c,r.height)];var s=kt(o);if(j(a)&&(a.value===Ve.CONTAIN||a.value===Ve.COVER)){if(kt(o)){var u=r.width/r.height;return u<o!=(a.value===Ve.COVER)?[r.width,r.width/o]:[r.height*o,r.height]}return[r.width,r.height]}var f=kt(t),w=kt(n),C=f||w;if(pe(a)&&(!c||pe(c))){if(f&&w)return[t,n];if(!s&&!C)return[r.width,r.height];if(C&&s){var b=f?t:n*o,U=w?n:t/o;return[b,U]}var E=f?t:r.width,N=w?n:r.height;return[E,N]}if(s){var S=0,x=0;return iA(a)?S=tA(a,r.width):iA(c)&&(x=tA(c,r.height)),pe(a)?S=x*o:(!c||pe(c))&&(x=S/o),[S,x]}var Z=null,G=null;if(iA(a)?Z=tA(a,r.width):c&&iA(c)&&(G=tA(c,r.height)),Z!==null&&(!c||pe(c))&&(G=f&&w?Z/t*n:r.height),G!==null&&pe(a)&&(Z=f&&w?G/n*t:r.width),Z!==null&&G!==null)return[Z,G];throw new Error("Unable to calculate background-size for element")},Ue=function(e,A){var r=e[A];return typeof r>"u"?e[0]:r},DB=function(e,A,r,t,n){var o=A[0],a=A[1],c=r[0],s=r[1];switch(e){case 2:return[new H(Math.round(t.left),Math.round(t.top+a)),new H(Math.round(t.left+t.width),Math.round(t.top+a)),new H(Math.round(t.left+t.width),Math.round(s+t.top+a)),new H(Math.round(t.left),Math.round(s+t.top+a))];case 3:return[new H(Math.round(t.left+o),Math.round(t.top)),new H(Math.round(t.left+o+c),Math.round(t.top)),new H(Math.round(t.left+o+c),Math.round(t.height+t.top)),new H(Math.round(t.left+o),Math.round(t.height+t.top))];case 1:return[new H(Math.round(t.left+o),Math.round(t.top+a)),new H(Math.round(t.left+o+c),Math.round(t.top+a)),new H(Math.round(t.left+o+c),Math.round(t.top+a+s)),new H(Math.round(t.left+o),Math.round(t.top+a+s))];default:return[new H(Math.round(n.left),Math.round(n.top)),new H(Math.round(n.left+n.width),Math.round(n.top)),new H(Math.round(n.left+n.width),Math.round(n.height+n.top)),new H(Math.round(n.left),Math.round(n.height+n.top))]}},TB="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",$o="Hidden Text",OB=function(){function e(A){this._data={},this._document=A}return e.prototype.parseMetrics=function(A,r){var t=this._document.createElement("div"),n=this._document.createElement("img"),o=this._document.createElement("span"),a=this._document.body;t.style.visibility="hidden",t.style.fontFamily=A,t.style.fontSize=r,t.style.margin="0",t.style.padding="0",t.style.whiteSpace="nowrap",a.appendChild(t),n.src=TB,n.width=1,n.height=1,n.style.margin="0",n.style.padding="0",n.style.verticalAlign="baseline",o.style.fontFamily=A,o.style.fontSize=r,o.style.margin="0",o.style.padding="0",o.appendChild(this._document.createTextNode($o)),t.appendChild(o),t.appendChild(n);var c=n.offsetTop-o.offsetTop+2;t.removeChild(o),t.appendChild(this._document.createTextNode($o)),t.style.lineHeight="normal",n.style.verticalAlign="super";var s=n.offsetTop-t.offsetTop+2;return a.removeChild(t),{baseline:c,middle:s}},e.prototype.getMetrics=function(A,r){var t=A+" "+r;return typeof this._data[t]>"u"&&(this._data[t]=this.parseMetrics(A,r)),this._data[t]},e}(),Zo=function(){function e(A,r){this.context=A,this.options=r}return e}(),_B=1e4,PB=function(e){R(A,e);function A(r,t){var n=e.call(this,r,t)||this;return n._activeEffects=[],n.canvas=t.canvas?t.canvas:document.createElement("canvas"),n.ctx=n.canvas.getContext("2d"),t.canvas||(n.canvas.width=Math.floor(t.width*t.scale),n.canvas.height=Math.floor(t.height*t.scale),n.canvas.style.width=t.width+"px",n.canvas.style.height=t.height+"px"),n.fontMetrics=new OB(document),n.ctx.scale(n.options.scale,n.options.scale),n.ctx.translate(-t.x,-t.y),n.ctx.textBaseline="bottom",n._activeEffects=[],n.context.logger.debug("Canvas renderer initialized ("+t.width+"x"+t.height+") with scale "+t.scale),n}return A.prototype.applyEffects=function(r){for(var t=this;this._activeEffects.length;)this.popEffect();r.forEach(function(n){return t.applyEffect(n)})},A.prototype.applyEffect=function(r){this.ctx.save(),HB(r)&&(this.ctx.globalAlpha=r.opacity),EB(r)&&(this.ctx.translate(r.offsetX,r.offsetY),this.ctx.transform(r.matrix[0],r.matrix[1],r.matrix[2],r.matrix[3],r.matrix[4],r.matrix[5]),this.ctx.translate(-r.offsetX,-r.offsetY)),Ro(r)&&(this.path(r.path),this.ctx.clip()),this._activeEffects.push(r)},A.prototype.popEffect=function(){this._activeEffects.pop(),this.ctx.restore()},A.prototype.renderStack=function(r){return l(this,void 0,void 0,function(){var t;return B(this,function(n){switch(n.label){case 0:return t=r.element.container.styles,t.isVisible()?[4,this.renderStackContent(r)]:[3,2];case 1:n.sent(),n.label=2;case 2:return[2]}})})},A.prototype.renderNode=function(r){return l(this,void 0,void 0,function(){return B(this,function(t){switch(t.label){case 0:if(sA(r.container.flags,16))debugger;return r.container.styles.isVisible()?[4,this.renderNodeBackgroundAndBorders(r)]:[3,3];case 1:return t.sent(),[4,this.renderNodeContent(r)];case 2:t.sent(),t.label=3;case 3:return[2]}})})},A.prototype.renderTextWithLetterSpacing=function(r,t,n){var o=this;if(t===0)this.ctx.fillText(r.text,r.bounds.left,r.bounds.top+n);else{var a=Sr(r.text);a.reduce(function(c,s){return o.ctx.fillText(s,c,r.bounds.top+n),c+o.ctx.measureText(s).width},r.bounds.left)}},A.prototype.createFontStyle=function(r){var t=r.fontVariant.filter(function(a){return a==="normal"||a==="small-caps"}).join(""),n=VB(r.fontFamily).join(", "),o=Ge(r.fontSize)?""+r.fontSize.number+r.fontSize.unit:r.fontSize.number+"px";return[[r.fontStyle,t,r.fontWeight,o,n].join(" "),n,o]},A.prototype.renderTextNode=function(r,t){return l(this,void 0,void 0,function(){var n,o,a,c,s,u,f,w,C=this;return B(this,function(b){return n=this.createFontStyle(t),o=n[0],a=n[1],c=n[2],this.ctx.font=o,this.ctx.direction=t.direction===1?"rtl":"ltr",this.ctx.textAlign="left",this.ctx.textBaseline="alphabetic",s=this.fontMetrics.getMetrics(a,c),u=s.baseline,f=s.middle,w=t.paintOrder,r.textBounds.forEach(function(U){w.forEach(function(E){switch(E){case 0:C.ctx.fillStyle=cA(t.color),C.renderTextWithLetterSpacing(U,t.letterSpacing,u);var N=t.textShadow;N.length&&U.text.trim().length&&(N.slice(0).reverse().forEach(function(S){C.ctx.shadowColor=cA(S.color),C.ctx.shadowOffsetX=S.offsetX.number*C.options.scale,C.ctx.shadowOffsetY=S.offsetY.number*C.options.scale,C.ctx.shadowBlur=S.blur.number,C.renderTextWithLetterSpacing(U,t.letterSpacing,u)}),C.ctx.shadowColor="",C.ctx.shadowOffsetX=0,C.ctx.shadowOffsetY=0,C.ctx.shadowBlur=0),t.textDecorationLine.length&&(C.ctx.fillStyle=cA(t.textDecorationColor||t.color),t.textDecorationLine.forEach(function(S){switch(S){case 1:C.ctx.fillRect(U.bounds.left,Math.round(U.bounds.top+u),U.bounds.width,1);break;case 2:C.ctx.fillRect(U.bounds.left,Math.round(U.bounds.top),U.bounds.width,1);break;case 3:C.ctx.fillRect(U.bounds.left,Math.ceil(U.bounds.top+f),U.bounds.width,1);break}}));break;case 1:t.webkitTextStrokeWidth&&U.text.trim().length&&(C.ctx.strokeStyle=cA(t.webkitTextStrokeColor),C.ctx.lineWidth=t.webkitTextStrokeWidth,C.ctx.lineJoin=window.chrome?"miter":"round",C.ctx.strokeText(U.text,U.bounds.left,U.bounds.top+u)),C.ctx.strokeStyle="",C.ctx.lineWidth=0,C.ctx.lineJoin="miter";break}})}),[2]})})},A.prototype.renderReplacedElement=function(r,t,n){if(n&&r.intrinsicWidth>0&&r.intrinsicHeight>0){var o=Rt(r),a=Pt(t);this.path(a),this.ctx.save(),this.ctx.clip(),this.ctx.drawImage(n,0,0,r.intrinsicWidth,r.intrinsicHeight,o.left,o.top,o.width,o.height),this.ctx.restore()}},A.prototype.renderNodeContent=function(r){return l(this,void 0,void 0,function(){var t,n,o,a,c,s,x,x,u,f,w,C,G,b,U,O,E,N,S,x,Z,G,O;return B(this,function(y){switch(y.label){case 0:this.applyEffects(r.getEffects(4)),t=r.container,n=r.curves,o=t.styles,a=0,c=t.textNodes,y.label=1;case 1:return a<c.length?(s=c[a],[4,this.renderTextNode(s,o)]):[3,4];case 2:y.sent(),y.label=3;case 3:return a++,[3,1];case 4:if(!(t instanceof uo))return[3,8];y.label=5;case 5:return y.trys.push([5,7,,8]),[4,this.context.cache.match(t.src)];case 6:return x=y.sent(),this.renderReplacedElement(t,n,x),[3,8];case 7:return y.sent(),this.context.logger.error("Error loading image "+t.src),[3,8];case 8:if(t instanceof fo&&this.renderReplacedElement(t,n,t.canvas),!(t instanceof wo))return[3,12];y.label=9;case 9:return y.trys.push([9,11,,12]),[4,this.context.cache.match(t.svg)];case 10:return x=y.sent(),this.renderReplacedElement(t,n,x),[3,12];case 11:return y.sent(),this.context.logger.error("Error loading svg "+t.svg.substring(0,255)),[3,12];case 12:return t instanceof Uo&&t.tree?(u=new A(this.context,{scale:this.options.scale,backgroundColor:t.backgroundColor,x:0,y:0,width:t.width,height:t.height}),[4,u.render(t.tree)]):[3,14];case 13:f=y.sent(),t.width&&t.height&&this.ctx.drawImage(f,0,0,t.width,t.height,t.bounds.left,t.bounds.top,t.bounds.width,t.bounds.height),y.label=14;case 14:if(t instanceof Dr&&(w=Math.min(t.bounds.width,t.bounds.height),t.type===Lt?t.checked&&(this.ctx.save(),this.path([new H(t.bounds.left+w*.39363,t.bounds.top+w*.79),new H(t.bounds.left+w*.16,t.bounds.top+w*.5549),new H(t.bounds.left+w*.27347,t.bounds.top+w*.44071),new H(t.bounds.left+w*.39694,t.bounds.top+w*.5649),new H(t.bounds.left+w*.72983,t.bounds.top+w*.23),new H(t.bounds.left+w*.84,t.bounds.top+w*.34085),new H(t.bounds.left+w*.39363,t.bounds.top+w*.79)]),this.ctx.fillStyle=cA(Co),this.ctx.fill(),this.ctx.restore()):t.type===xt&&t.checked&&(this.ctx.save(),this.ctx.beginPath(),this.ctx.arc(t.bounds.left+w/2,t.bounds.top+w/2,w/4,0,Math.PI*2,!0),this.ctx.fillStyle=cA(Co),this.ctx.fill(),this.ctx.restore())),NB(t)&&t.value.length){switch(C=this.createFontStyle(o),G=C[0],b=C[1],U=this.fontMetrics.getMetrics(G,b).baseline,this.ctx.font=G,this.ctx.fillStyle=cA(o.color),this.ctx.textBaseline="alphabetic",this.ctx.textAlign=RB(t.styles.textAlign),O=Rt(t),E=0,t.styles.textAlign){case 1:E+=O.width/2;break;case 2:E+=O.width;break}N=O.add(E,0,0,-O.height/2+1),this.ctx.save(),this.path([new H(O.left,O.top),new H(O.left+O.width,O.top),new H(O.left+O.width,O.top+O.height),new H(O.left,O.top+O.height)]),this.ctx.clip(),this.renderTextWithLetterSpacing(new Je(t.value,N),o.letterSpacing,U),this.ctx.restore(),this.ctx.textBaseline="alphabetic",this.ctx.textAlign="left"}if(!sA(t.styles.display,2048))return[3,20];if(t.styles.listStyleImage===null)return[3,19];if(S=t.styles.listStyleImage,S.type!==0)return[3,18];x=void 0,Z=S.url,y.label=15;case 15:return y.trys.push([15,17,,18]),[4,this.context.cache.match(Z)];case 16:return x=y.sent(),this.ctx.drawImage(x,t.bounds.left-(x.width+10),t.bounds.top),[3,18];case 17:return y.sent(),this.context.logger.error("Error loading list-style-image "+Z),[3,18];case 18:return[3,20];case 19:r.listValue&&t.styles.listStyleType!==-1&&(G=this.createFontStyle(o)[0],this.ctx.font=G,this.ctx.fillStyle=cA(o.color),this.ctx.textBaseline="middle",this.ctx.textAlign="right",O=new Q(t.bounds.left,t.bounds.top+tA(t.styles.paddingTop,t.bounds.width),t.bounds.width,Yn(o.lineHeight,o.fontSize.number)/2+1),this.renderTextWithLetterSpacing(new Je(r.listValue,O),o.letterSpacing,Yn(o.lineHeight,o.fontSize.number)/2+2),this.ctx.textBaseline="bottom",this.ctx.textAlign="left"),y.label=20;case 20:return[2]}})})},A.prototype.renderStackContent=function(r){return l(this,void 0,void 0,function(){var t,n,S,o,a,S,c,s,S,u,f,S,w,C,S,b,U,S,E,N,S;return B(this,function(x){switch(x.label){case 0:if(sA(r.element.container.flags,16))debugger;return[4,this.renderNodeBackgroundAndBorders(r.element)];case 1:x.sent(),t=0,n=r.negativeZIndex,x.label=2;case 2:return t<n.length?(S=n[t],[4,this.renderStack(S)]):[3,5];case 3:x.sent(),x.label=4;case 4:return t++,[3,2];case 5:return[4,this.renderNodeContent(r.element)];case 6:x.sent(),o=0,a=r.nonInlineLevel,x.label=7;case 7:return o<a.length?(S=a[o],[4,this.renderNode(S)]):[3,10];case 8:x.sent(),x.label=9;case 9:return o++,[3,7];case 10:c=0,s=r.nonPositionedFloats,x.label=11;case 11:return c<s.length?(S=s[c],[4,this.renderStack(S)]):[3,14];case 12:x.sent(),x.label=13;case 13:return c++,[3,11];case 14:u=0,f=r.nonPositionedInlineLevel,x.label=15;case 15:return u<f.length?(S=f[u],[4,this.renderStack(S)]):[3,18];case 16:x.sent(),x.label=17;case 17:return u++,[3,15];case 18:w=0,C=r.inlineLevel,x.label=19;case 19:return w<C.length?(S=C[w],[4,this.renderNode(S)]):[3,22];case 20:x.sent(),x.label=21;case 21:return w++,[3,19];case 22:b=0,U=r.zeroOrAutoZIndexOrTransformedOrOpacity,x.label=23;case 23:return b<U.length?(S=U[b],[4,this.renderStack(S)]):[3,26];case 24:x.sent(),x.label=25;case 25:return b++,[3,23];case 26:E=0,N=r.positiveZIndex,x.label=27;case 27:return E<N.length?(S=N[E],[4,this.renderStack(S)]):[3,30];case 28:x.sent(),x.label=29;case 29:return E++,[3,27];case 30:return[2]}})})},A.prototype.mask=function(r){this.ctx.beginPath(),this.ctx.moveTo(0,0),this.ctx.lineTo(this.canvas.width,0),this.ctx.lineTo(this.canvas.width,this.canvas.height),this.ctx.lineTo(0,this.canvas.height),this.ctx.lineTo(0,0),this.formatPath(r.slice(0).reverse()),this.ctx.closePath()},A.prototype.path=function(r){this.ctx.beginPath(),this.formatPath(r),this.ctx.closePath()},A.prototype.formatPath=function(r){var t=this;r.forEach(function(n,o){var a=EA(n)?n.start:n;o===0?t.ctx.moveTo(a.x,a.y):t.ctx.lineTo(a.x,a.y),EA(n)&&t.ctx.bezierCurveTo(n.startControl.x,n.startControl.y,n.endControl.x,n.endControl.y,n.end.x,n.end.y)})},A.prototype.renderRepeat=function(r,t,n,o){this.path(r),this.ctx.fillStyle=t,this.ctx.translate(n,o),this.ctx.fill(),this.ctx.translate(-n,-o)},A.prototype.resizeImage=function(r,t,n){var o;if(r.width===t&&r.height===n)return r;var a=(o=this.canvas.ownerDocument)!==null&&o!==void 0?o:document,c=a.createElement("canvas");c.width=Math.max(1,t),c.height=Math.max(1,n);var s=c.getContext("2d");return s.drawImage(r,0,0,r.width,r.height,0,0,t,n),c},A.prototype.renderBackgroundImage=function(r){return l(this,void 0,void 0,function(){var t,n,o,a,c,s;return B(this,function(u){switch(u.label){case 0:t=r.styles.backgroundImage.length-1,n=function(f){var w,C,b,aA,CA,QA,AA,gA,$,U,aA,CA,QA,AA,gA,E,N,S,x,Z,G,O,y,Y,$,_,aA,hA,FA,AA,gA,qA,CA,QA,ae,DA,jA,se,ce,PA,le,NA;return B(this,function(Fe){switch(Fe.label){case 0:if(f.type!==0)return[3,5];w=void 0,C=f.url,Fe.label=1;case 1:return Fe.trys.push([1,3,,4]),[4,o.context.cache.match(C)];case 2:return w=Fe.sent(),[3,4];case 3:return Fe.sent(),o.context.logger.error("Error loading background-image "+C),[3,4];case 4:return w&&(b=Jr(r,t,[w.width,w.height,w.width/w.height]),aA=b[0],CA=b[1],QA=b[2],AA=b[3],gA=b[4],$=o.ctx.createPattern(o.resizeImage(w,AA,gA),"repeat"),o.renderRepeat(aA,$,CA,QA)),[3,6];case 5:Es(f)?(U=Jr(r,t,[null,null,null]),aA=U[0],CA=U[1],QA=U[2],AA=U[3],gA=U[4],E=ps(f.angle,AA,gA),N=E[0],S=E[1],x=E[2],Z=E[3],G=E[4],O=document.createElement("canvas"),O.width=AA,O.height=gA,y=O.getContext("2d"),Y=y.createLinearGradient(S,Z,x,G),On(f.stops,N).forEach(function($e){return Y.addColorStop($e.stop,cA($e.color))}),y.fillStyle=Y,y.fillRect(0,0,AA,gA),AA>0&&gA>0&&($=o.ctx.createPattern(O,"repeat"),o.renderRepeat(aA,$,CA,QA))):Hs(f)&&(_=Jr(r,t,[null,null,null]),aA=_[0],hA=_[1],FA=_[2],AA=_[3],gA=_[4],qA=f.position.length===0?[hr]:f.position,CA=tA(qA[0],AA),QA=tA(qA[qA.length-1],gA),ae=Us(f,CA,QA,AA,gA),DA=ae[0],jA=ae[1],DA>0&&jA>0&&(se=o.ctx.createRadialGradient(hA+CA,FA+QA,0,hA+CA,FA+QA,DA),On(f.stops,DA*2).forEach(function($e){return se.addColorStop($e.stop,cA($e.color))}),o.path(aA),o.ctx.fillStyle=se,DA!==jA?(ce=r.bounds.left+.5*r.bounds.width,PA=r.bounds.top+.5*r.bounds.height,le=jA/DA,NA=1/le,o.ctx.save(),o.ctx.translate(ce,PA),o.ctx.transform(1,0,0,le,0,0),o.ctx.translate(-ce,-PA),o.ctx.fillRect(hA,NA*(FA-PA)+PA,AA,gA*NA),o.ctx.restore()):o.ctx.fill())),Fe.label=6;case 6:return t--,[2]}})},o=this,a=0,c=r.styles.backgroundImage.slice(0).reverse(),u.label=1;case 1:return a<c.length?(s=c[a],[5,n(s)]):[3,4];case 2:u.sent(),u.label=3;case 3:return a++,[3,1];case 4:return[2]}})})},A.prototype.renderSolidBorder=function(r,t,n){return l(this,void 0,void 0,function(){return B(this,function(o){return this.path(Wo(n,t)),this.ctx.fillStyle=cA(r),this.ctx.fill(),[2]})})},A.prototype.renderDoubleBorder=function(r,t,n,o){return l(this,void 0,void 0,function(){var a,c;return B(this,function(s){switch(s.label){case 0:return t<3?[4,this.renderSolidBorder(r,n,o)]:[3,2];case 1:return s.sent(),[2];case 2:return a=bB(o,n),this.path(a),this.ctx.fillStyle=cA(r),this.ctx.fill(),c=LB(o,n),this.path(c),this.ctx.fill(),[2]}})})},A.prototype.renderNodeBackgroundAndBorders=function(r){return l(this,void 0,void 0,function(){var t,n,o,a,c,s,u,f,w=this;return B(this,function(C){switch(C.label){case 0:return this.applyEffects(r.getEffects(2)),t=r.container.styles,n=!YA(t.backgroundColor)||t.backgroundImage.length,o=[{style:t.borderTopStyle,color:t.borderTopColor,width:t.borderTopWidth},{style:t.borderRightStyle,color:t.borderRightColor,width:t.borderRightWidth},{style:t.borderBottomStyle,color:t.borderBottomColor,width:t.borderBottomWidth},{style:t.borderLeftStyle,color:t.borderLeftColor,width:t.borderLeftWidth}],a=GB(Ue(t.backgroundClip,0),r.curves),n||t.boxShadow.length?(this.ctx.save(),this.path(a),this.ctx.clip(),YA(t.backgroundColor)||(this.ctx.fillStyle=cA(t.backgroundColor),this.ctx.fill()),[4,this.renderBackgroundImage(r.container)]):[3,2];case 1:C.sent(),this.ctx.restore(),t.boxShadow.slice(0).reverse().forEach(function(b){w.ctx.save();var U=_t(r.curves),E=b.inset?0:_B,N=IB(U,-E+(b.inset?1:-1)*b.spread.number,(b.inset?1:-1)*b.spread.number,b.spread.number*(b.inset?-2:2),b.spread.number*(b.inset?-2:2));b.inset?(w.path(U),w.ctx.clip(),w.mask(N)):(w.mask(U),w.ctx.clip(),w.path(N)),w.ctx.shadowOffsetX=b.offsetX.number+E,w.ctx.shadowOffsetY=b.offsetY.number,w.ctx.shadowColor=cA(b.color),w.ctx.shadowBlur=b.blur.number,w.ctx.fillStyle=b.inset?cA(b.color):"rgba(0,0,0,1)",w.ctx.fill(),w.ctx.restore()}),C.label=2;case 2:c=0,s=0,u=o,C.label=3;case 3:return s<u.length?(f=u[s],f.style!==0&&!YA(f.color)&&f.width>0?f.style!==2?[3,5]:[4,this.renderDashedDottedBorder(f.color,f.width,c,r.curves,2)]:[3,11]):[3,13];case 4:return C.sent(),[3,11];case 5:return f.style!==3?[3,7]:[4,this.renderDashedDottedBorder(f.color,f.width,c,r.curves,3)];case 6:return C.sent(),[3,11];case 7:return f.style!==4?[3,9]:[4,this.renderDoubleBorder(f.color,f.width,c,r.curves)];case 8:return C.sent(),[3,11];case 9:return[4,this.renderSolidBorder(f.color,c,r.curves)];case 10:C.sent(),C.label=11;case 11:c++,C.label=12;case 12:return s++,[3,3];case 13:return[2]}})})},A.prototype.renderDashedDottedBorder=function(r,t,n,o,a){return l(this,void 0,void 0,function(){var c,s,u,f,w,C,b,U,E,N,S,x,Z,G,O,y,O,y;return B(this,function(Y){return this.ctx.save(),c=xB(o,n),s=Wo(o,n),a===2&&(this.path(s),this.ctx.clip()),EA(s[0])?(u=s[0].start.x,f=s[0].start.y):(u=s[0].x,f=s[0].y),EA(s[1])?(w=s[1].end.x,C=s[1].end.y):(w=s[1].x,C=s[1].y),n===0||n===2?b=Math.abs(u-w):b=Math.abs(f-C),this.ctx.beginPath(),a===3?this.formatPath(c):this.formatPath(s.slice(0,2)),U=t<3?t*3:t*2,E=t<3?t*2:t,a===3&&(U=t,E=t),N=!0,b<=U*2?N=!1:b<=U*2+E?(S=b/(2*U+E),U*=S,E*=S):(x=Math.floor((b+E)/(U+E)),Z=(b-x*U)/(x-1),G=(b-(x+1)*U)/x,E=G<=0||Math.abs(E-Z)<Math.abs(E-G)?Z:G),N&&(a===3?this.ctx.setLineDash([0,U+E]):this.ctx.setLineDash([U,E])),a===3?(this.ctx.lineCap="round",this.ctx.lineWidth=t):this.ctx.lineWidth=t*2+1.1,this.ctx.strokeStyle=cA(r),this.ctx.stroke(),this.ctx.setLineDash([]),a===2&&(EA(s[0])&&(O=s[3],y=s[0],this.ctx.beginPath(),this.formatPath([new H(O.end.x,O.end.y),new H(y.start.x,y.start.y)]),this.ctx.stroke()),EA(s[1])&&(O=s[1],y=s[2],this.ctx.beginPath(),this.formatPath([new H(O.end.x,O.end.y),new H(y.start.x,y.start.y)]),this.ctx.stroke())),this.ctx.restore(),[2]})})},A.prototype.render=function(r){return l(this,void 0,void 0,function(){var t;return B(this,function(n){switch(n.label){case 0:return this.options.backgroundColor&&(this.ctx.fillStyle=cA(this.options.backgroundColor),this.ctx.fillRect(this.options.x,this.options.y,this.options.width,this.options.height)),t=yB(r),[4,this.renderStack(t)];case 1:return n.sent(),this.applyEffects([]),[2,this.canvas]}})})},A}(Zo),NB=function(e){return e instanceof po||e instanceof Qo?!0:e instanceof Dr&&e.type!==xt&&e.type!==Lt},GB=function(e,A){switch(e){case 0:return _t(A);case 2:return FB(A);case 1:default:return Pt(A)}},RB=function(e){switch(e){case 1:return"center";case 2:return"right";case 0:default:return"left"}},kB=["-apple-system","system-ui"],VB=function(e){return/iPhone OS 15_(0|1)/.test(window.navigator.userAgent)?e.filter(function(A){return kB.indexOf(A)===-1}):e},XB=function(e){R(A,e);function A(r,t){var n=e.call(this,r,t)||this;return n.canvas=t.canvas?t.canvas:document.createElement("canvas"),n.ctx=n.canvas.getContext("2d"),n.options=t,n.canvas.width=Math.floor(t.width*t.scale),n.canvas.height=Math.floor(t.height*t.scale),n.canvas.style.width=t.width+"px",n.canvas.style.height=t.height+"px",n.ctx.scale(n.options.scale,n.options.scale),n.ctx.translate(-t.x,-t.y),n.context.logger.debug("EXPERIMENTAL ForeignObject renderer initialized ("+t.width+"x"+t.height+" at "+t.x+","+t.y+") with scale "+t.scale),n}return A.prototype.render=function(r){return l(this,void 0,void 0,function(){var t,n;return B(this,function(o){switch(o.label){case 0:return t=Mr(this.options.width*this.options.scale,this.options.height*this.options.scale,this.options.scale,this.options.scale,r),[4,JB(t)];case 1:return n=o.sent(),this.options.backgroundColor&&(this.ctx.fillStyle=cA(this.options.backgroundColor),this.ctx.fillRect(0,0,this.options.width*this.options.scale,this.options.height*this.options.scale)),this.ctx.drawImage(n,-this.options.x*this.options.scale,-this.options.y*this.options.scale),[2,this.canvas]}})})},A}(Zo),JB=function(e){return new Promise(function(A,r){var t=new Image;t.onload=function(){A(t)},t.onerror=r,t.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent(new XMLSerializer().serializeToString(e))})},WB=function(){function e(A){var r=A.id,t=A.enabled;this.id=r,this.enabled=t,this.start=Date.now()}return e.prototype.debug=function(){for(var A=[],r=0;r<arguments.length;r++)A[r]=arguments[r];this.enabled&&(typeof window<"u"&&window.console&&typeof console.debug=="function"?console.debug.apply(console,g([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},e.prototype.getTime=function(){return Date.now()-this.start},e.prototype.info=function(){for(var A=[],r=0;r<arguments.length;r++)A[r]=arguments[r];this.enabled&&typeof window<"u"&&window.console&&typeof console.info=="function"&&console.info.apply(console,g([this.id,this.getTime()+"ms"],A))},e.prototype.warn=function(){for(var A=[],r=0;r<arguments.length;r++)A[r]=arguments[r];this.enabled&&(typeof window<"u"&&window.console&&typeof console.warn=="function"?console.warn.apply(console,g([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},e.prototype.error=function(){for(var A=[],r=0;r<arguments.length;r++)A[r]=arguments[r];this.enabled&&(typeof window<"u"&&window.console&&typeof console.error=="function"?console.error.apply(console,g([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},e.instances={},e}(),YB=function(){function e(A,r){var t;this.windowBounds=r,this.instanceName="#"+e.instanceCount++,this.logger=new WB({id:this.instanceName,enabled:A.logging}),this.cache=(t=A.cache)!==null&&t!==void 0?t:new fB(this,A)}return e.instanceCount=1,e}(),$B=function(e,A){return A===void 0&&(A={}),ZB(e,A)};typeof window<"u"&&Go.setContext(window);var ZB=function(e,A){return l(void 0,void 0,void 0,function(){var r,t,n,o,a,c,s,u,f,w,C,b,U,E,N,S,x,Z,G,O,Y,y,Y,$,_,aA,hA,FA,AA,gA,qA,CA,QA,ae,DA,jA,se,ce,PA,le;return B(this,function(NA){switch(NA.label){case 0:if(!e||typeof e!="object")return[2,Promise.reject("Invalid element provided as first argument")];if(r=e.ownerDocument,!r)throw new Error("Element is not attached to a Document");if(t=r.defaultView,!t)throw new Error("Document is not attached to a Window");return n={allowTaint:($=A.allowTaint)!==null&&$!==void 0?$:!1,imageTimeout:(_=A.imageTimeout)!==null&&_!==void 0?_:15e3,proxy:A.proxy,useCORS:(aA=A.useCORS)!==null&&aA!==void 0?aA:!1},o=i({logging:(hA=A.logging)!==null&&hA!==void 0?hA:!0,cache:A.cache},n),a={windowWidth:(FA=A.windowWidth)!==null&&FA!==void 0?FA:t.innerWidth,windowHeight:(AA=A.windowHeight)!==null&&AA!==void 0?AA:t.innerHeight,scrollX:(gA=A.scrollX)!==null&&gA!==void 0?gA:t.pageXOffset,scrollY:(qA=A.scrollY)!==null&&qA!==void 0?qA:t.pageYOffset},c=new Q(a.scrollX,a.scrollY,a.windowWidth,a.windowHeight),s=new YB(o,c),u=(CA=A.foreignObjectRendering)!==null&&CA!==void 0?CA:!1,f={allowTaint:(QA=A.allowTaint)!==null&&QA!==void 0?QA:!1,onclone:A.onclone,ignoreElements:A.ignoreElements,inlineImages:u,copyStyles:u},s.logger.debug("Starting document clone with size "+c.width+"x"+c.height+" scrolled to "+-c.left+","+-c.top),w=new Po(s,e,f),C=w.clonedReferenceElement,C?[4,w.toIFrame(r,c)]:[2,Promise.reject("Unable to find element in cloned iframe")];case 1:return b=NA.sent(),U=Or(C)||zl(C)?F(C.ownerDocument):h(s,C),E=U.width,N=U.height,S=U.left,x=U.top,Z=zB(s,C,A.backgroundColor),G={canvas:A.canvas,backgroundColor:Z,scale:(DA=(ae=A.scale)!==null&&ae!==void 0?ae:t.devicePixelRatio)!==null&&DA!==void 0?DA:1,x:((jA=A.x)!==null&&jA!==void 0?jA:0)+S,y:((se=A.y)!==null&&se!==void 0?se:0)+x,width:(ce=A.width)!==null&&ce!==void 0?ce:Math.ceil(E),height:(PA=A.height)!==null&&PA!==void 0?PA:Math.ceil(N)},u?(s.logger.debug("Document cloned, using foreign object rendering"),Y=new XB(s,G),[4,Y.render(C)]):[3,3];case 2:return O=NA.sent(),[3,5];case 3:return s.logger.debug("Document cloned, element located at "+S+","+x+" with size "+E+"x"+N+" using computed rendering"),s.logger.debug("Starting DOM parsing"),y=mo(s,C),Z===y.styles.backgroundColor&&(y.styles.backgroundColor=OA.TRANSPARENT),s.logger.debug("Starting renderer for element at "+G.x+","+G.y+" with size "+G.width+"x"+G.height),Y=new PB(s,G),[4,Y.render(y)];case 4:O=NA.sent(),NA.label=5;case 5:return(!((le=A.removeContainer)!==null&&le!==void 0)||le)&&(Po.destroy(b)||s.logger.error("Cannot detach cloned iframe as it is not in the DOM anymore")),s.logger.debug("Finished rendering"),[2,O]}})})},zB=function(e,A,r){var t=A.ownerDocument,n=t.documentElement?ke(e,getComputedStyle(t.documentElement).backgroundColor):OA.TRANSPARENT,o=t.body?ke(e,getComputedStyle(t.body).backgroundColor):OA.TRANSPARENT,a=typeof r=="string"?ke(e,r):r===null?OA.TRANSPARENT:4294967295;return A===t.documentElement?YA(n)?YA(o)?a:o:n:a};return $B})});var I=zo(jB());function Ag(m,R){m&1&&(v(0,"div",8),P(1,"ion-spinner",9),v(2,"p"),V(3,"Calculating travel times..."),M()())}function eg(m,R){if(m&1&&P(0,"img",29),m&2){let i=eA(2);qe("alt",i.center.name)}}function tg(m,R){if(m&1&&P(0,"img",30),m&2){let i=eA(2);qe("alt",i.center.name)}}function rg(m,R){if(m&1&&P(0,"img",31),m&2){let i=eA(2);qe("alt",i.center.name)}}function ng(m,R){if(m&1&&P(0,"img",32),m&2){let i=eA(2);qe("alt",i.center.name)}}function og(m,R){if(m&1&&(v(0,"ion-chip",18),P(1,"ion-icon",33),v(2,"ion-label"),V(3),M()()),m&2){let i=eA(2);k("color",i.getStatusColor(i.center.status)),K(3),IA(i.center.status)}}function ig(m,R){if(m&1&&(v(0,"div",21)(1,"ion-item",22),P(2,"ion-icon",34),v(3,"ion-label")(4,"h2"),V(5,"Capacity"),M(),v(6,"p"),V(7),M()()()()),m&2){let i=eA(2);K(7),yA("",i.center.capacity," people")}}function ag(m,R){if(m&1){let i=Ae();v(0,"ion-card",35),nA("click",function(){let B=GA(i).$implicit,g=eA(2);return RA(g.selectTravelMode(B.mode))}),v(1,"ion-card-header"),P(2,"ion-icon",36),v(3,"ion-card-title"),V(4),M()(),v(5,"ion-card-content")(6,"div",37)(7,"div",38),P(8,"ion-icon",39),v(9,"span"),V(10),M()(),v(11,"div",40),P(12,"ion-icon",41),v(13,"span"),V(14),M()()(),v(15,"ion-button",42),nA("click",function(){let B=GA(i).$implicit,g=eA(2);return RA(g.selectTravelMode(B.mode))}),V(16," Select "),P(17,"ion-icon",43),M()()()}if(m&2){let i=R.$implicit,l=eA(2);K(2),k("name",i.icon)("color",i.color),K(2),yA(" ",i.mode==="foot-walking"?"Walking":i.mode==="cycling-regular"?"Cycling":"Driving"," "),K(6),IA(l.formatTime(i.time)),K(4),IA(l.formatDistance(i.distance)),K(),k("color",i.color)}}function sg(m,R){if(m&1&&(v(0,"div",10)(1,"div",11),wA(2,eg,1,1,"img",12)(3,tg,1,1,"img",13)(4,rg,1,1,"img",14)(5,ng,1,1,"img",15),M(),v(6,"h1",16),V(7),M(),v(8,"div",17)(9,"ion-chip",18),P(10,"ion-icon",19),v(11,"ion-label"),V(12),M()(),wA(13,og,4,2,"ion-chip",20),M(),v(14,"div",21)(15,"ion-item",22),P(16,"ion-icon",23),v(17,"ion-label")(18,"h2"),V(19,"Address"),M(),v(20,"p"),V(21),M()()()(),v(22,"div",21)(23,"ion-item",22),P(24,"ion-icon",24),v(25,"ion-label")(26,"h2"),V(27,"Contact"),M(),v(28,"p"),V(29),M()()()(),wA(30,ig,8,1,"div",25),v(31,"div",26)(32,"h2"),V(33,"Travel Time Estimates"),M(),v(34,"div",27),wA(35,ag,18,6,"ion-card",28),M()()()),m&2){let i=eA();K(2),k("ngIf",i.center.disaster_type&&i.center.disaster_type.toLowerCase().includes("earthquake")),K(),k("ngIf",i.center.disaster_type&&i.center.disaster_type.toLowerCase().includes("flood")),K(),k("ngIf",i.center.disaster_type&&i.center.disaster_type.toLowerCase().includes("typhoon")),K(),k("ngIf",!i.center.disaster_type||!i.center.disaster_type.toLowerCase().includes("earthquake")&&!i.center.disaster_type.toLowerCase().includes("flood")&&!i.center.disaster_type.toLowerCase().includes("typhoon")),K(2),IA(i.center.name),K(2),k("color",i.getStatusColor(i.center.status)),K(),k("name",i.getDisasterTypeIcon(i.center.disaster_type)),K(2),IA(i.center.disaster_type||"General"),K(),k("ngIf",i.center.status),K(8),IA(i.center.address||"No address available"),K(8),IA(i.center.contact||"No contact information available"),K(),k("ngIf",i.center.capacity),K(5),k("ngForOf",i.travelEstimates)}}var Ii=(()=>{class m{constructor(i,l,B,g){this.modalCtrl=i,this.http=l,this.toastCtrl=B,this.mapboxRouting=g,this.travelEstimates=[],this.selectedMode="foot-walking",this.isLoading=!0}ngOnInit(){return X(this,null,function*(){this.isLoading=!0,yield this.calculateTravelTimes(),this.isLoading=!1})}calculateTravelTimes(){return X(this,null,function*(){let i=[{id:"foot-walking",name:"Walking",icon:"walk-outline",color:"primary"},{id:"cycling-regular",name:"Cycling",icon:"bicycle-outline",color:"success"},{id:"driving-car",name:"Driving",icon:"car-outline",color:"danger"}];this.travelEstimates=[];let l=Number(this.center.latitude),B=Number(this.center.longitude);if(console.log("Calculating travel times with coordinates:",{userLat:this.userLat,userLng:this.userLng,centerLat:l,centerLng:B}),isNaN(l)||isNaN(B)||isNaN(this.userLat)||isNaN(this.userLng)){console.error("Invalid coordinates for travel time calculations:",{userLat:this.userLat,userLng:this.userLng,centerLat:l,centerLng:B}),this.toastCtrl.create({message:"Invalid coordinates. Using estimated travel times.",duration:3e3,color:"warning",position:"bottom"}).then(g=>g.present()),this.useFallbackCalculations(i);return}for(let g of i)try{let Q=yield this.getTravelTimeEstimate(this.userLat,this.userLng,l,B,g.id);this.travelEstimates.push({mode:g.id,time:Q.time,distance:Q.distance,icon:g.icon,color:g.color})}catch(Q){if(console.error(`Error calculating ${g.name} time:`,Q),this.travelEstimates.length===0){let p="Using estimated travel times due to connection issues";Q.message&&(Q.message.includes("Invalid coordinates")?p="Invalid coordinates. Using estimated travel times.":Q.message.includes("API Error")&&(p=`${Q.message}. Using estimated travel times.`)),this.toastCtrl.create({message:p,duration:3e3,color:"warning",position:"bottom"}).then(T=>T.present())}let h=this.calculateStraightLineDistance(this.userLat,this.userLng,l,B),F;switch(g.id){case"foot-walking":F=5e3/3600;break;case"cycling-regular":F=15e3/3600;break;case"driving-car":F=4e4/3600;break;default:F=5e3/3600}let d=h/F;this.travelEstimates.push({mode:g.id,time:d,distance:h,icon:g.icon,color:g.color})}})}useFallbackCalculations(i){let l=Number(this.center.latitude),B=Number(this.center.longitude),g=isNaN(this.userLat)?10.3157:this.userLat,Q=isNaN(this.userLng)?123.8854:this.userLng,h=isNaN(l)?10.3257:l,F=isNaN(B)?123.8954:B,d=this.calculateStraightLineDistance(g,Q,h,F);console.log(`Using fallback calculation with distance: ${d} meters`);for(let p of i){let T;switch(p.id){case"foot-walking":T=5e3/3600;break;case"cycling-regular":T=15e3/3600;break;case"driving-car":T=4e4/3600;break;default:T=5e3/3600}let D=d/T;this.travelEstimates.push({mode:p.id,time:D,distance:d,icon:p.icon,color:p.color})}}getTravelTimeEstimate(i,l,B,g,Q){return X(this,null,function*(){if([i,l,B,g].some(h=>typeof h!="number"||isNaN(h)))throw console.error("Invalid coordinates for travel time estimate:",{startLat:i,startLng:l,endLat:B,endLng:g}),new Error("Invalid coordinates");if(Math.abs(i)>90||Math.abs(B)>90||Math.abs(l)>180||Math.abs(g)>180)throw console.error("Coordinates out of range for travel time estimate:",{startLat:i,startLng:l,endLat:B,endLng:g}),new Error("Coordinates out of range");console.log(`Calculating Mapbox route from [${i}, ${l}] to [${B}, ${g}] using mode: ${Q}`);try{let h=this.mapboxRouting.convertTravelModeToProfile(Q),F=yield this.mapboxRouting.getDirections(l,i,g,B,h,{geometries:"geojson",overview:"simplified",steps:!1});if(!F.routes||F.routes.length===0)throw new Error("No routes found");let d=F.routes[0];return console.log(`Received Mapbox response for ${Q} route:`,{duration:d.duration,distance:d.distance}),{time:d.duration,distance:d.distance}}catch(h){throw console.error(`Failed to fetch ${Q} route from Mapbox:`,h),h.message?h.message.includes("Invalid Mapbox access token")?new Error("Invalid Mapbox access token. Please check your token configuration."):h.message.includes("Rate limit exceeded")?new Error("Too many requests to Mapbox. Please wait a moment and try again."):h.message.includes("Network error")?new Error("Network error. Please check your internet connection."):h.message.includes("No routes found")?new Error("No route could be calculated between these points."):new Error(`Mapbox routing error: ${h.message}`):h}})}calculateStraightLineDistance(i,l,B,g){let h=i*Math.PI/180,F=B*Math.PI/180,d=(B-i)*Math.PI/180,p=(g-l)*Math.PI/180,T=Math.sin(d/2)*Math.sin(d/2)+Math.cos(h)*Math.cos(F)*Math.sin(p/2)*Math.sin(p/2);return 6371e3*(2*Math.atan2(Math.sqrt(T),Math.sqrt(1-T)))}formatTime(i){let l=Math.round(i/60);if(l<60)return`${l} min`;{let B=Math.floor(l/60),g=l%60;return`${B} hr ${g} min`}}formatDistance(i){return i<1e3?`${Math.round(i)} m`:`${(i/1e3).toFixed(2)} km`}selectTravelMode(i){this.selectedMode=i,this.dismiss(i)}dismiss(i){this.modalCtrl.dismiss({selectedMode:i||null})}getDisasterTypeIcon(i){if(!i)return"alert-circle-outline";let l=i.toLowerCase();return l.includes("earthquake")||l.includes("quake")?"earth-outline":l.includes("flood")||l.includes("flash")?"water-outline":l.includes("typhoon")||l.includes("storm")?"thunderstorm-outline":l.includes("fire")?"flame-outline":"alert-circle-outline"}getStatusColor(i){if(!i)return"medium";let l=i.toLowerCase();return l.includes("active")||l.includes("open")?"success":l.includes("inactive")||l.includes("closed")?"warning":l.includes("full")?"danger":"medium"}static{this.\u0275fac=function(l){return new(l||m)(ze(Yt),ze(Xt),ze(je),ze($t))}}static{this.\u0275cmp=me({type:m,selectors:[["app-evacuation-center-details"]],inputs:{center:"center",userLat:"userLat",userLng:"userLng"},decls:14,vars:2,consts:[[1,"ion-no-border"],["slot","start"],[3,"click"],["name","chevron-back-outline","slot","icon-only"],[1,"ion-padding"],["class","loading-container",4,"ngIf"],["class","details-container",4,"ngIf"],["expand","block","color","medium",3,"click"],[1,"loading-container"],["name","circles"],[1,"details-container"],[1,"center-image"],["src","assets/earthquake.png",3,"alt",4,"ngIf"],["src","assets/flood.png",3,"alt",4,"ngIf"],["src","assets/typhoon.png",3,"alt",4,"ngIf"],["src","assets/ALERTO.png",3,"alt",4,"ngIf"],[1,"center-name"],[1,"center-type"],[3,"color"],[3,"name"],[3,"color",4,"ngIf"],[1,"info-section"],["lines","none"],["name","location-outline","slot","start","color","primary"],["name","call-outline","slot","start","color","primary"],["class","info-section",4,"ngIf"],[1,"travel-section"],[1,"travel-cards"],["class","travel-card",3,"click",4,"ngFor","ngForOf"],["src","assets/earthquake.png",3,"alt"],["src","assets/flood.png",3,"alt"],["src","assets/typhoon.png",3,"alt"],["src","assets/ALERTO.png",3,"alt"],["name","information-circle-outline"],["name","people-outline","slot","start","color","primary"],[1,"travel-card",3,"click"],[1,"travel-icon",3,"name","color"],[1,"travel-info"],[1,"travel-time"],["name","time-outline"],[1,"travel-distance"],["name","navigate-outline"],["expand","block","fill","clear",3,"click","color"],["name","arrow-forward-outline","slot","end"]],template:function(l,B){l&1&&(v(0,"ion-header",0)(1,"ion-toolbar")(2,"ion-buttons",1)(3,"ion-button",2),nA("click",function(){return B.dismiss()}),P(4,"ion-icon",3),M()(),v(5,"ion-title"),V(6,"Evacuation Center"),M()()(),v(7,"ion-content",4),wA(8,Ag,4,0,"div",5)(9,sg,36,13,"div",6),M(),v(10,"ion-footer",0)(11,"ion-toolbar")(12,"ion-button",7),nA("click",function(){return B.dismiss()}),V(13," Back to Map "),M()()()),l&2&&(K(8),k("ngIf",B.isLoading),K(),k("ngIf",!B.isLoading))},dependencies:[be,He,ni,oi,ii,ai,si,ci,Jt,ui,fi,Ie,Wt,ye,hi,Ci,Qi,Ee,Vt,ve],styles:["ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--background: transparent;--border-color: transparent}.loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:200px}.loading-container[_ngcontent-%COMP%]   ion-spinner[_ngcontent-%COMP%]{width:48px;height:48px;margin-bottom:16px}.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:var(--ion-color-medium)}.details-container[_ngcontent-%COMP%]{padding-bottom:20px}.center-image[_ngcontent-%COMP%]{width:100%;height:180px;border-radius:12px;overflow:hidden;margin-bottom:16px;background-color:#f5f5f5;display:flex;justify-content:center;align-items:center}.center-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:100%;max-height:100%;object-fit:contain;padding:16px}.center-name[_ngcontent-%COMP%]{font-size:24px;font-weight:700;margin:0 0 8px;color:var(--ion-color-dark)}.center-type[_ngcontent-%COMP%]{display:flex;gap:8px;margin-bottom:16px;flex-wrap:wrap}.info-section[_ngcontent-%COMP%]{margin-bottom:16px}.info-section[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;--inner-padding-end: 0;--background: transparent}.info-section[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px}.info-section[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:16px;font-weight:600;margin-bottom:4px}.info-section[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:14px;color:var(--ion-color-medium)}.travel-section[_ngcontent-%COMP%]{margin-top:24px}.travel-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:18px;font-weight:600;margin-bottom:16px;color:var(--ion-color-dark)}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]{margin:0;border-radius:12px;box-shadow:0 4px 12px #00000014;transition:transform .2s ease,box-shadow .2s ease}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]:active{transform:scale(.98);box-shadow:0 2px 8px #0000001a}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]{display:flex;align-items:center;padding:12px 16px 0}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]   .travel-icon[_ngcontent-%COMP%]{font-size:28px;margin-right:12px}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%]{font-size:18px;font-weight:600}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:12px 16px 16px}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .travel-info[_ngcontent-%COMP%]{display:flex;justify-content:space-between;margin-bottom:12px}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .travel-info[_ngcontent-%COMP%]   .travel-time[_ngcontent-%COMP%], .travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .travel-info[_ngcontent-%COMP%]   .travel-distance[_ngcontent-%COMP%]{display:flex;align-items:center}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .travel-info[_ngcontent-%COMP%]   .travel-time[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%], .travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .travel-info[_ngcontent-%COMP%]   .travel-distance[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px;margin-right:6px;color:var(--ion-color-medium)}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .travel-info[_ngcontent-%COMP%]   .travel-time[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .travel-info[_ngcontent-%COMP%]   .travel-distance[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:15px;font-weight:500;color:var(--ion-color-dark)}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{margin-top:8px;font-weight:500}ion-footer[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--background: transparent;--border-color: transparent;padding:0 16px 16px}"]})}}return m})();function cg(m,R){if(m&1&&(v(0,"p"),V(1),M()),m&2){let i=eA();K(),ei(" ",i.formatDistance(i.totalDistance)," \u2022 ",i.formatTime(i.totalDuration)," ")}}function lg(m,R){if(m&1&&(v(0,"span"),V(1),M()),m&2){let i=eA(2).$implicit,l=eA();K(),yA(" \u2022 ",l.formatTime(i.duration),"")}}function Bg(m,R){if(m&1&&(v(0,"p"),V(1),wA(2,lg,2,1,"span",4),M()),m&2){let i=eA().$implicit,l=eA();K(),yA(" ",l.formatDistance(i.distance)," "),K(),k("ngIf",i.duration>0)}}function gg(m,R){if(m&1&&(v(0,"ion-item",9),P(1,"ion-icon",3),v(2,"ion-label"),P(3,"h3",10),wA(4,Bg,3,2,"p",4),M(),v(5,"ion-note",11),V(6),M()()),m&2){let i=R.$implicit,l=R.index,B=eA();K(),k("name",B.getDirectionIcon(i.type))("color",B.getTravelModeColor()),K(2),k("innerHTML",i.instruction,jo),K(),k("ngIf",i.distance>0),K(2),IA(l+1)}}var yi=(()=>{class m{constructor(){this.directions=[],this.travelMode="foot-walking",this.totalDistance=null,this.totalDuration=null,this.close=new qo}getTravelModeName(){switch(this.travelMode){case"foot-walking":return"Walking";case"cycling-regular":return"Cycling";case"driving-car":return"Driving";default:return"Traveling"}}getTravelModeIcon(){switch(this.travelMode){case"foot-walking":return"walk-outline";case"cycling-regular":return"bicycle-outline";case"driving-car":return"car-outline";default:return"navigate-outline"}}getTravelModeColor(){switch(this.travelMode){case"foot-walking":return"primary";case"cycling-regular":return"success";case"driving-car":return"danger";default:return"medium"}}formatTime(i){let l=Math.round(i/60);if(l<60)return`${l} min`;{let B=Math.floor(l/60),g=l%60;return`${B} hr ${g} min`}}formatDistance(i){return i<1e3?`${Math.round(i)} m`:`${(i/1e3).toFixed(2)} km`}closePanel(){this.close.emit()}getDirectionIcon(i){switch(i){case 0:return"arrow-forward-outline";case 1:return"arrow-forward-outline";case 2:return"arrow-forward-outline";case 3:return"arrow-forward-outline";case 4:return"arrow-back-outline";case 5:return"arrow-back-outline";case 6:return"arrow-back-outline";case 7:return"arrow-back-outline";case 8:return"arrow-down-outline";case 9:return"flag-outline";case 10:return"arrow-up-outline";case 11:return"arrow-forward-outline";case 12:return"arrow-forward-outline";case 13:return"arrow-forward-outline";case 14:return"arrow-forward-outline";case 15:return"flag-outline";default:return"navigate-outline"}}static{this.\u0275fac=function(l){return new(l||m)}}static{this.\u0275cmp=me({type:m,selectors:[["app-directions-panel"]],inputs:{directions:"directions",travelMode:"travelMode",totalDistance:"totalDistance",totalDuration:"totalDuration"},outputs:{close:"close"},decls:13,vars:5,consts:[[1,"directions-panel"],[1,"directions-header"],["lines","none"],["slot","start",3,"name","color"],[4,"ngIf"],["fill","clear","slot","end",3,"click"],["name","close-outline","slot","icon-only"],[1,"directions-list"],["lines","full",4,"ngFor","ngForOf"],["lines","full"],[3,"innerHTML"],["slot","end"]],template:function(l,B){l&1&&(v(0,"div",0)(1,"div",1)(2,"ion-item",2),P(3,"ion-icon",3),v(4,"ion-label")(5,"h2"),V(6),M(),wA(7,cg,2,2,"p",4),M(),v(8,"ion-button",5),nA("click",function(){return B.closePanel()}),P(9,"ion-icon",6),M()()(),v(10,"div",7)(11,"ion-list"),wA(12,gg,7,5,"ion-item",8),M()()()),l&2&&(K(3),k("name",B.getTravelModeIcon())("color",B.getTravelModeColor()),K(3),yA("",B.getTravelModeName()," Directions"),K(),k("ngIf",B.totalDistance&&B.totalDuration),K(5),k("ngForOf",B.directions))},dependencies:[be,He,Ie,Wt,ye,wi,di,Ee,Vt,ve],styles:[".directions-panel[_ngcontent-%COMP%]{position:absolute;bottom:0;left:0;right:0;background-color:#fff;border-top-left-radius:15px;border-top-right-radius:15px;box-shadow:0 -2px 10px #0000001a;max-height:50vh;overflow-y:auto;z-index:1000}.directions-header[_ngcontent-%COMP%]{padding:10px 0;border-bottom:1px solid #eee;position:sticky;top:0;background-color:#fff;z-index:1001}.directions-list[_ngcontent-%COMP%]{max-height:calc(50vh - 60px);overflow-y:auto}ion-item[_ngcontent-%COMP%]{--padding-start: 16px;--inner-padding-end: 16px}ion-icon[_ngcontent-%COMP%]{font-size:24px}h2[_ngcontent-%COMP%]{font-weight:600;margin:0}h3[_ngcontent-%COMP%]{font-size:14px;font-weight:500;margin:0}p[_ngcontent-%COMP%]{font-size:12px;color:var(--ion-color-medium);margin:4px 0 0}ion-note[_ngcontent-%COMP%]{font-size:12px;padding:4px 8px;border-radius:50%;background-color:var(--ion-color-light);color:var(--ion-color-dark);display:flex;align-items:center;justify-content:center;min-width:24px;min-height:24px}"]})}}return m})();var Li=zo(bi());function ug(m,R){m&1&&(v(0,"div",37),P(1,"ion-icon",32),v(2,"span"),V(3,"Offline Mode - Using cached data"),M()())}function fg(m,R){if(m&1){let i=Ae();v(0,"div",38)(1,"ion-button",39),nA("click",function(){GA(i);let B=eA();return RA(B.requestLocationExplicitly())}),P(2,"ion-icon",40),V(3," Enable Location Access "),M(),v(4,"p",41),V(5,"Tap the button above to enable location access"),M()()}}function wg(m,R){m&1&&(v(0,"div",42),P(1,"ion-icon",43),v(2,"p"),V(3,"Showing your current location"),M(),v(4,"small"),V(5,"Search for evacuation centers or select a disaster type to see routes"),M()())}function dg(m,R){if(m&1){let i=Ae();v(0,"div",44),nA("click",function(){GA(i);let B=eA();return RA(B.showDirectionsPanel=!0)}),P(1,"ion-icon",45),v(2,"div",46)(3,"strong"),V(4),M(),V(5),v(6,"div",47),V(7),M()(),P(8,"ion-icon",48),M()}if(m&2){let i=eA();K(),k("name",i.travelMode==="foot-walking"?"walk-outline":i.travelMode==="cycling-regular"?"bicycle-outline":"car-outline")("color",i.travelMode==="foot-walking"?"primary":i.travelMode==="cycling-regular"?"success":"danger"),K(3),yA("",(i.routeTime/60).toFixed(0)," min"),K(),yA(" \u2022 ",(i.routeDistance/1e3).toFixed(2)," km "),K(2),IA(i.getTravelModeName())}}function hg(m,R){if(m&1){let i=Ae();v(0,"app-directions-panel",49),nA("close",function(){GA(i);let B=eA();return RA(B.showDirectionsPanel=!1)}),M()}if(m&2){let i=eA();k("directions",i.currentDirections)("travelMode",i.travelMode)("totalDistance",i.routeDistance)("totalDuration",i.routeTime)}}function Cg(m,R){if(m&1&&(v(0,"div",50),P(1,"ion-icon",8),v(2,"span"),V(3),M()()),m&2){let i=eA();K(),k("name",i.currentDisasterType.toLowerCase().includes("earthquake")?"earth-outline":i.currentDisasterType.toLowerCase().includes("typhoon")?"thunderstorm-outline":i.currentDisasterType.toLowerCase().includes("flood")?"water-outline":"alert-circle-outline"),K(2),yA("",i.currentDisasterType," Evacuation Centers")}}function Qg(m,R){if(m&1){let i=Ae();v(0,"ion-fab",51)(1,"ion-fab-button",52),nA("click",function(){GA(i);let B=eA();return RA(B.routeToTwoNearestCenters())}),P(2,"ion-icon",53),M(),v(3,"ion-label",25),V(4,"Route to Nearest Centers"),M()()}}function pg(m,R){if(m&1){let i=Ae();v(0,"ion-fab",54)(1,"ion-fab-button",55),nA("click",function(){GA(i);let B=eA();return RA(B.showDirectionsPanel=!0)}),P(2,"ion-icon",56),M(),v(3,"ion-label",25),V(4,"Show Directions"),M()()}}var Zg=(()=>{class m{getTravelModeName(){switch(this.travelMode){case"foot-walking":return"Walking";case"cycling-regular":return"Cycling";case"driving-car":return"Driving";default:return"Traveling"}}findTwoNearestCenters(i,l,B){if(!B.length)return[];let g=B;if(this.currentDisasterType&&this.currentDisasterType!=="all"){console.log(`Filtering centers by disaster type: ${this.currentDisasterType}`);let h=this.currentDisasterType.toLowerCase();if(g=B.filter(F=>{if(!F.disaster_type)return!1;let d=F.disaster_type.toLowerCase();return h==="earthquake"||h==="earthquakes"?d.includes("earthquake")||d.includes("quake"):h==="typhoon"||h==="typhoons"?d.includes("typhoon")||d.includes("storm")||d.includes("hurricane"):h==="flood"||h==="floods"?d.includes("flood")||d.includes("flash"):d===h}),console.log(`Filtered to ${g.length} centers for disaster type: ${this.currentDisasterType}`),g.length===0)return console.log(`No centers found for disaster type: ${this.currentDisasterType}`),[]}return[...g].sort((h,F)=>{let d=this.calculateDistance(i,l,Number(h.latitude),Number(h.longitude)),p=this.calculateDistance(i,l,Number(F.latitude),Number(F.longitude));return d-p}).slice(0,2)}updateRoute(){this.routeToTwoNearestCenters()}requestLocationExplicitly(){return X(this,null,function*(){console.log("User explicitly requested location access via button click"),this.showLocationRequestButton=!1,yield this.loadingService.showLoading("Getting your location...");try{try{let g=yield pA.checkPermissions();if(console.log("Permission status:",g),g.location!=="granted"){console.log("Requesting permissions explicitly...");let Q=yield pA.requestPermissions();if(console.log("Permission request result:",Q),Q.location!=="granted")throw new Error("Location permission denied")}}catch(g){console.log("Permission check failed, might be in browser:",g)}let i=yield pA.getCurrentPosition({enableHighAccuracy:!0,timeout:3e4,maximumAge:0});console.log("Successfully got position:",i);let l=i.coords.latitude,B=i.coords.longitude;yield this.loadingService.dismissLoading(),this.toastCtrl.create({message:"Location access successful!",duration:2e3,color:"success"}).then(g=>g.present()),this.gpsEnabled=!0,this.map?(this.userMarker?(this.userMarker.setLatLng([l,B]),this.map.setView([l,B],15)):this.updateUserMarker(l,B),this.startWatchingPosition()):this.initializeMap(l,B)}catch(i){console.error("Error getting location:",i),yield this.loadingService.dismissLoading(),this.showLocationRequestButton=!0,yield(yield this.alertCtrl.create({header:"Location Access Failed",message:"We couldn't access your location. Would you like to see help on enabling location access?",buttons:[{text:"Show Help",handler:()=>{this.showLocationHelp()}},{text:"Try Again",handler:()=>{this.requestLocationExplicitly()}},{text:"Cancel",role:"cancel"}]})).present()}})}showLocationHelp(){return X(this,null,function*(){let i="To use location services:";navigator.userAgent.includes("Chrome")?i+='<br><br><b>Chrome:</b><br>1. Click the lock/info icon in the address bar<br>2. Select "Site settings"<br>3. Change Location permission to "Allow"<br>':navigator.userAgent.includes("Firefox")?i+='<br><br><b>Firefox:</b><br>1. Click the lock icon in the address bar<br>2. Select "Site Permissions"<br>3. Change "Access Your Location" to "Allow"<br>':navigator.userAgent.includes("Safari")?i+='<br><br><b>Safari:</b><br>1. Open Safari settings<br>2. Go to Websites > Location<br>3. Ensure this website is set to "Allow"<br>':i+="<br><br>Please enable location access for this website in your browser settings.",i+="<br><br>On mobile devices, also ensure that:<br>1. Your device location/GPS is turned on<br>2. The app has permission to access your location",yield(yield this.alertCtrl.create({header:"Location Services Help",message:i,buttons:[{text:"Try Again",handler:()=>{this.requestLocationExplicitly()}},{text:"OK",role:"cancel"}]})).present()})}routeToTwoNearestCenters(){return X(this,null,function*(){try{if(!this.gpsEnabled){console.log("GPS is disabled, not calculating routes"),(yield this.toastCtrl.create({message:"Please enable GPS to see evacuation routes",duration:3e3,color:"warning"})).present();return}console.log("Forcing fresh GPS position check for routing...");try{let i=yield this.getCurrentPositionWithFallback(),l=i.coords.latitude,B=i.coords.longitude;console.log(`Got fresh GPS position: [${l}, ${B}]`),this.userMarker?(this.userMarker.setLatLng([l,B]),this.map.setView([l,B],15)):this.userMarker=I.marker([l,B],{icon:I.icon({iconUrl:"assets/Location.png",iconSize:[32,32],iconAnchor:[16,32]})}).addTo(this.map);let g=l,Q=B;this.toastCtrl.create({message:"Using your current real-time location",duration:2e3,color:"success"}).then(F=>F.present()),console.log(`Using FRESH GPS coordinates for routing: [${g}, ${Q}]`),(!this.evacuationCenters||this.evacuationCenters.length===0)&&(yield this.loadEvacuationCenters(g,Q));let h=this.findTwoNearestCenters(g,Q,this.evacuationCenters);if(h.length===0){(yield this.toastCtrl.create({message:"No evacuation centers found.",duration:3e3,color:"danger"})).present();return}console.log("Aggressively clearing ALL existing routes"),this.map.eachLayer(F=>{F instanceof I.GeoJSON&&(console.log("Removing existing route layer"),this.map.removeLayer(F))});for(let F of h){let d=Number(F.latitude),p=Number(F.longitude);if(console.log(`Calculating route from [${g}, ${Q}] to center: ${F.name} with disaster type: ${F.disaster_type}`),console.log(`Center coordinates: [${d}, ${p}], types: [${typeof d}, ${typeof p}]`),isNaN(d)||isNaN(p)){console.error("Invalid center coordinates:",{centerLat:d,centerLng:p,center:F});continue}yield this.getRealRoute(g,Q,d,p,this.travelMode,F.disaster_type)}if(this.userMarker){let F="You are here!.";h.forEach((d,p)=>{let T=this.calculateDistance(g,Q,Number(d.latitude),Number(d.longitude));F+=`<br> \u2022 <strong>${d.name}</strong> <br> Distance: ${(T/1e3).toFixed(2)} km`}),this.userMarker.bindPopup(F).openPopup()}}catch(i){console.error("Failed to get fresh GPS position:",i),(yield this.toastCtrl.create({message:"Could not get your current location. Please check your GPS settings.",duration:3e3,color:"danger"})).present();return}}catch(i){(yield this.toastCtrl.create({message:"Failed to get your location or route.",duration:3e3,color:"danger"})).present(),console.error("Failed to route to two nearest centers",i)}})}constructor(){this.travelMode="foot-walking",this.routeTime=null,this.routeDistance=null,this.userMarker=null,this.evacuationCenters=[],this.gpsEnabled=!0,this.isOnline=!0,this.loadingService=LA(mi),this.mapboxRouting=LA($t),this.offlineStorage=LA(Ui),this.offlineMapService=LA(Fi),this.toastController=LA(je),this.alertCtrl=LA(pi),this.toastCtrl=LA(je),this.modalCtrl=LA(Yt),this.http=LA(Xt),this.watchId=null,this.currentDisasterType="all",this.isFilterMode=!1,this.currentDirections=[],this.showDirectionsPanel=!1,this.showLocationRequestButton=!1,this.ORS_API_KEY=ee.orsApiKey,this.isLoadingCenters=!1,this.lastErrorToast=0,this.ERROR_TOAST_DEBOUNCE=5e3,this.route=LA(ti)}setupNetworkMonitoring(){window.addEventListener("online",()=>{console.log("\u{1F310} Network connection restored"),this.handleNetworkOnline()}),window.addEventListener("offline",()=>{console.log("\u{1F4E1} Network connection lost - switching to offline mode"),this.handleNetworkOffline()}),navigator.onLine||(console.log("\u{1F4E1} Starting in offline mode"),this.handleNetworkOffline())}handleNetworkOnline(){return X(this,null,function*(){console.log("\u{1F310} Network connection restored - attempting to switch to online mode"),this.offlineStorage.setOfflineMode(!1),console.log("\u{1F310} Switching back to online mode"),this.map&&(this.map.eachLayer(i=>{i instanceof I.TileLayer&&this.map.removeLayer(i)}),I.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{attribution:"OpenStreetMap contributors",maxZoom:19,minZoom:8}).addTo(this.map)),yield this.syncOfflineData(),this.currentDisasterType&&this.currentDisasterType!=="all"&&(yield this.loadMapWithDisasterFilter(this.currentDisasterType,!1,!0)),this.showNetworkStatusToast("\u{1F310} Connection restored - Online mode active","success")})}handleNetworkOffline(){return X(this,null,function*(){console.log("\u{1F4E1} Automatically switching to offline mode due to network loss"),console.log("\u{1F50D} DEBUG: handleNetworkOffline called"),this.map&&(console.log("\u{1F50D} DEBUG: Switching map tiles to offline"),this.map.eachLayer(l=>{l instanceof I.TileLayer&&this.map.removeLayer(l)}),this.offlineMapService.createOfflineTileLayer().addTo(this.map),console.log("\u{1F50D} DEBUG: Offline tiles added")),console.log("\u{1F50D} DEBUG: About to load offline evacuation centers (all types)"),yield this.loadOfflineEvacuationCenters("all"),this.showOfflineTransitionAlert()})}showOfflineTransitionAlert(){return X(this,null,function*(){yield(yield this.alertCtrl.create({header:"\u{1F4E1} Connection Lost",message:`Your internet connection has been lost. The app has automatically switched to offline mode using cached data.

      <strong>Available offline:</strong>
      \u2022 Cached evacuation centers
      \u2022 Basic map tiles
      \u2022 Your current location

      <strong>Limited offline:</strong>
      \u2022 No routing/directions
      \u2022 No real-time updates`,buttons:[{text:"Continue Offline",role:"confirm",cssClass:"alert-button-confirm"}],cssClass:"offline-transition-alert"})).present()})}showNetworkStatusToast(i,l){return X(this,null,function*(){yield(yield this.toastController.create({message:i,duration:3e3,position:"top",color:l,buttons:[{text:"OK",role:"cancel"}]})).present()})}syncOfflineData(){return X(this,null,function*(){try{console.log("\u{1F504} Syncing evacuation centers for offline use...");let i=yield Ze(this.http.get(`${ee.apiUrl}/evacuation-centers`));if(i&&i.length>0){console.log("\u{1F50D} DEBUG: Raw centers from API:",i);let l=i.map(g=>({id:g.id,name:g.name,address:g.address||"",latitude:g.latitude,longitude:g.longitude,capacity:g.capacity,status:g.status,disaster_type:g.disaster_type,contact:g.contact}));console.log("\u{1F50D} DEBUG: Converted offline centers:",l),yield this.offlineStorage.saveEvacuationCenters(l),console.log(`\u2705 Synced ${i.length} evacuation centers for offline use`);let B=yield this.offlineStorage.getEvacuationCenters();console.log("\u{1F50D} DEBUG: Verified saved centers:",B)}}catch(i){console.warn("\u26A0\uFE0F Failed to sync offline data:",i)}})}toggleOfflineMode(){return X(this,null,function*(){this.offlineStorage.isOfflineMode()?(this.offlineStorage.setOfflineMode(!1),console.log("\u{1F310} Switched to online mode"),this.map&&(this.map.eachLayer(l=>{l instanceof I.TileLayer&&this.map.removeLayer(l)}),I.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{attribution:"OpenStreetMap contributors",maxZoom:19,minZoom:8}).addTo(this.map))):(this.offlineStorage.setOfflineMode(!0),console.log("\u{1F504} Switched to offline mode"),this.map&&(this.map.eachLayer(B=>{B instanceof I.TileLayer&&this.map.removeLayer(B)}),this.offlineMapService.createOfflineTileLayer().addTo(this.map)),yield this.loadOfflineEvacuationCenters("all"))})}loadOfflineEvacuationCenters(i){return X(this,null,function*(){try{console.log("\u{1F50D} DEBUG: Loading offline evacuation centers..."),console.log("\u{1F50D} DEBUG: Disaster type filter:",i);let l=yield this.offlineStorage.getEvacuationCenters();if(console.log("\u{1F50D} DEBUG: Raw offline centers loaded:",l),l&&l.length>0){console.log(`\u{1F4CD} Loaded ${l.length} evacuation centers from offline storage`);let B=l;i&&i!=="all"&&(B=l.filter(g=>g.disaster_type?.toLowerCase()===i.toLowerCase()),console.log(`\u{1F50D} DEBUG: Filtered to ${B.length} centers for disaster type: ${i}`)),this.evacuationCenters=B.map(g=>({id:g.id,name:g.name,address:g.address,latitude:g.latitude,longitude:g.longitude,capacity:g.capacity,status:g.status,disaster_type:g.disaster_type,contact:g.contact})),console.log("\u{1F50D} DEBUG: Final evacuation centers for map:",this.evacuationCenters),console.log("\u{1F50D} DEBUG: Disaster types in centers:",this.evacuationCenters.map(g=>g.disaster_type)),console.log("\u{1F50D} DEBUG: About to add offline markers..."),this.addOfflineMarkers()}else console.warn("\u26A0\uFE0F No offline evacuation centers available"),console.log("\u{1F50D} DEBUG: offlineCenters is:",l),this.evacuationCenters=[]}catch(l){console.error("\u274C Error loading offline evacuation centers:",l),this.evacuationCenters=[]}})}addOfflineMarkers(){if(console.log("\u{1F50D} DEBUG: addOfflineMarkers called"),console.log("\u{1F50D} DEBUG: Map exists?",!!this.map),console.log("\u{1F50D} DEBUG: Evacuation centers count:",this.evacuationCenters.length),console.log("\u{1F50D} DEBUG: Evacuation centers:",this.evacuationCenters),!this.map){console.error("\u274C Map not initialized");return}if(!this.evacuationCenters.length){console.warn("\u26A0\uFE0F No evacuation centers to display");return}let i=0;this.map.eachLayer(B=>{B instanceof I.Marker&&B!==this.userMarker&&(this.map.removeLayer(B),i++)}),console.log(`\u{1F9F9} Removed ${i} existing markers`);let l=0;this.evacuationCenters.forEach((B,g)=>{console.log(`\u{1F50D} DEBUG: Processing center ${g+1}:`,B);let Q=Number(B.latitude),h=Number(B.longitude);if(console.log(`\u{1F50D} DEBUG: Coordinates: lat=${Q}, lng=${h}`),!isNaN(Q)&&!isNaN(h)){let F=this.getDisasterIcon(B.disaster_type||"");console.log(`\u{1F50D} DEBUG: Icon URL: ${F}`);let d=I.marker([Q,h],{icon:I.icon({iconUrl:F,iconSize:[40,40],iconAnchor:[20,40],popupAnchor:[0,-40]})}),p=`
          <div class="evacuation-popup">
            <h3>${B.name||"Evacuation Center"}</h3>
            <p><strong>Type:</strong> ${B.disaster_type||"General"}</p>
            <p><strong>Address:</strong> ${B.address||"N/A"}</p>
            <p><strong>Capacity:</strong> ${B.capacity||"N/A"}</p>
            <p><strong>Status:</strong> ${B.status||"N/A"}</p>
            <p><em>Offline Mode - Limited functionality</em></p>
          </div>
        `;d.bindPopup(p),d.addTo(this.map),l++,console.log(`\u2705 Added marker ${l} for: ${B.name}`)}else console.error(`\u274C Invalid coordinates for center: ${B.name} (lat=${Q}, lng=${h})`)}),console.log(`\u2705 Added ${l} offline markers to map`)}debugLoadOfflineData(){return X(this,null,function*(){console.log("\u{1F41B} DEBUG: Force loading offline data (all disaster types)..."),yield this.loadOfflineEvacuationCenters("all")})}exportOfflineData(){return X(this,null,function*(){try{console.log("\u{1F4E6} Exporting offline data...");let i=yield this.offlineStorage.getEvacuationCenters(),l=this.offlineStorage.getLastSyncTime(),B=this.offlineStorage.getStorageInfo(),g={evacuation_centers:i,export_timestamp:new Date().toISOString(),last_sync_time:l,total_centers:i.length,storage_info:{used_mb:(B.used/(1024*1024)).toFixed(2),percentage:B.percentage.toFixed(1)},disaster_types:[...new Set(i.map(T=>T.disaster_type))],app_version:"Alerto v1.0"},Q=JSON.stringify(g,null,2),h=new Blob([Q],{type:"application/json"}),F=URL.createObjectURL(h),d=document.createElement("a");d.href=F,d.download=`alerto-offline-data-${new Date().toISOString().split("T")[0]}.json`,document.body.appendChild(d),d.click(),document.body.removeChild(d),URL.revokeObjectURL(F),yield(yield this.alertCtrl.create({header:"Export Successful",message:`Exported ${i.length} evacuation centers to your downloads folder.`,buttons:["OK"]})).present(),console.log("\u2705 Offline data exported successfully")}catch(i){console.error("\u274C Failed to export offline data:",i),yield(yield this.alertCtrl.create({header:"Export Failed",message:"Failed to export offline data. Please try again.",buttons:["OK"]})).present()}})}shareOfflineData(){return X(this,null,function*(){try{console.log("\u{1F4E4} Sharing offline data...");let i=yield this.offlineStorage.getEvacuationCenters();if(i.length===0){yield(yield this.alertCtrl.create({header:"No Data to Share",message:"No offline evacuation data available to share. Please sync data first.",buttons:["OK"]})).present();return}let l=`Alerto Evacuation Centers Data

Total Centers: ${i.length}
Disaster Types: ${[...new Set(i.map(B=>B.disaster_type))].join(", ")}

Centers:
`+i.map(B=>`\u2022 ${B.name} (${B.disaster_type})
  ${B.address}
  Coordinates: ${B.latitude}, ${B.longitude}`).join(`

`)+`

Exported from Alerto App on ${new Date().toLocaleDateString()}`;navigator.share?yield navigator.share({title:"Alerto Evacuation Centers",text:l}):(yield navigator.clipboard.writeText(l),yield(yield this.toastCtrl.create({message:"Evacuation data copied to clipboard!",duration:3e3,color:"success"})).present()),console.log("\u2705 Offline data shared successfully")}catch(i){console.error("\u274C Failed to share offline data:",i),yield(yield this.toastCtrl.create({message:"Failed to share data. Please try again.",duration:3e3,color:"danger"})).present()}})}getDisasterIcon(i){if(!i)return"assets/forTyphoon.png";switch(i){case"Earthquake":return"assets/forEarthquake.png";case"Flood":return"assets/forFlood.png";case"Typhoon":return"assets/forTyphoon.png";default:return console.warn(`Unknown disaster type: ${i}, using default icon`),"assets/forTyphoon.png"}}getDisasterColor(i){if(!i)return"#3388ff";switch(i){case"Earthquake":return"#ffa500";case"Flood":return"#0000ff";case"Typhoon":return"#008000";default:return console.warn(`Unknown disaster type: ${i}, using default color`),"#3388ff"}}clearPulseCircles(){this.map.eachLayer(i=>{i instanceof I.Circle&&i.options.className==="marker-pulse"&&this.map.removeLayer(i)})}addPulsingAnimationToNearest(i){if(!i)return;this.clearPulseCircles();let l=Number(i.latitude),B=Number(i.longitude);if(isNaN(l)||isNaN(B)){console.error("Invalid coordinates for nearest center:",i);return}let g=this.getDisasterColor(i.disaster_type);I.circle([l,B],{radius:100,fillColor:g,color:g,weight:2,opacity:.8,fillOpacity:.3,className:"marker-pulse"}).addTo(this.map),console.log(`Added pulsing animation to nearest center: ${i.name} with color: ${g}`)}hasRecentErrorToast(){return Date.now()-this.lastErrorToast<this.ERROR_TOAST_DEBOUNCE}setLastErrorToast(){this.lastErrorToast=Date.now()}toggleGps(i){return X(this,null,function*(){if(console.log("GPS toggle:",i.detail.checked),this.gpsEnabled=i.detail.checked,this.gpsEnabled){console.log("Enabling GPS tracking...");try{let l=yield this.getCurrentPositionWithFallback();console.log("Position on toggle:",l);let B=l.coords.latitude,g=l.coords.longitude;this.userMarker?(this.userMarker.setLatLng([B,g]),this.userMarker.addTo(this.map)):this.updateUserMarker(B,g),this.map.setView([B,g],15),this.startWatchingPosition()}catch(l){console.error("Error enabling GPS:",l),this.gpsEnabled=!1,(yield this.toastCtrl.create({message:"Failed to enable GPS. Please check your location settings.",duration:3e3,color:"danger"})).present()}}else if(console.log("Disabling GPS tracking..."),this.userMarker&&this.userMarker.remove(),this.watchId){if(typeof this.watchId=="string")try{let l=this.watchId;pA.clearWatch({id:l})}catch(l){console.log("Error clearing Capacitor watch:",l)}else if(typeof this.watchId=="number")try{navigator.geolocation.clearWatch(this.watchId)}catch(l){console.log("Error clearing browser watch:",l)}this.watchId=null}})}ngOnInit(){return X(this,null,function*(){console.log("\u{1F5FA}\uFE0F MAIN MAP: Initializing clean map (tabs/map)..."),this.setupNetworkMonitoring(),navigator.onLine&&!this.offlineStorage.isOfflineMode()&&(yield this.syncOfflineData()),this.route.queryParams.subscribe(i=>{if(i.centerId){let l=i.centerId;console.log(`\u{1F50D} SEARCH NAVIGATION: Loading specific center ID: ${l}`),this.loadSpecificCenter(l);return}if(i.lat&&i.lng){let l=parseFloat(i.lat),B=parseFloat(i.lng),g=i.name||"Search Result",Q=i.directions==="true",h=i.viewOnly==="true";console.log(`\u{1F50D} SEARCH NAVIGATION: Loading location [${l}, ${B}] - ${g}, directions: ${Q}, viewOnly: ${h}`),Q?this.loadSearchLocationWithRouting(l,B,g):h&&this.loadSearchLocation(l,B,g);return}if(i.searchLat&&i.searchLng){let l=parseFloat(i.searchLat),B=parseFloat(i.searchLng),g=i.searchName||"Search Result";console.log(`\u{1F50D} SEARCH NAVIGATION: Loading location [${l}, ${B}] - ${g}`),this.loadSearchLocation(l,B,g);return}console.log("\u{1F5FA}\uFE0F MAIN MAP: Loading clean map with user location only"),this.loadCleanMap()})})}loadCleanMap(){return X(this,null,function*(){console.log("\u{1F5FA}\uFE0F CLEAN MAP: Loading map with user location only..."),yield this.loadingService.showLoading("Loading map...");try{let i=yield pA.getCurrentPosition({enableHighAccuracy:!0,timeout:2e4}),l=i.coords.latitude,B=i.coords.longitude;console.log(`\u{1F5FA}\uFE0F CLEAN MAP: User location [${l}, ${B}]`),this.initializeMap(l,B),this.evacuationCenters=[],this.isFilterMode=!1,this.currentDisasterType="all",this.map.eachLayer(Q=>{Q instanceof I.Marker&&Q!==this.userMarker&&this.map.removeLayer(Q),Q instanceof I.GeoJSON&&this.map.removeLayer(Q)}),yield this.loadingService.dismissLoading(),yield(yield this.toastCtrl.create({message:"\u{1F4CD} Map ready - Search for evacuation centers to view them here",duration:3e3,color:"primary",position:"top"})).present()}catch(i){yield this.loadingService.dismissLoading(),console.error("\u{1F5FA}\uFE0F CLEAN MAP: Error loading map",i),yield(yield this.alertCtrl.create({header:"Location Error",message:"Unable to get your location. Please enable GPS and try again.",buttons:[{text:"Retry",handler:()=>this.loadCleanMap()},{text:"Use Default Location",handler:()=>this.initializeMap(10.3157,123.8854)}]})).present()}})}loadSpecificCenter(i){return X(this,null,function*(){console.log(`\u{1F50D} SPECIFIC CENTER: Loading center ID ${i}...`),yield this.loadingService.showLoading("Loading evacuation center...");try{let B=(yield Ze(this.http.get(`${ee.apiUrl}/evacuation-centers`))).find(z=>z.id.toString()===i);if(!B){yield this.loadingService.dismissLoading(),yield(yield this.alertCtrl.create({header:"Center Not Found",message:"The requested evacuation center could not be found.",buttons:["OK"]})).present(),this.loadCleanMap();return}let g=yield pA.getCurrentPosition({enableHighAccuracy:!0,timeout:2e4}),Q=g.coords.latitude,h=g.coords.longitude,F=Number(B.latitude),d=Number(B.longitude);this.initializeMap(Q,h);let p=this.getDisasterIcon(B.disaster_type||""),T=I.marker([F,d],{icon:I.icon({iconUrl:p,iconSize:[40,40],iconAnchor:[20,40],popupAnchor:[0,-40]})});T.bindPopup(`
        <div class="evacuation-popup">
          <h3>${B.name}</h3>
          <p><strong>Type:</strong> ${B.disaster_type||"General"}</p>
          <p><strong>Address:</strong> ${B.address}</p>
          <p><strong>Capacity:</strong> ${B.capacity||"N/A"}</p>
        </div>
      `).openPopup(),T.addTo(this.map);let D=I.latLngBounds([[Q,h],[F,d]]);this.map.fitBounds(D,{padding:[50,50]}),yield this.loadingService.dismissLoading(),yield(yield this.toastCtrl.create({message:`\u{1F4CD} Showing ${B.name}`,duration:3e3,color:"success"})).present()}catch(l){yield this.loadingService.dismissLoading(),console.error("\u{1F50D} SPECIFIC CENTER: Error loading center",l),yield(yield this.toastCtrl.create({message:"Error loading evacuation center. Please try again.",duration:3e3,color:"danger"})).present(),this.loadCleanMap()}})}loadSearchLocation(i,l,B){return X(this,null,function*(){console.log(`\u{1F50D} SEARCH LOCATION: Loading [${i}, ${l}] - ${B}...`),yield this.loadingService.showLoading("Loading location...");try{let g=yield pA.getCurrentPosition({enableHighAccuracy:!0,timeout:2e4}),Q=g.coords.latitude,h=g.coords.longitude;this.initializeMap(Q,h);let F=I.marker([i,l],{icon:I.icon({iconUrl:"assets/Location.png",iconSize:[40,40],iconAnchor:[20,40],popupAnchor:[0,-40]})});F.bindPopup(`
        <div class="search-popup">
          <h3>\u{1F4CD} ${B}</h3>
          <p>Search result location</p>
        </div>
      `).openPopup(),F.addTo(this.map);let d=I.latLngBounds([[Q,h],[i,l]]);this.map.fitBounds(d,{padding:[50,50]}),yield this.loadingService.dismissLoading(),yield(yield this.toastCtrl.create({message:`\u{1F4CD} Showing ${B}`,duration:3e3,color:"primary"})).present()}catch(g){yield this.loadingService.dismissLoading(),console.error("\u{1F50D} SEARCH LOCATION: Error loading location",g),yield(yield this.toastCtrl.create({message:"Error loading search location. Please try again.",duration:3e3,color:"danger"})).present(),this.loadCleanMap()}})}loadMapWithSearchLocation(i,l,B,g=!1){return X(this,null,function*(){yield this.loadingService.showLoading("Loading selected location...");try{if(console.log(`Initializing map with search location: [${i}, ${l}], name: ${B}`),this.isFilterMode=!1,this.currentDisasterType="all",this.initializeMap(i,l),this.map.eachLayer(h=>{h instanceof I.Marker&&h!==this.userMarker&&this.map.removeLayer(h),h instanceof I.GeoJSON&&this.map.removeLayer(h)}),I.marker([i,l],{icon:I.icon({iconUrl:"assets/Location.png",iconSize:[32,32],iconAnchor:[16,32]})}).addTo(this.map).bindPopup(`<b>${B}</b><br>Selected evacuation center`).openPopup(),this.gpsEnabled)try{let h=yield this.getCurrentPositionWithFallback(),F=h.coords.latitude,d=h.coords.longitude;if(this.updateUserMarker(F,d),g){this.map.eachLayer(T=>{T instanceof I.GeoJSON&&this.map.removeLayer(T)}),yield this.getRealRoute(F,d,i,l,this.travelMode),this.toastCtrl.create({message:`Showing directions to ${B}`,duration:3e3,color:"success"}).then(T=>T.present());let p=I.latLngBounds([[F,d],[i,l]]);this.map.fitBounds(p,{padding:[50,50]})}else this.toastCtrl.create({message:`Showing ${B} on map`,duration:2e3,color:"primary"}).then(p=>p.present())}catch(h){console.error("Error getting user location for routing:",h),g&&this.toastCtrl.create({message:"Could not get your location to calculate directions. Please check your GPS settings.",duration:3e3,color:"warning"}).then(F=>F.present())}else g&&this.toastCtrl.create({message:"Please enable GPS to get directions",duration:3e3,color:"warning"}).then(h=>h.present());yield this.loadingService.dismissLoading()}catch(Q){console.error("Error loading search location:",Q),yield this.loadingService.dismissLoading(),this.toastCtrl.create({message:"Failed to load selected location. Please try again.",duration:3e3,color:"danger"}).then(h=>h.present()),this.loadMapWithUserLocation()}})}loadSearchLocationWithRouting(i,l,B){return X(this,null,function*(){console.log(`\u{1F50D} SEARCH ROUTING: Loading search location with routing [${i}, ${l}] - ${B}`);try{let g=yield pA.getCurrentPosition({enableHighAccuracy:!0,timeout:2e4}),Q=g.coords.latitude,h=g.coords.longitude;console.log(`\u{1F50D} SEARCH ROUTING: User location [${Q}, ${h}]`),console.log(`\u{1F50D} SEARCH ROUTING: Target location [${i}, ${l}] - ${B}`),this.isFilterMode=!1,this.currentDisasterType="all",this.initializeMap(Q,h),this.map.eachLayer(d=>{d instanceof I.Marker&&d!==this.userMarker&&this.map.removeLayer(d),d instanceof I.GeoJSON&&this.map.removeLayer(d)}),this.userMarker=I.marker([Q,h],{icon:I.icon({iconUrl:"assets/Location.png",iconSize:[30,30],iconAnchor:[15,30]})}).addTo(this.map),this.userMarker.bindPopup("\u{1F4CD} You are here!"),I.marker([i,l],{icon:I.icon({iconUrl:"assets/forEarthquake.png",iconSize:[40,40],iconAnchor:[20,40]})}).addTo(this.map).bindPopup(`<b>${B}</b><br>Selected evacuation center`).openPopup(),yield this.showTransportationOptionsForSearch(i,l,B),console.log(`\u{1F50D} SEARCH ROUTING: Successfully loaded search location with routing: ${B}`)}catch(g){console.error("\u{1F50D} SEARCH ROUTING: Error loading search location with routing:",g),(yield this.toastCtrl.create({message:"Error getting your location for routing. Please enable GPS and try again.",duration:3e3,color:"danger"})).present()}})}showTransportationOptionsForSearch(i,l,B){return X(this,null,function*(){yield(yield this.alertCtrl.create({header:`Route to ${B}`,message:"Choose your transportation mode:",buttons:[{text:"\u{1F6B6}\u200D\u2642\uFE0F Walk",handler:()=>{this.routeToSearchLocation(i,l,B,"walking")}},{text:"\u{1F6B4}\u200D\u2642\uFE0F Cycle",handler:()=>{this.routeToSearchLocation(i,l,B,"cycling")}},{text:"\u{1F697} Drive",handler:()=>{this.routeToSearchLocation(i,l,B,"driving")}},{text:"Cancel",role:"cancel"}]})).present()})}routeToSearchLocation(i,l,B,g){return X(this,null,function*(){if(this.userMarker)try{let Q=this.userMarker.getLatLng().lat,h=this.userMarker.getLatLng().lng,F=this.mapboxRouting.convertTravelModeToProfile(g),d=yield this.mapboxRouting.getDirections(h,Q,l,i,F,{geometries:"geojson",overview:"full",steps:!1});if(d&&d.routes&&d.routes.length>0){let p=d.routes[0],T="#3880ff";this.map.eachLayer(z=>{z instanceof I.GeoJSON&&this.map.removeLayer(z)});let D=I.polyline(p.geometry.coordinates.map(z=>[z[1],z[0]]),{color:T,weight:5,opacity:.8});D.addTo(this.map),yield(yield this.toastCtrl.create({message:`\u{1F5FA}\uFE0F Route: ${(p.distance/1e3).toFixed(2)}km, ${(p.duration/60).toFixed(0)}min via ${g}`,duration:4e3,color:"primary"})).present(),this.map.fitBounds(D.getBounds(),{padding:[50,50]})}}catch(Q){console.error("\u{1F50D} Error routing to search location:",Q),yield(yield this.toastCtrl.create({message:"Error calculating route. Please try again.",duration:3e3,color:"danger"})).present()}})}ngOnDestroy(){console.log("Map page destroyed, cleaning up resources"),this.stopWatchingPosition(),this.map&&this.map.remove()}getCurrentPositionWithFallback(){return X(this,null,function*(){try{console.log("Trying Capacitor Geolocation...");try{let i=yield pA.checkPermissions();if(console.log("Permission status:",i),i.location!=="granted"){console.log("Requesting permissions explicitly...");let l=yield pA.requestPermissions();if(console.log("Permission request result:",l),l.location!=="granted")throw new Error("Location permission denied")}}catch(i){console.log("Permission check failed, might be in browser:",i)}try{return console.log("Getting current position via Capacitor..."),yield pA.getCurrentPosition({enableHighAccuracy:!0,timeout:1e4})}catch(i){throw console.log("Capacitor Geolocation failed, trying browser fallback:",i),i}}catch{if(console.log("Trying browser geolocation fallback..."),navigator.geolocation)return new Promise((l,B)=>{navigator.geolocation.getCurrentPosition(g=>{console.log("Browser geolocation succeeded:",g),l({coords:{latitude:g.coords.latitude,longitude:g.coords.longitude,accuracy:g.coords.accuracy,altitude:g.coords.altitude,altitudeAccuracy:g.coords.altitudeAccuracy,heading:g.coords.heading,speed:g.coords.speed},timestamp:g.timestamp})},g=>{if(console.error("Browser geolocation failed:",g),g.code===1&&g.message.includes("secure origins")){let Q=new Error("Geolocation requires HTTPS. Please use a secure connection, run on a real device, or enable insecure origins in Chrome flags.");Q.code=g.code,B(Q)}else B(g)},{enableHighAccuracy:!0,timeout:1e4})});throw console.error("Geolocation not available in this browser"),new Error("Geolocation not available in this browser")}})}loadMapWithDisasterFilter(i,l=!1,B=!1){return X(this,null,function*(){yield this.loadingService.showLoading(`Loading ${i==="all"?"all evacuation centers":i+" evacuation centers"}...`);try{console.log("Getting user location for disaster map...");try{let g=yield this.getCurrentPositionWithFallback();console.log("Position received:",g);let Q=g.coords.latitude,h=g.coords.longitude;console.log(`Initializing disaster map with real GPS coordinates: [${Q}, ${h}]`),this.initializeMap(Q,h),this.startWatchingPosition(),yield this.loadEvacuationCentersFiltered(Q,h,i),l?this.toastCtrl.create({message:`\u{1F6A8} EMERGENCY: Showing nearest ${i} evacuation centers with routes`,duration:5e3,color:"danger",position:"top"}).then(F=>F.present()):this.toastCtrl.create({message:`Showing ${i==="all"?"all evacuation centers":i+" evacuation centers"} near you`,duration:3e3,color:"primary"}).then(F=>F.present()),yield this.loadingService.dismissLoading();return}catch(g){console.error("Failed to get GPS position for disaster map:",g),yield this.loadingService.dismissLoading(),yield(yield this.alertCtrl.create({header:"GPS Required",message:"We need your location to show nearby evacuation centers. Please enable GPS and try again.",buttons:[{text:"Enable GPS",handler:()=>{this.loadMapWithDisasterFilter(i)}},{text:"Cancel",role:"cancel"}]})).present()}}catch(g){console.error("Error loading disaster map:",g),yield this.loadingService.dismissLoading(),this.toastCtrl.create({message:"Failed to load evacuation centers. Please try again.",duration:3e3,color:"danger"}).then(Q=>Q.present())}})}loadMapWithOnlyUserLocation(){return X(this,null,function*(){yield this.loadingService.showLoading("Loading map...");try{console.log("Getting user location for map tab...");try{let i=yield this.getCurrentPositionWithFallback();console.log("Position received:",i);let l=i.coords.latitude,B=i.coords.longitude;console.log(`Initializing map with only user location: [${l}, ${B}]`),this.isFilterMode=!1,this.currentDisasterType="all",this.evacuationCenters=[],this.initializeMap(l,B),this.map.eachLayer(g=>{g instanceof I.Marker&&g!==this.userMarker&&this.map.removeLayer(g),g instanceof I.GeoJSON&&this.map.removeLayer(g)}),this.startWatchingPosition(),this.userMarker&&this.userMarker.bindPopup("You are here!").openPopup(),this.toastCtrl.create({message:"Showing your current location",duration:2e3,color:"success"}).then(g=>g.present()),yield this.loadingService.dismissLoading();return}catch(i){console.error("Failed to get GPS position for map tab:",i),yield this.loadingService.dismissLoading(),yield(yield this.alertCtrl.create({header:"Location Required",message:"We need your location to show the map. Please enable GPS and try again.",buttons:[{text:"Enable GPS",handler:()=>{this.loadMapWithOnlyUserLocation()}},{text:"Cancel",role:"cancel"}]})).present();return}}catch(i){console.error("Error loading map:",i),yield this.loadingService.dismissLoading(),this.toastCtrl.create({message:"Failed to load map. Please try again.",duration:3e3,color:"danger"}).then(l=>l.present())}})}loadMapWithUserLocation(){return X(this,null,function*(){yield this.loadingService.showLoading("Loading map...");try{console.log("Getting user location...");try{let i=yield this.getCurrentPositionWithFallback();console.log("Position received:",i);let l=i.coords.latitude,B=i.coords.longitude;console.log(`Initializing map with real GPS coordinates: [${l}, ${B}]`),this.isFilterMode||(this.currentDisasterType="all"),this.initializeMap(l,B),this.startWatchingPosition(),this.toastCtrl.create({message:"Using your real-time location",duration:2e3,color:"success"}).then(g=>g.present()),yield this.loadingService.dismissLoading();return}catch(i){throw console.error("Failed to get GPS position, showing alert:",i),i}}catch(i){console.error("Error getting location",i);let l="Unable to access your location. ";i.code===1?navigator.userAgent.includes("Chrome")?l+='Location permission denied. Please click the lock icon in the address bar, select "Site settings", and change Location permission to "Allow".':navigator.userAgent.includes("Firefox")?l+='Location permission denied. Please click the lock icon in the address bar, select "Site Permissions", and change "Access Your Location" to "Allow".':navigator.userAgent.includes("Safari")?l+='Location permission denied. Please check Safari settings > Websites > Location and ensure this website is set to "Allow".':l+="Location permission denied. Please enable location access for this website in your browser settings.":i.code===2?l+="Position unavailable. Your GPS signal might be weak or unavailable.":i.code===3?l+="Location request timed out. Please try again.":l+="Please enable GPS or try again. "+(i.message||""),yield(yield this.alertCtrl.create({header:"Location Error",message:l,buttons:[{text:"Retry",handler:()=>{this.loadMapWithUserLocation()}},{text:"Load Default Map",role:"cancel",handler:()=>{this.initializeMap(10.3157,123.8854)}}]})).present()}yield this.loadingService.dismissLoading()})}stopWatchingPosition(){if(this.watchId){if(console.log("Stopping position watch..."),typeof this.watchId=="string")try{let i=this.watchId;pA.clearWatch({id:i})}catch(i){console.log("Error clearing Capacitor watch:",i)}else if(typeof this.watchId=="number")try{navigator.geolocation.clearWatch(this.watchId)}catch(i){console.log("Error clearing browser watch:",i)}this.watchId=null}}startWatchingPosition(){this.stopWatchingPosition(),console.log("Starting position watch...");try{this.watchId=pA.watchPosition({enableHighAccuracy:!0,timeout:1e4},(i,l)=>{i&&this.gpsEnabled&&(console.log("Capacitor watch position update:",i),this.updateUserMarker(i.coords.latitude,i.coords.longitude)),l&&(console.error("Error watching position:",l),this.toastCtrl.create({message:"GPS signal lost or weak. Please check your location settings.",duration:3e3,color:"warning"}).then(B=>B.present()))}),console.log("Capacitor watch started with ID:",this.watchId)}catch(i){console.log("Capacitor watch failed, trying browser fallback:",i),navigator.geolocation?(this.watchId=navigator.geolocation.watchPosition(l=>{this.gpsEnabled&&(console.log("Browser watch position update:",l),this.updateUserMarker(l.coords.latitude,l.coords.longitude))},l=>{console.error("Browser watch error:",l),this.toastCtrl.create({message:"GPS signal lost or weak. Please check your location settings.",duration:3e3,color:"warning"}).then(B=>B.present())},{enableHighAccuracy:!0,timeout:1e4}),console.log("Browser watch started with ID:",this.watchId)):console.error("Geolocation watching not available")}}initializeMap(i,l){console.log(`Initializing map with coordinates: [${i}, ${l}]`),(isNaN(i)||isNaN(l)||Math.abs(i)>90||Math.abs(l)>180)&&(console.error("Invalid coordinates for map initialization:",{lat:i,lng:l}),i=12.8797,l=121.774,console.log(`Using fallback coordinates for Philippines: [${i}, ${l}]`)),this.map&&(console.log("Removing existing map"),this.map.remove()),this.map=I.map("map").setView([i,l],15),console.log("Map initialized"),this.offlineStorage.isOfflineMode()||!navigator.onLine?(console.log("\u{1F504} Loading offline map tiles..."),this.offlineMapService.createOfflineTileLayer().addTo(this.map)):(console.log("\u{1F310} Loading online map tiles..."),I.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{attribution:"OpenStreetMap contributors",maxZoom:19,minZoom:8}).addTo(this.map)),this.isOnline=!0,this.gpsEnabled?(console.log("GPS is enabled, adding user marker"),this.userMarker?(this.userMarker.setLatLng([i,l]),this.userMarker.addTo(this.map),console.log("Updated existing user marker")):(this.userMarker=I.marker([i,l],{icon:I.icon({iconUrl:"assets/Location.png",iconSize:[32,32],iconAnchor:[16,32]})}).addTo(this.map).bindPopup("You are here!").openPopup(),console.log("Created new user marker")),this.toastCtrl.create({message:"Using your real-time GPS location",duration:2e3,color:"success"}).then(B=>B.present())):console.log("GPS is disabled, not adding user marker"),this.isFilterMode||this.evacuationCenters.length>0?(console.log("Loading evacuation centers"),this.loadEvacuationCenters(i,l)):console.log("Skipping evacuation centers - showing only user location")}updateUserMarker(i,l){if(console.log(`Updating user marker to: [${i}, ${l}]`),isNaN(i)||isNaN(l)||Math.abs(i)>90||Math.abs(l)>180){console.error("Invalid coordinates for user marker update:",{lat:i,lng:l});return}if(this.userMarker){let B=this.userMarker.getLatLng();this.userMarker.setLatLng([i,l]),this.map.setView([i,l]),console.log("Updated existing user marker position");let g=this.calculateDistance(B.lat,B.lng,i,l);console.log(`User moved ${g.toFixed(2)} meters from previous position`),g>20&&(console.log(`Significant movement detected (${g.toFixed(2)}m), recalculating routes`),this.map.eachLayer(Q=>{Q instanceof I.GeoJSON&&(console.log("Removing existing route layer"),this.map.removeLayer(Q))}),this.evacuationCenters&&this.evacuationCenters.length>0&&(console.log("Recalculating routes to nearest evacuation centers"),this.routeToTwoNearestCenters()))}else this.userMarker=I.marker([i,l],{icon:I.icon({iconUrl:"assets/Location.png",iconSize:[32,32],iconAnchor:[16,32]})}).addTo(this.map).bindPopup("You are here!").openPopup(),console.log("Created new user marker"),this.evacuationCenters&&this.evacuationCenters.length>0&&(console.log("Calculating initial routes with real GPS data"),this.routeToTwoNearestCenters())}loadEvacuationCentersFiltered(i,l,B){return X(this,null,function*(){if(this.isLoadingCenters){console.log("Already loading evacuation centers, skipping duplicate request");return}this.isLoadingCenters=!0;try{if(console.log(`Loading evacuation centers for disaster type: ${B}`),console.log(`User coordinates: [${i}, ${l}]`),isNaN(i)||isNaN(l)||Math.abs(i)>90||Math.abs(l)>180){console.error("Invalid user coordinates for loading evacuation centers:",{userLat:i,userLng:l}),(yield this.toastCtrl.create({message:"Invalid location coordinates. Please check your GPS settings.",duration:3e3,color:"danger"})).present();return}this.currentDisasterType=B;let g=[];if(this.offlineStorage.isOfflineMode()||!this.offlineStorage.isOnline())console.log("\u{1F504} Loading evacuation centers from offline storage"),g=yield this.offlineStorage.getEvacuationCenters(),console.log("\u{1F4F1} OFFLINE DATA:",g),console.log("\u{1F4CA} TOTAL CACHED CENTERS:",g?.length||0),g.length===0&&(console.warn("\u26A0\uFE0F No cached evacuation centers found"),this.toastCtrl.create({message:"No offline evacuation data available. Please sync data when online.",duration:4e3,color:"warning"}).then(d=>d.present()));else{console.log("\u{1F310} Fetching evacuation centers from:",`${ee.apiUrl}/evacuation-centers`);try{g=yield Ze(this.http.get(`${ee.apiUrl}/evacuation-centers`)),console.log("\u{1F4E1} RAW API RESPONSE:",g),console.log("\u{1F4CA} TOTAL CENTERS RECEIVED:",g?.length||0)}catch(d){console.error("\u274C Failed to fetch online data, trying offline cache:",d),g=yield this.offlineStorage.getEvacuationCenters(),console.log("\u{1F4F1} FALLBACK TO OFFLINE DATA:",g),g.length>0&&this.toastCtrl.create({message:"Using cached evacuation data due to network error.",duration:3e3,color:"warning"}).then(p=>p.present())}}if(g&&g.length>0){let d=[...new Set(g.map(p=>p.disaster_type))];console.log("\u{1F3F7}\uFE0F UNIQUE DISASTER TYPES IN DATABASE:",d),d.forEach(p=>{let T=g.filter(D=>D.disaster_type===p).length;console.log(`   \u{1F4C8} ${p}: ${T} centers`)}),console.log("\u{1F50D} SAMPLE CENTERS:"),g.slice(0,5).forEach((p,T)=>{console.log(`   ${T+1}. "${p.name}" - Type: "${p.disaster_type}" - Status: "${p.status}"`)})}this.map.eachLayer(d=>{d instanceof I.Marker&&d!==this.userMarker&&this.map.removeLayer(d)}),this.map.eachLayer(d=>{d instanceof I.GeoJSON&&this.map.removeLayer(d)});let Q=g||[];if(B!=="all"){console.log(`\u{1F50D} FILTERING centers for disaster type: "${B}"`),console.log(`\u{1F4CA} Total centers before filtering: ${Q.length}`),console.log("\u{1F4CB} All centers disaster types:",g.map(p=>`${p.name}: "${p.disaster_type}"`)),Q=Q.filter(p=>{if(!p.disaster_type)return console.log(`\u274C Center "${p.name}" has no disaster_type, excluding`),!1;let T=p.disaster_type.trim(),D=B.trim(),W=T===D;return console.log(`\u{1F3E2} Center "${p.name}"`),console.log(`   \u{1F4CD} Center Type: "${T}" (length: ${T.length})`),console.log(`   \u{1F3AF} Looking for: "${D}" (length: ${D.length})`),console.log(`   \u2705 Match: ${W}`),W}),console.log(`\u{1F3AF} FILTERED RESULT: ${Q.length} centers for disaster type: "${B}"`),console.log("\u2705 INCLUDED CENTERS:"),Q.forEach((p,T)=>{console.log(`   ${T+1}. ${p.name} (${p.disaster_type})`)});let d=g.filter(p=>p.disaster_type&&p.disaster_type.trim()!==B.trim());console.log("\u274C EXCLUDED CENTERS:"),d.forEach((p,T)=>{console.log(`   ${T+1}. ${p.name} (${p.disaster_type})`)}),Q.length===0&&(console.error("\u{1F6A8} NO CENTERS FOUND FOR DISASTER TYPE!"),console.error("\u{1F50D} Debug Info:"),console.error(`   Target disaster type: "${B}"`),console.error("   Available disaster types:",[...new Set(g.map(p=>p.disaster_type))]),console.error(`   Total centers in database: ${g.length}`))}console.log("\u{1F9F9} AGGRESSIVE CLEARING: Removing ALL existing markers");let h=[];if(this.map.eachLayer(d=>{d instanceof I.Marker&&d!==this.userMarker&&(console.log("\u{1F5D1}\uFE0F Marking marker for removal:",d),h.push(d)),d instanceof I.GeoJSON&&(console.log("\u{1F5D1}\uFE0F Marking route for removal:",d),h.push(d))}),h.forEach(d=>{console.log("\u{1F5D1}\uFE0F Removing layer from map"),this.map.removeLayer(d)}),this.evacuationCenters=[],console.log(`\u{1F9F9} CLEARED: Removed ${h.length} layers from map`),this.evacuationCenters=Q,this.evacuationCenters.length===0){console.log(`\u{1F6A8} NO EVACUATION CENTERS FOUND for disaster type: "${B}"`),this.alertCtrl.create({header:"No Evacuation Centers Found",message:`There are no evacuation centers stored for ${B==="all"?"any disaster type":B}. Please contact your administrator to add evacuation centers.`,buttons:["OK"]}).then(d=>d.present()),this.userMarker&&this.userMarker.bindPopup("You are here!").openPopup(),this.map.setView([i,l],15);return}console.log(`\u{1F3AF} ADDING ${this.evacuationCenters.length} FILTERED MARKERS to map`),console.log(`\u{1F4CD} Disaster type filter: "${B}"`),console.log("\u{1F3E2} Centers to display:",this.evacuationCenters.map(d=>`${d.name} (${d.disaster_type})`)),this.evacuationCenters.forEach((d,p)=>{let T=Number(d.latitude),D=Number(d.longitude);if(console.log(`\u{1F3E2} Processing center ${p+1}/${this.evacuationCenters.length}: ${d.name}`),console.log(`   \u{1F4CD} Coordinates: [${T}, ${D}]`),console.log(`   \u{1F3F7}\uFE0F Disaster Type: "${d.disaster_type}"`),console.log(`   \u{1F3AF} Filter Type: "${B}"`),!isNaN(T)&&!isNaN(D)){let W=this.getDisasterIcon(d.disaster_type||"");console.log(`   \u{1F3A8} Icon URL: ${W}`);let z=I.marker([T,D],{icon:I.icon({iconUrl:W,iconSize:[40,40],iconAnchor:[20,40],popupAnchor:[0,-40]})}),xA=`
            <div class="evacuation-popup">
              <h3>${d.name||"Evacuation Center"}</h3>
              <p><strong>Type:</strong> ${d.disaster_type||"General"}</p>
              <p><strong>Distance:</strong> ${(this.calculateDistance(i,l,T,D)/1e3).toFixed(2)} km</p>
              <p><button class="popup-button">View Details</button></p>
            </div>
          `;z.bindPopup(xA),z.on("click",()=>{setTimeout(()=>{z.closePopup(),this.showEvacuationCenterDetails(d,i,l)},300)}),z.addTo(this.map),console.log(`   \u2705 MARKER ADDED to map for: ${d.name} (${d.disaster_type})`)}else console.error(`   \u274C Invalid coordinates for center: ${d.name}`)}),console.log(`\u{1F389} COMPLETED: Added ${this.evacuationCenters.length} markers for disaster type "${B}"`);let F=0;if(this.map.eachLayer(d=>{d instanceof I.Marker&&d!==this.userMarker&&F++}),console.log(`\u{1F50D} VERIFICATION: ${F} evacuation center markers currently on map`),this.gpsEnabled&&this.userMarker&&this.evacuationCenters.length>0){console.log("GPS enabled and user marker exists, finding nearest centers");let d=this.findTwoNearestCenters(i,l,this.evacuationCenters);if(d.length>0){this.addPulsingAnimationToNearest(d[0]);for(let D of d){let W=Number(D.latitude),z=Number(D.longitude);if(console.log(`Calculating route to center: ${D.name}`),console.log(`Center coordinates: [${W}, ${z}], types: [${typeof W}, ${typeof z}]`),isNaN(W)||isNaN(z)){console.error("Invalid center coordinates:",{centerLat:W,centerLng:z,center:D});continue}yield this.getRealRoute(i,l,W,z,this.travelMode,D.disaster_type)}let p="You are here!.";d.forEach((D,W)=>{let z=this.calculateDistance(i,l,Number(D.latitude),Number(D.longitude));p+=`<br> \u2022 <strong>${W+1}: ${D.name} </strong> <br> Distance: ${(z/1e3).toFixed(2)} km`}),this.userMarker.bindPopup(p).openPopup();let T=I.latLngBounds([]);T.extend([i,l]),d.forEach(D=>{T.extend([Number(D.latitude),Number(D.longitude)])}),this.map.fitBounds(T,{padding:[50,50]})}else console.log("No nearest centers found"),this.map.setView([i,l],15)}else console.log("GPS disabled, no user marker, or no centers found, skipping route calculation"),this.map.setView([i,l],15)}catch(g){console.error("Error loading filtered evacuation centers:",g),console.log("Network error loading evacuation centers - offline mode available")}finally{this.isLoadingCenters=!1}})}loadEvacuationCenters(i,l){return X(this,null,function*(){if(this.isLoadingCenters){console.log("Already loading evacuation centers, skipping duplicate request");return}this.isLoadingCenters=!0;try{if(console.log(`Loading evacuation centers with user coordinates: [${i}, ${l}]`),isNaN(i)||isNaN(l)||Math.abs(i)>90||Math.abs(l)>180){console.error("Invalid user coordinates for loading evacuation centers:",{userLat:i,userLng:l}),(yield this.toastCtrl.create({message:"Invalid location coordinates. Please check your GPS settings.",duration:3e3,color:"danger"})).present();return}let B=[];if(this.offlineStorage.isOfflineMode()||!this.offlineStorage.isOnline())console.log("\u{1F504} Loading evacuation centers from offline storage"),B=yield this.offlineStorage.getEvacuationCenters(),console.log("\u{1F4F1} OFFLINE DATA:",B),B.length===0&&(console.warn("\u26A0\uFE0F No cached evacuation centers found"),this.toastCtrl.create({message:"No offline evacuation data available. Please sync data when online.",duration:4e3,color:"warning"}).then(g=>g.present()));else{console.log("\u{1F310} Fetching evacuation centers from:",`${ee.apiUrl}/evacuation-centers`);try{B=yield Ze(this.http.get(`${ee.apiUrl}/evacuation-centers`)),console.log("\u{1F4E1} Received centers from API:",B)}catch(g){console.error("\u274C Failed to fetch online data, trying offline cache:",g),B=yield this.offlineStorage.getEvacuationCenters(),console.log("\u{1F4F1} FALLBACK TO OFFLINE DATA:",B),B.length>0&&this.toastCtrl.create({message:"Using cached evacuation data due to network error.",duration:3e3,color:"warning"}).then(Q=>Q.present())}}if(this.evacuationCenters=B||[],this.map.eachLayer(g=>{g instanceof I.Marker&&g!==this.userMarker&&this.map.removeLayer(g)}),this.evacuationCenters.forEach(g=>{let Q=Number(g.latitude),h=Number(g.longitude);if(console.log(`Processing center: ${g.name}, coordinates: [${Q}, ${h}]`),!isNaN(Q)&&!isNaN(h)){let F=this.getDisasterIcon(g.disaster_type||""),d=I.marker([Q,h],{icon:I.icon({iconUrl:F,iconSize:[40,40],iconAnchor:[20,40],popupAnchor:[0,-40]})}),p=`
            <div class="evacuation-popup">
              <h3>${g.name||"Evacuation Center"}</h3>
              <p><strong>Distance:</strong> ${(this.calculateDistance(i,l,Q,h)/1e3).toFixed(2)} km</p>
              <p><button class="popup-button">View Details</button></p>
            </div>
          `;d.bindPopup(p),d.on("click",()=>{setTimeout(()=>{d.closePopup(),this.showEvacuationCenterDetails(g,i,l)},300)}),d.addTo(this.map),console.log(`Added marker for center: ${g.name}`)}else console.error(`Invalid coordinates for center: ${g.name}`)}),this.gpsEnabled&&this.userMarker){console.log("GPS enabled and user marker exists, finding nearest centers");let g=this.findTwoNearestCenters(i,l,this.evacuationCenters);if(g.length>0){this.addPulsingAnimationToNearest(g[0]),this.map.eachLayer(h=>{h instanceof I.GeoJSON&&this.map.removeLayer(h)});for(let h of g){let F=Number(h.latitude),d=Number(h.longitude);if(console.log(`Calculating route to center: ${h.name}`),console.log(`Center coordinates: [${F}, ${d}], types: [${typeof F}, ${typeof d}]`),isNaN(F)||isNaN(d)){console.error("Invalid center coordinates:",{centerLat:F,centerLng:d,center:h});continue}yield this.getRealRoute(i,l,F,d,this.travelMode,h.disaster_type)}let Q="You are here!.";g.forEach((h,F)=>{let d=this.calculateDistance(i,l,Number(h.latitude),Number(h.longitude));Q+=`<br> \u2022${F+1}: ${h.name} <br> Distance: ${(d/1e3).toFixed(2)} km`}),this.userMarker.bindPopup(Q).openPopup()}else console.log("No nearest centers found"),this.map.setView([i,l],15)}else console.log("GPS disabled or no user marker, skipping route calculation"),this.map.setView([i,l],15)}catch(B){console.error("Failed to load evacuation centers",B),console.log("Network error loading evacuation centers - offline mode available")}finally{this.isLoadingCenters=!1}})}getRealRoute(F,d,p,T){return X(this,arguments,function*(i,l,B,g,Q=this.travelMode,h){if(console.log("Clearing all existing routes before calculating new route"),this.map.eachLayer(D=>{D instanceof I.GeoJSON&&(console.log("Removing existing route layer"),this.map.removeLayer(D))}),console.log("Requesting Mapbox route with coordinates:",{startLat:i,startLng:l,endLat:B,endLng:g,travelMode:Q}),[i,l,B,g].some(D=>typeof D!="number"||isNaN(D))){(yield this.toastCtrl.create({message:"Invalid route coordinates. Cannot request directions.",duration:3e3,color:"danger"})).present();return}if(Math.abs(i)>90||Math.abs(B)>90||Math.abs(l)>180||Math.abs(g)>180){(yield this.toastCtrl.create({message:"Route coordinates out of range. Cannot request directions.",duration:3e3,color:"danger"})).present();return}try{console.log("Sending route request to Mapbox");let D=this.mapboxRouting.convertTravelModeToProfile(Q),W=yield this.mapboxRouting.getDirections(l,i,g,B,D,{geometries:"geojson",overview:"full",steps:!0});if(!W.routes||W.routes.length===0)throw new Error("No routes found");let z=W.routes[0],xA=this.mapboxRouting.convertToGeoJSON(z),fA="#3388ff";if(h)switch(h){case"Earthquake":fA="#ffa500";break;case"Flood":fA="#0000ff";break;case"Typhoon":fA="#008000";break;default:console.warn(`Unknown disaster type for route color: ${h}`);break}console.log(`Route calculation - Disaster type: "${h}", Normalized type: "${h?h.toLowerCase():"none"}", Selected color: ${fA}`),this.currentDisasterType&&this.currentDisasterType!=="all"&&(this.currentDisasterType==="Earthquake"?fA="#ffa500":this.currentDisasterType==="Typhoon"?fA="#008000":this.currentDisasterType==="Flood"&&(fA="#0000ff"),console.log(`Filter mode active: ${this.currentDisasterType}, forcing route color to: ${fA}`)),console.log(`Using route color: ${fA} for disaster type: ${h||"unknown"}`);let Zt=I.geoJSON(xA,{style:{color:fA,weight:5,opacity:.8}}).addTo(this.map);this.routeTime=z.duration,this.routeDistance=z.distance;let At=this.mapboxRouting.getRouteSummary(z);console.log(`Mapbox route summary: ${At.durationText}, ${At.distanceText}`),this.map.fitBounds(Zt.getBounds(),{padding:[50,50]})}catch(D){console.error("Failed to fetch route from Mapbox",D);let W="Failed to fetch route. Please check your internet connection or try again later.";D.message?D.message.includes("Invalid Mapbox access token")?W="Invalid Mapbox access token. Please check your token configuration.":D.message.includes("Rate limit exceeded")?W="Too many requests to Mapbox. Please wait a moment and try again.":D.message.includes("Network error")?W="Network error. Please check your internet connection.":D.message.includes("No routes found")?W="No route could be calculated between these points.":W=`Mapbox routing error: ${D.message}`:D.status===401?W="Invalid Mapbox access token. Please check your token.":D.status===422?W="Invalid coordinates or routing parameters.":D.status===429?W="Rate limit exceeded. Please try again later.":D.status===0&&(W="Network error. Please check your internet connection.");let z=Q==="foot-walking"?"walking":Q==="cycling-regular"?"cycling":Q==="driving-car"?"driving":Q;this.hasRecentErrorToast()||((yield this.toastCtrl.create({message:`Failed to fetch ${z} route: ${W}`,duration:5e3,color:"danger"})).present(),this.setLastErrorToast())}})}findNearestCenter(i,l,B){if(!B.length)return null;let g=B[0],Q=this.calculateDistance(i,l,Number(g.latitude),Number(g.longitude));for(let h of B){let F=this.calculateDistance(i,l,Number(h.latitude),Number(h.longitude));F<Q&&(Q=F,g=h)}return g}calculateDistance(i,l,B,g){let h=i*Math.PI/180,F=B*Math.PI/180,d=(B-i)*Math.PI/180,p=(g-l)*Math.PI/180,T=Math.sin(d/2)*Math.sin(d/2)+Math.cos(h)*Math.cos(F)*Math.sin(p/2)*Math.sin(p/2);return 6371e3*(2*Math.atan2(Math.sqrt(T),Math.sqrt(1-T)))}downloadMap(){return X(this,null,function*(){try{yield this.loadingService.showLoading("Capturing map...");let i=document.getElementById("map");if(!i)throw new Error("Map element not found");console.log("Capturing map as image...");let l=yield(0,Li.default)(i,{useCORS:!0,allowTaint:!0,scrollX:0,scrollY:0,windowWidth:document.documentElement.offsetWidth,windowHeight:document.documentElement.offsetHeight,scale:1});yield this.loadingService.dismissLoading();let B=l.toDataURL("image/png"),h=`evacuation-map-${new Date().toISOString().replace(/[:.]/g,"-").substring(0,19)}.png`;yield(yield this.alertCtrl.create({header:"Map Captured",message:"Your map has been captured. What would you like to do with it?",buttons:[{text:"Download",handler:()=>{this.downloadImage(B,h)}},{text:"Share",handler:()=>{this.shareImage(B,h)}},{text:"Cancel",role:"cancel"}]})).present()}catch(i){console.error("Error capturing map:",i),yield this.loadingService.dismissLoading(),(yield this.toastCtrl.create({message:"Failed to capture map. Please try again.",duration:3e3,color:"danger"})).present()}})}downloadImage(i,l){let B=document.createElement("a");B.href=i,B.download=l,document.body.appendChild(B),B.click(),document.body.removeChild(B),this.toastCtrl.create({message:"Map downloaded successfully",duration:2e3,color:"success"}).then(g=>g.present())}shareImage(i,l){return X(this,null,function*(){try{if(navigator.share){let B=yield(yield fetch(i)).blob(),g=new File([B],l,{type:"image/png"});yield navigator.share({title:"Evacuation Map",text:"Here is my evacuation map with routes to the nearest evacuation centers",files:[g]}),console.log("Map shared successfully")}else console.log("Web Share API not supported"),(yield this.toastCtrl.create({message:"Sharing not supported on this device. The map has been downloaded instead.",duration:3e3,color:"warning"})).present(),this.downloadImage(i,l)}catch(B){console.error("Error sharing map:",B),(yield this.toastCtrl.create({message:"Failed to share map. The map has been downloaded instead.",duration:3e3,color:"warning"})).present(),this.downloadImage(i,l)}})}showEvacuationCenterDetails(i,l,B){return X(this,null,function*(){console.log("Showing evacuation center details for:",i.name);let g=yield this.modalCtrl.create({component:Ii,componentProps:{center:i,userLat:l,userLng:B},cssClass:"evacuation-details-modal",breakpoints:[0,.5,.75,1],initialBreakpoint:.75});yield g.present();let{data:Q}=yield g.onDidDismiss();if(Q&&Q.selectedMode&&(console.log("Selected travel mode:",Q.selectedMode),this.travelMode=Q.selectedMode,console.log("Clearing all existing routes before calculating new route"),this.map.eachLayer(h=>{h instanceof I.GeoJSON&&(console.log("Removing existing route layer"),this.map.removeLayer(h))}),this.userMarker)){let h=this.userMarker.getLatLng(),F=Number(i.latitude),d=Number(i.longitude);if(console.log("Recalculating route with new travel mode:",{userLat:h.lat,userLng:h.lng,centerLat:F,centerLng:d,travelMode:this.travelMode}),isNaN(F)||isNaN(d)){console.error("Invalid center coordinates:",{centerLat:F,centerLng:d}),(yield this.toastCtrl.create({message:"Invalid evacuation center coordinates. Cannot calculate route.",duration:3e3,color:"danger"})).present();return}this.toastCtrl.create({message:`Showing ${this.getTravelModeName().toLowerCase()} route to ${i.name}`,duration:2e3,color:"primary"}).then(p=>p.present()),yield this.getRealRoute(h.lat,h.lng,F,d,this.travelMode,i.disaster_type)}})}static{this.\u0275fac=function(l){return new(l||m)}}static{this.\u0275cmp=me({type:m,selectors:[["app-map"]],decls:47,vars:15,consts:[["class","offline-status-banner",4,"ngIf"],["id","map"],["class","location-request-container",4,"ngIf"],["class","map-default-message",4,"ngIf"],["class","route-summary-card",3,"click",4,"ngIf"],[3,"directions","travelMode","totalDistance","totalDuration","close",4,"ngIf"],["vertical","top","horizontal","end","slot","fixed"],["size","small",3,"click","color"],[3,"name"],["vertical","top","horizontal","end","slot","fixed",2,"top","60px"],["name","cloud-offline"],["vertical","top","horizontal","end","slot","fixed",2,"top","120px"],["size","small","color","danger"],["name","bug"],["side","start"],["size","small","color","warning",3,"click"],["name","globe"],["size","small","color","primary",3,"click"],["name","earth"],["size","small","color","danger",3,"click"],["name","thunderstorm"],["size","small","color","success",3,"click"],["name","water"],["vertical","top","horizontal","start","slot","fixed"],["name","download-outline"],[1,"fab-label"],[1,"gps-status",3,"click"],["class","disaster-type-indicator",4,"ngIf"],["vertical","bottom","horizontal","end","slot","fixed",4,"ngIf"],["vertical","bottom","horizontal","start","slot","fixed",4,"ngIf"],["vertical","bottom","horizontal","center","slot","fixed"],["color","secondary"],["name","cloud-offline-outline"],["side","top"],["color","warning",3,"click"],["color","success",3,"click"],["name","share-outline"],[1,"offline-status-banner"],[1,"location-request-container"],["expand","block","color","primary",3,"click"],["name","locate","slot","start"],[1,"location-help-text"],[1,"map-default-message"],["name","information-circle-outline"],[1,"route-summary-card",3,"click"],[3,"name","color"],[1,"summary-text"],[1,"travel-mode"],["name","chevron-up",1,"expand-icon"],[3,"close","directions","travelMode","totalDistance","totalDuration"],[1,"disaster-type-indicator"],["vertical","bottom","horizontal","end","slot","fixed"],["color","primary",3,"click"],["name","navigate-outline"],["vertical","bottom","horizontal","start","slot","fixed"],["color","tertiary",3,"click"],["name","list-outline"]],template:function(l,B){l&1&&(v(0,"ion-content"),wA(1,ug,4,0,"div",0),P(2,"div",1),wA(3,fg,6,0,"div",2)(4,wg,6,0,"div",3)(5,dg,9,5,"div",4)(6,hg,1,4,"app-directions-panel",5),v(7,"ion-fab",6)(8,"ion-fab-button",7),nA("click",function(){return B.toggleGps({detail:{checked:!B.gpsEnabled}})}),P(9,"ion-icon",8),M()(),v(10,"ion-fab",9)(11,"ion-fab-button",7),nA("click",function(){return B.toggleOfflineMode()}),P(12,"ion-icon",10),M()(),v(13,"ion-fab",11)(14,"ion-fab-button",12),P(15,"ion-icon",13),M(),v(16,"ion-fab-list",14)(17,"ion-fab-button",15),nA("click",function(){return B.debugLoadOfflineData()}),P(18,"ion-icon",16),M(),v(19,"ion-fab-button",17),nA("click",function(){return B.loadOfflineEvacuationCenters("earthquake")}),P(20,"ion-icon",18),M(),v(21,"ion-fab-button",19),nA("click",function(){return B.loadOfflineEvacuationCenters("typhoon")}),P(22,"ion-icon",20),M(),v(23,"ion-fab-button",21),nA("click",function(){return B.loadOfflineEvacuationCenters("flood")}),P(24,"ion-icon",22),M()()(),v(25,"ion-fab",23)(26,"ion-fab-button",21),nA("click",function(){return B.downloadMap()}),P(27,"ion-icon",24),M(),v(28,"ion-label",25),V(29,"Save Map"),M()(),v(30,"div",26),nA("click",function(){return B.showLocationHelp()}),P(31,"ion-icon",8),v(32,"span"),V(33),M()(),wA(34,Cg,4,2,"div",27)(35,Qg,5,0,"ion-fab",28)(36,pg,5,0,"ion-fab",29),v(37,"ion-fab",30)(38,"ion-fab-button",31),P(39,"ion-icon",32),M(),v(40,"ion-fab-list",33)(41,"ion-fab-button",34),nA("click",function(){return B.exportOfflineData()}),P(42,"ion-icon",24),M(),v(43,"ion-fab-button",35),nA("click",function(){return B.shareOfflineData()}),P(44,"ion-icon",36),M()(),v(45,"ion-label",25),V(46,"Offline Data"),M()()()),l&2&&(K(),k("ngIf",B.offlineStorage.isOfflineMode()||!B.offlineStorage.isOnline()),K(2),k("ngIf",B.showLocationRequestButton),K(),k("ngIf",!B.isFilterMode&&B.evacuationCenters.length===0),K(),k("ngIf",B.routeTime&&B.routeDistance),K(),k("ngIf",B.showDirectionsPanel&&B.currentDirections.length>0),K(2),k("color",B.gpsEnabled?"primary":"medium"),K(),k("name",B.gpsEnabled?"locate":"locate-outline"),K(2),k("color",B.offlineStorage.isOfflineMode()?"warning":"medium"),K(19),Ai("active",B.gpsEnabled),K(),k("name",B.gpsEnabled?"location":"location-outline"),K(2),yA("GPS ",B.gpsEnabled?"Active":"Inactive",""),K(),k("ngIf",B.isFilterMode&&B.currentDisasterType!=="all"),K(),k("ngIf",B.isFilterMode||B.evacuationCenters.length>0),K(),k("ngIf",B.currentDirections.length>0&&!B.showDirectionsPanel))},dependencies:[be,He,Jt,li,Bi,gi,Ie,ye,Ee,ve,ri,yi],styles:['.offline-status-banner[_ngcontent-%COMP%]{position:absolute;top:10px;left:50%;transform:translate(-50%);background:linear-gradient(135deg,#ff6b35,#f7931e);color:#fff;border-radius:20px;padding:8px 16px;display:flex;align-items:center;gap:8px;z-index:1001;box-shadow:0 2px 8px #0003;font-size:14px;font-weight:500;animation:_ngcontent-%COMP%_slideDown .3s ease-out}.offline-status-banner[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}@keyframes _ngcontent-%COMP%_slideDown{0%{opacity:0;transform:translate(-50%) translateY(-20px)}to{opacity:1;transform:translate(-50%) translateY(0)}}#map[_ngcontent-%COMP%]{width:100%;height:100%}.mode-segment[_ngcontent-%COMP%]{position:absolute;left:50%;transform:translate(-50%);top:10px;z-index:1000;background:#ffffffe6;border-radius:20px;padding:4px;width:90%;max-width:400px;box-shadow:0 2px 8px #0000001a}.mode-segment[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]{--background: transparent;--background-checked: var(--ion-color-light);--indicator-color: transparent;--border-radius: 16px;min-height:40px}.mode-segment[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]   .segment-icon[_ngcontent-%COMP%]{width:24px;height:24px;display:block;margin:0 auto 4px}.mode-segment[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]   .segment-label[_ngcontent-%COMP%]{font-size:12px;font-weight:500}.route-summary-card[_ngcontent-%COMP%]{position:absolute;left:50%;transform:translate(-50%);top:70px;background:#fffffff2;border-radius:16px;box-shadow:0 2px 8px #0000001a;padding:12px 16px;display:flex;align-items:center;gap:12px;z-index:1000;cursor:pointer;transition:all .2s ease}.route-summary-card[_ngcontent-%COMP%]:hover{background:#fff;box-shadow:0 4px 12px #00000026;transform:translate(-50%) translateY(-2px)}.route-summary-card[_ngcontent-%COMP%]:active{transform:translate(-50%) translateY(0)}.route-summary-card[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px}.route-summary-card[_ngcontent-%COMP%]   .summary-text[_ngcontent-%COMP%]{line-height:1.3}.route-summary-card[_ngcontent-%COMP%]   .summary-text[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{font-size:16px}.route-summary-card[_ngcontent-%COMP%]   .summary-text[_ngcontent-%COMP%]   .travel-mode[_ngcontent-%COMP%]{font-size:12px;opacity:.8;margin-top:2px}.route-summary-card[_ngcontent-%COMP%]   .expand-icon[_ngcontent-%COMP%]{font-size:18px;margin-left:8px;color:var(--ion-color-medium)}.fab-label[_ngcontent-%COMP%]{position:absolute;right:80px;bottom:30px;background:#fffffff2;padding:8px 16px;border-radius:20px;font-size:14px;color:var(--ion-color-primary);z-index:1000;box-shadow:0 2px 8px #0000001a;font-weight:500}ion-fab-button[activated][_ngcontent-%COMP%]{--background: var(--ion-color-primary);--color: white}.gps-status[_ngcontent-%COMP%]{position:absolute;top:10px;left:70px;background:#ffffffe6;border-radius:20px;padding:8px 12px;display:flex;align-items:center;gap:6px;z-index:1000;box-shadow:0 2px 8px #0000001a;font-size:14px;color:var(--ion-color-medium);cursor:pointer;transition:all .2s ease}.gps-status[_ngcontent-%COMP%]:hover{background:#fff;box-shadow:0 4px 12px #00000026;transform:translateY(-2px)}.gps-status[_ngcontent-%COMP%]:active{transform:translateY(0)}.gps-status.active[_ngcontent-%COMP%]{color:var(--ion-color-primary);background:rgba(var(--ion-color-primary-rgb),.1)}.gps-status.active[_ngcontent-%COMP%]:hover{background:rgba(var(--ion-color-primary-rgb),.2)}.gps-status.active[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 1.5s infinite}.gps-status[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.gps-status[_ngcontent-%COMP%]:after{content:"?";display:inline-block;width:16px;height:16px;line-height:16px;text-align:center;background:var(--ion-color-medium);color:#fff;border-radius:50%;font-size:12px;margin-left:6px;opacity:.7}.disaster-type-indicator[_ngcontent-%COMP%]{position:absolute;top:10px;right:80px;background:#ffffffe6;border-radius:20px;padding:8px 12px;display:flex;align-items:center;gap:6px;z-index:1000;box-shadow:0 2px 8px #0000001a;font-size:14px;font-weight:500;color:var(--ion-color-dark)}.disaster-type-indicator[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px;color:var(--ion-color-primary)}.location-request-container[_ngcontent-%COMP%]{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);background:#fffffff2;border-radius:16px;box-shadow:0 4px 16px #00000026;padding:20px;text-align:center;max-width:300px;width:90%;z-index:1001;animation:_ngcontent-%COMP%_fadeIn .5s ease-out}.location-request-container[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{margin:10px 0;--border-radius: 10px;--box-shadow: 0 4px 8px rgba(var(--ion-color-primary-rgb), .3);font-weight:600;height:48px}.location-request-container[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]:active{--box-shadow: 0 2px 4px rgba(var(--ion-color-primary-rgb), .2);transform:translateY(2px)}.location-request-container[_ngcontent-%COMP%]   .location-help-text[_ngcontent-%COMP%]{margin:10px 0 0;font-size:14px;color:var(--ion-color-medium);line-height:1.4}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0;transform:translate(-50%,-40%)}to{opacity:1;transform:translate(-50%,-50%)}}.map-default-message[_ngcontent-%COMP%]{position:absolute;bottom:30px;left:50%;transform:translate(-50%);background:#fffffff2;border-radius:16px;box-shadow:0 2px 8px #0000001a;padding:12px 16px;text-align:center;max-width:300px;z-index:1000}.map-default-message[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;color:var(--ion-color-primary);margin-bottom:8px}.map-default-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 5px;font-weight:500;font-size:16px;color:var(--ion-color-dark)}.map-default-message[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{color:var(--ion-color-medium);font-size:13px;display:block;line-height:1.4}@keyframes _ngcontent-%COMP%_pulse{0%{opacity:.6}50%{opacity:1}to{opacity:.6}}@keyframes _ngcontent-%COMP%_pulsate{0%{transform:scale(.8);opacity:.8}50%{transform:scale(1.5);opacity:.4}to{transform:scale(.8);opacity:.8}}.marker-pulse-container[_ngcontent-%COMP%]{position:relative}.marker-pulse[_ngcontent-%COMP%]{position:absolute;top:50%;left:50%;width:50px;height:50px;margin-top:-25px;margin-left:-25px;border-radius:50%;z-index:100;pointer-events:none;animation:_ngcontent-%COMP%_pulsate 1.5s ease-out infinite;box-shadow:0 0 10px #00000080}[_nghost-%COMP%]     .popup-button{background-color:var(--ion-color-primary);color:#fff;border:none;border-radius:4px;padding:6px 12px;font-size:14px;cursor:pointer;margin-top:8px;transition:background-color .2s}[_nghost-%COMP%]     .popup-button:hover{background-color:var(--ion-color-primary-shade)}[_nghost-%COMP%]     .evacuation-popup h3{margin:0 0 8px;font-size:16px;font-weight:600}[_nghost-%COMP%]     .evacuation-popup p{margin:4px 0;font-size:14px}.evacuation-details-modal[_ngcontent-%COMP%]{--border-radius: 16px 16px 0 0;--backdrop-opacity: .4}']})}}return m})();export{Zg as MapPage};
