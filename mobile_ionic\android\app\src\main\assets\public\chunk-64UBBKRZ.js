import{a as U}from"./chunk-CG3MNCHG.js";import"./chunk-FULEFYAM.js";import"./chunk-AGHLTJ5J.js";import{$a as D,A as v,Ab as H,C,D as c,Db as G,F as e,G as i,H as s,I as w,J as p,K as u,M as n,Ma as _,N as y,Na as k,O as h,Oa as I,Pa as N,Qa as O,Ra as F,Sa as M,Ta as P,V as x,Wa as A,X as E,ab as L,cb as B,ea as T,fb as R,gb as K,oa as S,q as f,r as b,ub as $,vb as j,y as l,z as m,zb as q}from"./chunk-QCXYQNJC.js";import"./chunk-6WVAEWPV.js";import"./chunk-HYNAH5QB.js";import"./chunk-5AIHQZWU.js";import"./chunk-4PQ5B4D2.js";import"./chunk-HC6MZPB3.js";import"./chunk-SV2ZKNWA.js";import"./chunk-EPGIQT2W.js";import"./chunk-RS5W3JWO.js";import"./chunk-DY4KE6AI.js";import"./chunk-GIGBYVJT.js";import"./chunk-KGEDUKSE.js";import"./chunk-MCRJI3T3.js";import"./chunk-OBBPMR2I.js";import"./chunk-AMQPVFGX.js";import"./chunk-KKCAABTQ.js";import"./chunk-OFX7WKKZ.js";import"./chunk-F4H6ZFEG.js";import"./chunk-NMYJD6OP.js";import"./chunk-XXJXE6HG.js";import"./chunk-SV7S5NYR.js";import"./chunk-WTCPO44B.js";import"./chunk-QVY4QQUF.js";import"./chunk-2HRRFJKF.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-BAKMWPBW.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import{g}from"./chunk-2R6CW7ES.js";function Q(d,V){if(d&1){let t=w();e(0,"ion-item",14),s(1,"ion-icon",15),e(2,"ion-label")(3,"h2"),n(4),i(),e(5,"p"),n(6),i(),e(7,"p",16)(8,"ion-badge",17),n(9),i(),e(10,"ion-badge",17),n(11),i()()(),e(12,"ion-buttons",18)(13,"ion-button",19),p("click",function(){let a=f(t).$implicit,r=u();return b(r.testForegroundNotification(a))}),s(14,"ion-icon",20),i()()()}if(d&2){let t=V.$implicit,o=u();l(),c("name",o.getDisasterIcon(t.category))("color",o.getDisasterColor(t.category)),l(3),y(t.title),l(2),y(t.body),l(2),c("color",o.getDisasterColor(t.category)),l(),h(" ",t.category.toUpperCase()," "),l(),c("color",t.severity==="high"?"danger":"warning"),l(),h(" ",t.severity.toUpperCase()," "),l(2),c("color",o.getDisasterColor(t.category))}}var oe=(()=>{class d{constructor(t,o,a,r){this.fcmService=t,this.alertController=o,this.loadingController=a,this.router=r,this.testNotifications=[{title:"EARTHQUAKE ALERT",body:"Magnitude 7.2 earthquake detected. Evacuate to nearest safe area immediately.",category:"earthquake",severity:"high"},{title:"FLOOD WARNING",body:"Flash flood warning in your area. Move to higher ground immediately.",category:"flood",severity:"high"},{title:"TYPHOON ALERT",body:"Typhoon approaching. Seek shelter in a sturdy building.",category:"typhoon",severity:"medium"},{title:"FIRE EMERGENCY",body:"Fire reported in your vicinity. Evacuate the area immediately.",category:"fire",severity:"high"},{title:"GENERAL ALERT",body:"Emergency situation detected. Follow local authorities instructions.",category:"general",severity:"medium"}]}testForegroundNotification(t){return g(this,null,function*(){let o=yield this.loadingController.create({message:"Testing foreground notification...",duration:3e3});yield o.present();try{let a={title:t.title,body:t.body,category:t.category,severity:t.severity,wasTapped:!1,data:{category:t.category,severity:t.severity}};yield this.fcmService.simulateForegroundNotification(a),yield o.dismiss(),yield(yield this.alertController.create({header:"Test Complete",message:"Foreground notification test completed. Check if the emergency modal appeared.",buttons:["OK"]})).present()}catch(a){yield o.dismiss(),yield(yield this.alertController.create({header:"Test Failed",message:`Error testing notification: ${a}`,buttons:["OK"]})).present()}})}testBackgroundNotification(t){return g(this,null,function*(){let o=yield this.loadingController.create({message:"Testing background notification...",duration:3e3});yield o.present();try{let a={title:t.title,body:t.body,category:t.category,severity:t.severity,wasTapped:!0,data:{category:t.category,severity:t.severity}};yield this.fcmService.simulateBackgroundNotification(a),yield o.dismiss(),yield(yield this.alertController.create({header:"Test Complete",message:"Background notification test completed. Check if the emergency modal appeared.",buttons:["OK"]})).present()}catch(a){yield o.dismiss(),yield(yield this.alertController.create({header:"Test Failed",message:`Error testing notification: ${a}`,buttons:["OK"]})).present()}})}testAllNotifications(){return g(this,null,function*(){let t=yield this.loadingController.create({message:"Testing all notification types...",duration:15e3});yield t.present();try{for(let a=0;a<this.testNotifications.length;a++){let r=this.testNotifications[a];a>0&&(yield new Promise(z=>setTimeout(z,3e3)));let W={title:r.title,body:r.body,category:r.category,severity:r.severity,wasTapped:!1,data:{category:r.category,severity:r.severity}};yield this.fcmService.simulateForegroundNotification(W)}yield t.dismiss(),yield(yield this.alertController.create({header:"All Tests Complete",message:"All notification types have been tested. Check if emergency modals appeared for each.",buttons:["OK"]})).present()}catch(o){yield t.dismiss(),yield(yield this.alertController.create({header:"Test Failed",message:`Error during batch testing: ${o}`,buttons:["OK"]})).present()}})}getDisasterIcon(t){switch(t.toLowerCase()){case"earthquake":return"warning-outline";case"flood":return"water-outline";case"typhoon":return"cloudy-outline";case"fire":return"flame-outline";default:return"notifications-outline"}}getDisasterColor(t){switch(t.toLowerCase()){case"earthquake":return"warning";case"flood":return"primary";case"typhoon":return"success";case"fire":return"danger";default:return"medium"}}goBack(){this.router.navigate(["/tabs/profile"])}static{this.\u0275fac=function(o){return new(o||d)(m(U),m(q),m(H),m(T))}}static{this.\u0275cmp=v({type:d,selectors:[["app-notification-test"]],decls:94,vars:3,consts:[[3,"translucent"],["slot","start"],[3,"click"],["name","chevron-back-outline"],[1,"ion-padding",3,"fullscreen"],[1,"test-container"],["expand","block","color","danger",3,"click"],["name","flash-outline","slot","start"],["class","notification-item",4,"ngFor","ngForOf"],["name","phone-portrait-outline","slot","start","color","primary"],["name","moon-outline","slot","start","color","secondary"],["name","checkmark-circle-outline","slot","start","color","success"],["name","close-circle-outline","slot","start","color","danger"],["name","warning-outline","slot","start","color","warning"],[1,"notification-item"],["slot","start",3,"name","color"],[1,"category-badge"],[3,"color"],["slot","end"],["fill","clear",3,"click","color"],["name","play-outline"]],template:function(o,a){o&1&&(e(0,"ion-header",0)(1,"ion-toolbar")(2,"ion-buttons",1)(3,"ion-button",2),p("click",function(){return a.goBack()}),s(4,"ion-icon",3),i()(),e(5,"ion-title"),n(6,"Emergency Notification Test"),i()()(),e(7,"ion-content",4)(8,"div",5)(9,"ion-card")(10,"ion-card-header")(11,"ion-card-title"),n(12,"\u{1F6A8} Emergency Notification Testing"),i(),e(13,"ion-card-subtitle"),n(14,"Test emergency modals and notifications"),i()(),e(15,"ion-card-content")(16,"p"),n(17,"This page allows you to test emergency notifications to ensure they work properly when the app is active."),i(),e(18,"p")(19,"strong"),n(20,"What to expect:"),i()(),e(21,"ul")(22,"li"),n(23,"Emergency modal should appear immediately"),i(),e(24,"li"),n(25,"Device should vibrate"),i(),e(26,"li"),n(27,"Modal should have disaster-specific colors"),i(),e(28,"li"),n(29,"App should NOT crash or close"),i()()()(),e(30,"ion-card")(31,"ion-card-header")(32,"ion-card-title"),n(33,"Quick Test"),i()(),e(34,"ion-card-content")(35,"ion-button",6),p("click",function(){return a.testAllNotifications()}),s(36,"ion-icon",7),n(37," Test All Notification Types "),i()()(),e(38,"ion-card")(39,"ion-card-header")(40,"ion-card-title"),n(41,"Individual Tests"),i(),e(42,"ion-card-subtitle"),n(43,"Test specific disaster types"),i()(),e(44,"ion-card-content")(45,"ion-list"),C(46,Q,15,9,"ion-item",8),i()()(),e(47,"ion-card")(48,"ion-card-header")(49,"ion-card-title"),n(50,"Test Types"),i()(),e(51,"ion-card-content")(52,"ion-list")(53,"ion-item"),s(54,"ion-icon",9),e(55,"ion-label")(56,"h3"),n(57,"Foreground Test"),i(),e(58,"p"),n(59,"Tests notifications when app is active and visible"),i()()(),e(60,"ion-item"),s(61,"ion-icon",10),e(62,"ion-label")(63,"h3"),n(64,"Background Test"),i(),e(65,"p"),n(66,"Simulates notifications when app was in background"),i()()()()()(),e(67,"ion-card")(68,"ion-card-header")(69,"ion-card-title"),n(70,"Troubleshooting"),i()(),e(71,"ion-card-content")(72,"ion-list")(73,"ion-item"),s(74,"ion-icon",11),e(75,"ion-label")(76,"h3"),n(77,"\u2705 Working Correctly"),i(),e(78,"p"),n(79,"Modal appears, device vibrates, app stays open"),i()()(),e(80,"ion-item"),s(81,"ion-icon",12),e(82,"ion-label")(83,"h3"),n(84,"\u274C App Crashes"),i(),e(85,"p"),n(86,"Check console logs, modal creation might be failing"),i()()(),e(87,"ion-item"),s(88,"ion-icon",13),e(89,"ion-label")(90,"h3"),n(91,"\u26A0\uFE0F No Modal"),i(),e(92,"p"),n(93,"Check if fallback toast appears instead"),i()()()()()()()()),o&2&&(c("translucent",!0),l(7),c("fullscreen",!0),l(39),c("ngForOf",a.testNotifications))},dependencies:[G,_,k,I,N,O,F,M,P,A,D,L,B,R,K,$,j,E,x,S],styles:[".test-container[_ngcontent-%COMP%]{max-width:600px;margin:0 auto}.notification-item[_ngcontent-%COMP%]{margin-bottom:8px;border-radius:8px;--background: var(--ion-color-light)}.category-badge[_ngcontent-%COMP%]{margin-top:8px;display:flex;gap:8px}ion-badge[_ngcontent-%COMP%]{font-size:.7rem;padding:4px 8px}ion-card[_ngcontent-%COMP%]{margin-bottom:16px}ion-card-title[_ngcontent-%COMP%]{color:var(--ion-color-primary)}.test-button[_ngcontent-%COMP%]{margin:8px 0}ion-list[_ngcontent-%COMP%]{background:transparent}ion-item[_ngcontent-%COMP%]{--padding-start: 16px;--padding-end: 16px;margin-bottom:4px}h3[_ngcontent-%COMP%]{margin:0 0 4px;font-weight:600}p[_ngcontent-%COMP%]{margin:0;color:var(--ion-color-medium);font-size:.9rem}"]})}}return d})();export{oe as NotificationTestPage};
