import{a as N}from"./chunk-4PHGPBSW.js";import"./chunk-UFD7UJFV.js";import{a as j}from"./chunk-CG3MNCHG.js";import{a as _}from"./chunk-FULEFYAM.js";import"./chunk-AGHLTJ5J.js";import{$ as M,A as w,Da as R,Db as L,F as n,G as o,H as u,J as h,Ja as F,M as l,Na as x,Q as m,R as d,S as p,Wa as I,bb as O,cb as E,ea as C,fb as W,ha as k,ia as y,ja as P,ka as S,la as T,ma as b,oa as v,tb as z,y as c,z as g,zb as A}from"./chunk-QCXYQNJC.js";import"./chunk-6WVAEWPV.js";import"./chunk-HYNAH5QB.js";import"./chunk-5AIHQZWU.js";import"./chunk-4PQ5B4D2.js";import"./chunk-HC6MZPB3.js";import"./chunk-SV2ZKNWA.js";import"./chunk-EPGIQT2W.js";import"./chunk-RS5W3JWO.js";import"./chunk-DY4KE6AI.js";import"./chunk-GIGBYVJT.js";import"./chunk-KGEDUKSE.js";import"./chunk-MCRJI3T3.js";import"./chunk-OBBPMR2I.js";import"./chunk-AMQPVFGX.js";import"./chunk-KKCAABTQ.js";import"./chunk-OFX7WKKZ.js";import"./chunk-F4H6ZFEG.js";import"./chunk-NMYJD6OP.js";import"./chunk-XXJXE6HG.js";import"./chunk-SV7S5NYR.js";import"./chunk-WTCPO44B.js";import"./chunk-QVY4QQUF.js";import"./chunk-2HRRFJKF.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-BAKMWPBW.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import{g as a}from"./chunk-2R6CW7ES.js";var X=(()=>{class f{constructor(i,r,e,s,t,B){this.authService=i,this.router=r,this.http=e,this.platform=s,this.fcmService=t,this.alertController=B,this.user={full_name:"",email:"",password:"",confirmPassword:""},this.fcmToken="",this.fcmTokenReady=!1}ngOnInit(){return a(this,null,function*(){console.log("\u{1F525} Register page initializing..."),yield this.initializeFCM()})}initializeFCM(){return a(this,null,function*(){try{console.log("\u{1F525} Initializing FCM for registration..."),yield this.fcmService.initPush(),yield this.getFCMToken(),console.log("\u2705 FCM initialization complete, token ready:",!!this.fcmToken),this.fcmTokenReady=!0}catch(i){console.error("\u274C FCM initialization failed:",i),this.fcmTokenReady=!1}})}getFCMToken(){return a(this,null,function*(){try{if(!this.platform.is("cordova")&&!this.platform.is("capacitor")){console.log("Running in browser, using mock FCM token"),this.fcmToken="browser-mock-token-"+Math.random().toString(36).substring(2,15),console.log("Mock FCM Token:",this.fcmToken);return}console.log("Getting FCM token from service..."),this.fcmToken=yield this.fcmService.getToken(),console.log("\u2705 FCM Token obtained:",this.fcmToken.substring(0,20)+"...")}catch(i){console.error("\u274C Error getting FCM token from service:",i),this.fcmToken=""}})}onRegister(){return a(this,null,function*(){if(this.user.password!==this.user.confirmPassword){yield this.presentAlert("Registration Failed","Passwords do not match!");return}this.authService.register({full_name:this.user.full_name,email:this.user.email,password:this.user.password,password_confirmation:this.user.confirmPassword}).subscribe({next:i=>a(this,null,function*(){if(console.log("Registration successful:",i),this.fcmToken){console.log("Registering FCM token after registration:",this.fcmToken);let r={token:this.fcmToken,device_type:this.platform.is("ios")?"ios":"android",project_id:_.firebase.projectId};console.log("Token registration payload:",r),this.fcmService.registerTokenWithBackend(this.fcmToken)}else console.warn("No FCM token available to register after registration");yield this.presentAlert("Registration Successful","Your account has been created successfully. Please log in."),this.router.navigate(["/login"])}),error:i=>a(this,null,function*(){console.error("Registration error:",i),yield this.presentAlert("Registration Failed","Registration failed: "+(i.error?.message||"Unknown error"))})})})}registerTokenWithEndpoints(i){return a(this,null,function*(){let r=[`${_.apiUrl}/device-token`,"http://localhost:8000/api/device-token","https://7af9-43-226-6-217.ngrok-free.app/api/device-token"];for(let e of r)try{let s=yield this.http.post(e,i).toPromise();console.log(`FCM token registered with ${e}:`,s),localStorage.setItem("fcm_token",this.fcmToken);break}catch(s){console.error(`Error registering token with ${e}:`,s)}})}presentAlert(i,r){return a(this,null,function*(){yield(yield this.alertController.create({header:i,message:r,buttons:["OK"]})).present()})}goToLogin(){this.router.navigate(["/login"])}static{this.\u0275fac=function(r){return new(r||f)(g(N),g(C),g(M),g(R),g(j),g(A))}}static{this.\u0275cmp=w({type:f,selectors:[["app-register"]],decls:33,vars:4,consts:[[1,"ion-padding","register-bg"],[1,"register-wrapper"],["src","assets/ALERTO.png","alt","App Logo",1,"register-logo"],[1,"register-title"],[1,"register-form",3,"ngSubmit"],["position","floating"],["type","text","name","full_name","required","",3,"ngModelChange","ngModel"],["type","email","name","email","required","",3,"ngModelChange","ngModel"],["type","password","name","password","required","",3,"ngModelChange","ngModel"],["type","password","name","confirmPassword","required","",3,"ngModelChange","ngModel"],["expand","block","type","submit",1,"register-btn"],[1,"ion-text-center","ion-margin-top"],[3,"click"]],template:function(r,e){r&1&&(n(0,"ion-content",0)(1,"div",1),u(2,"img",2),n(3,"h1",3),l(4,"Sign Up Here!"),o(),n(5,"form",4),h("ngSubmit",function(){return e.onRegister()}),n(6,"ion-item")(7,"ion-label",5),l(8,"Full Name:"),o(),n(9,"ion-input",6),p("ngModelChange",function(t){return d(e.user.full_name,t)||(e.user.full_name=t),t}),o()(),n(10,"ion-item")(11,"ion-label",5),l(12,"Email:"),o(),n(13,"ion-input",7),p("ngModelChange",function(t){return d(e.user.email,t)||(e.user.email=t),t}),o()(),n(14,"ion-item")(15,"ion-label",5),l(16,"Password:"),o(),n(17,"ion-input",8),p("ngModelChange",function(t){return d(e.user.password,t)||(e.user.password=t),t}),o()(),n(18,"ion-item")(19,"ion-label",5),l(20,"Confirm Password:"),o(),n(21,"ion-input",9),p("ngModelChange",function(t){return d(e.user.confirmPassword,t)||(e.user.confirmPassword=t),t}),o()(),u(22,"br")(23,"br"),n(24,"ion-button",10),l(25,"Register"),o()(),n(26,"div",11)(27,"ion-text"),l(28,"Already have an account? "),o(),n(29,"a",12),h("click",function(){return e.goToLogin()}),n(30,"strong")(31,"u"),l(32,"Log In"),o()()()()()()),r&2&&(c(9),m("ngModel",e.user.full_name),c(4),m("ngModel",e.user.email),c(4),m("ngModel",e.user.password),c(4),m("ngModel",e.user.confirmPassword))},dependencies:[L,x,I,O,E,W,z,F,v,T,k,y,b,S,P],styles:[".register-container[_ngcontent-%COMP%]{height:80vh;display:flex;align-items:center;justify-content:center}.register-wrapper[_ngcontent-%COMP%]{width:100%;max-width:420px;padding:32px 28px;margin:0 auto;display:flex;flex-direction:column;align-items:center;text-align:center}.register-logo[_ngcontent-%COMP%]{width:280px;height:280px}.register-title[_ngcontent-%COMP%]{font-size:2rem;font-weight:700}.register-desc[_ngcontent-%COMP%]{color:#888;font-size:1.1rem}.register-form[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:18px;color:#888}.register-form[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:100%;--highlight-color-focused: xz#000000;--min-height: 44px;--padding-start: 0;--padding-end: 0;--inner-padding-end: 0;--inner-padding-start: 0}.forgot-link[_ngcontent-%COMP%]{text-align:right;font-size:.95rem}.forgot-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#1565c0;text-decoration:none}.register-link[_ngcontent-%COMP%]{font-size:1rem;color:#444}.register-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#1565c0;text-decoration:none;font-weight:600}.register-btn[_ngcontent-%COMP%]{--border-radius: 25px;font-size:1.1rem;height:48px;width:100%}"]})}}return f})();export{X as RegisterPage};
