-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:27:9-35:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:31:13-47
	android:authorities
		INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:29:13-64
	android:exported
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:30:13-37
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:28:13-62
manifest
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:2:1-66:12
INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:2:1-66:12
INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:2:1-66:12
INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:2:1-66:12
MERGED from [:capacitor-firebase-messaging] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-19:12
MERGED from [:capacitor-app] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:capacitor-geolocation] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\geolocation\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:capacitor-haptics] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:capacitor-keyboard] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\keyboard\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-26:12
MERGED from [:capacitor-network] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\network\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:capacitor-screen-orientation] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\screen-orientation\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:capacitor-status-bar] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\status-bar\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:capacitor-android] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\android\capacitor\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:capacitor-cordova-android-plugins] C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-14:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\16884767c054ac4cab0f70a5a4855d4d\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dda665aa4a1576cfb1759fb2bbcd5279\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75881b531e34911967ea794bd3408c30\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef234481c09f01fb9f0508a5da2b1126\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:17:1-66:12
MERGED from [io.ionic.libs:iongeolocation-android:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf5c25e299eb52ddcbcc73083ca78071\transformed\iongeolocation-android-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87382da045b5faebd5371143916fd9ef\transformed\play-services-location-21.3.0\AndroidManifest.xml:2:1-8:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3897ee7a3a7e64eb47ff9b7bb8256b24\transformed\play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d121929483b8667b8bd7f522bf5e661\transformed\firebase-installations-17.2.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\324fc306ed84dc357040da54cc5f1fbc\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f546ee03c2308c684eeebc8faf0e1407\transformed\firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b70f94d043134f6a6c82aff3176808d5\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3cd5cde5baf76ffd76ccca9ac0ba6533\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f872c9901e2c95f57da6f5d814a1f18d\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\509d7e86b27aaa293843dd95b79d84c3\transformed\play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fd50506c236bde2540ace785bc60713\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d222fc1537ba4d05d2594c911ab56ac7\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd457bbc543ecdbee5c91b5aa5e11e77\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17f9b79c3605804ded7cffe50acaf238\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f8069ce470fc0e16369d43e56e54e22\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.webkit:webkit:1.12.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3fbbd497413c0ee4069f169f4dc7a260\transformed\webkit-1.12.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\243233694e4ca3136f1ca4dacc434c39\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf2bdf699ee56f42fbfc2f03618abe36\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6c8f2b475cbb949dc656efeb2abd4fcd\transformed\activity-ktx-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eaa603a2c27a5645906131bea5891cf1\transformed\core-ktx-1.15.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\206ff51956c4746556b0b1a85c73686b\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\111c5c3c3f6ae179fe7713443d447c72\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23941852aafd4e5308edbcd04fec6a27\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bfc7da70d4fbc8073307b3326dd9704e\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d68bb0f6b9dc7d41a2b6dfc3b4acad60\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a75dca28172537968edb11f4713fc67\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cb5e40cf228b6c33a81f0245b3675c9\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f212eb2fcec7b76a8049b85cea08416b\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4b3e930a763ca268d3b38830b586bc2a\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f974b30b07e5bde53cb3618bce731f69\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\998c918bf96ae2f6a4f5c8c644413a6f\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19f28f4dfdbce1cf5b5d150bff07fa96\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\697a983ff8b6be23efe7df3e3bbc5a94\transformed\play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment:1.8.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a4193c6fbbe5e128015b7f6283124c0\transformed\fragment-1.8.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8c531c2afb462bf72eed5fcccc5521b\transformed\activity-1.9.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60fa4356081104865d164e2dc1a44707\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c35e3b0c2fe34519a603108fedf6f64\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c246ddff93723fa14095088b03472f98\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\640934fa0c2deb02232fb188bbe4540c\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83cc729ebb343678ef420dbf99d69098\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\27b18f4155bc5ffa642e6d94ad680607\transformed\firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d59858b3709795c3a4e2c9928bb49778\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c53ce620ea6072f75d375d7efaf4f97b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98f96d669de42549aba9c948d9b3d391\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e166bb1d6f54168e00acc5493c1d998b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\70b2f935023aef227fa195183aee45e2\transformed\transport-api-3.1.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f584d735ff9bfd7953105e9d7907cfc\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\67accc8724fe27e8ee25560dda61f8a8\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d911d07782f4978bb71e91acc0c48400\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6b333b6497d110c82f46d53344a04f8\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7519e99783a8412739804580db52b13\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [org.apache.cordova:framework:10.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1684f9ee58db97dc3c9598ebbc48c3f5\transformed\framework-10.1.1\AndroidManifest.xml:20:1-27:12
	package
		INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml
	android:versionCode
		INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:2:11-69
application
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:4:5-52:19
INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:4:5-52:19
MERGED from [:capacitor-firebase-messaging] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-17:19
MERGED from [:capacitor-firebase-messaging] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-17:19
MERGED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-24:19
MERGED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-24:19
MERGED from [:capacitor-cordova-android-plugins] C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-12:19
MERGED from [:capacitor-cordova-android-plugins] C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-12:19
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87382da045b5faebd5371143916fd9ef\transformed\play-services-location-21.3.0\AndroidManifest.xml:6:5-20
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87382da045b5faebd5371143916fd9ef\transformed\play-services-location-21.3.0\AndroidManifest.xml:6:5-20
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3897ee7a3a7e64eb47ff9b7bb8256b24\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3897ee7a3a7e64eb47ff9b7bb8256b24\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d121929483b8667b8bd7f522bf5e661\transformed\firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d121929483b8667b8bd7f522bf5e661\transformed\firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\324fc306ed84dc357040da54cc5f1fbc\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\324fc306ed84dc357040da54cc5f1fbc\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f546ee03c2308c684eeebc8faf0e1407\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f546ee03c2308c684eeebc8faf0e1407\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b70f94d043134f6a6c82aff3176808d5\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b70f94d043134f6a6c82aff3176808d5\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3cd5cde5baf76ffd76ccca9ac0ba6533\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3cd5cde5baf76ffd76ccca9ac0ba6533\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\509d7e86b27aaa293843dd95b79d84c3\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\509d7e86b27aaa293843dd95b79d84c3\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19f28f4dfdbce1cf5b5d150bff07fa96\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19f28f4dfdbce1cf5b5d150bff07fa96\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\697a983ff8b6be23efe7df3e3bbc5a94\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\697a983ff8b6be23efe7df3e3bbc5a94\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c35e3b0c2fe34519a603108fedf6f64\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c35e3b0c2fe34519a603108fedf6f64\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83cc729ebb343678ef420dbf99d69098\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83cc729ebb343678ef420dbf99d69098\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d59858b3709795c3a4e2c9928bb49778\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d59858b3709795c3a4e2c9928bb49778\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c53ce620ea6072f75d375d7efaf4f97b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c53ce620ea6072f75d375d7efaf4f97b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e166bb1d6f54168e00acc5493c1d998b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e166bb1d6f54168e00acc5493c1d998b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:9:9-35
	android:label
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:7:9-41
	android:roundIcon
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:8:9-58
	android:icon
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:6:9-47
	android:allowBackup
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:5:9-35
	android:theme
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:10:9-40
	android:usesCleartextTraffic
		ADDED from [:capacitor-cordova-android-plugins] C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:18-53
activity#io.ionic.starter.MainActivity
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:12:9-25:20
	android:label
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:15:13-56
	android:launchMode
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:17:13-44
	android:exported
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:18:13-36
	android:configChanges
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:13:13-140
	android:theme
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:16:13-62
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:14:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:20:13-23:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:21:17-69
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:21:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:22:17-77
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:22:27-74
service#io.capawesome.capacitorjs.plugins.firebase.messaging.FirebaseMessagingService
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:38:9-45:19
	android:exported
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:40:13-37
	android:stopWithTask
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:41:13-41
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:39:13-105
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:42:13-44:29
action#com.google.firebase.MESSAGING_EVENT
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:43:17-78
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:43:25-75
meta-data#com.google.firebase.messaging.default_notification_channel_id
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:48:9-50:71
	android:value
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:50:13-68
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:49:13-89
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:56:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d121929483b8667b8bd7f522bf5e661\transformed\firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d121929483b8667b8bd7f522bf5e661\transformed\firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f872c9901e2c95f57da6f5d814a1f18d\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f872c9901e2c95f57da6f5d814a1f18d\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c53ce620ea6072f75d375d7efaf4f97b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c53ce620ea6072f75d375d7efaf4f97b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:56:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:57:5-79
MERGED from [:capacitor-network] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\network\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
MERGED from [:capacitor-network] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\network\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d121929483b8667b8bd7f522bf5e661\transformed\firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d121929483b8667b8bd7f522bf5e661\transformed\firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f872c9901e2c95f57da6f5d814a1f18d\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f872c9901e2c95f57da6f5d814a1f18d\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c53ce620ea6072f75d375d7efaf4f97b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c53ce620ea6072f75d375d7efaf4f97b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e166bb1d6f54168e00acc5493c1d998b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e166bb1d6f54168e00acc5493c1d998b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:57:22-76
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:58:5-79
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:58:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:59:5-81
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:59:22-78
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:62:5-68
MERGED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
MERGED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
MERGED from [:capacitor-cordova-android-plugins] C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
MERGED from [:capacitor-cordova-android-plugins] C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f872c9901e2c95f57da6f5d814a1f18d\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f872c9901e2c95f57da6f5d814a1f18d\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:62:22-65
uses-permission#android.permission.VIBRATE
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:63:5-66
MERGED from [:capacitor-haptics] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
MERGED from [:capacitor-haptics] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:63:22-63
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:64:5-77
MERGED from [:capacitor-firebase-messaging] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-77
MERGED from [:capacitor-firebase-messaging] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-77
MERGED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-77
MERGED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-77
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:23:5-77
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:64:22-74
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:65:5-81
MERGED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-81
MERGED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-81
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:65:22-78
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:32:13-34:64
	android:resource
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:34:17-51
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:33:17-67
uses-sdk
INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml
MERGED from [:capacitor-firebase-messaging] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-firebase-messaging] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-app] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-app] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-geolocation] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\geolocation\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-geolocation] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\geolocation\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-haptics] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-haptics] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-keyboard] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\keyboard\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-keyboard] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\keyboard\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-network] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\network\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-network] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\network\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-screen-orientation] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\screen-orientation\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-screen-orientation] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\screen-orientation\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-status-bar] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\status-bar\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-status-bar] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\status-bar\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-android] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\android\capacitor\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-android] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\android\capacitor\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-cordova-android-plugins] C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:capacitor-cordova-android-plugins] C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\16884767c054ac4cab0f70a5a4855d4d\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\16884767c054ac4cab0f70a5a4855d4d\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dda665aa4a1576cfb1759fb2bbcd5279\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dda665aa4a1576cfb1759fb2bbcd5279\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75881b531e34911967ea794bd3408c30\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75881b531e34911967ea794bd3408c30\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef234481c09f01fb9f0508a5da2b1126\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef234481c09f01fb9f0508a5da2b1126\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:20:5-44
MERGED from [io.ionic.libs:iongeolocation-android:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf5c25e299eb52ddcbcc73083ca78071\transformed\iongeolocation-android-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [io.ionic.libs:iongeolocation-android:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf5c25e299eb52ddcbcc73083ca78071\transformed\iongeolocation-android-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87382da045b5faebd5371143916fd9ef\transformed\play-services-location-21.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87382da045b5faebd5371143916fd9ef\transformed\play-services-location-21.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3897ee7a3a7e64eb47ff9b7bb8256b24\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3897ee7a3a7e64eb47ff9b7bb8256b24\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d121929483b8667b8bd7f522bf5e661\transformed\firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d121929483b8667b8bd7f522bf5e661\transformed\firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\324fc306ed84dc357040da54cc5f1fbc\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\324fc306ed84dc357040da54cc5f1fbc\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f546ee03c2308c684eeebc8faf0e1407\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f546ee03c2308c684eeebc8faf0e1407\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b70f94d043134f6a6c82aff3176808d5\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b70f94d043134f6a6c82aff3176808d5\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3cd5cde5baf76ffd76ccca9ac0ba6533\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3cd5cde5baf76ffd76ccca9ac0ba6533\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f872c9901e2c95f57da6f5d814a1f18d\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f872c9901e2c95f57da6f5d814a1f18d\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\509d7e86b27aaa293843dd95b79d84c3\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\509d7e86b27aaa293843dd95b79d84c3\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fd50506c236bde2540ace785bc60713\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6fd50506c236bde2540ace785bc60713\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d222fc1537ba4d05d2594c911ab56ac7\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d222fc1537ba4d05d2594c911ab56ac7\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd457bbc543ecdbee5c91b5aa5e11e77\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd457bbc543ecdbee5c91b5aa5e11e77\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17f9b79c3605804ded7cffe50acaf238\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17f9b79c3605804ded7cffe50acaf238\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f8069ce470fc0e16369d43e56e54e22\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f8069ce470fc0e16369d43e56e54e22\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.webkit:webkit:1.12.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3fbbd497413c0ee4069f169f4dc7a260\transformed\webkit-1.12.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.12.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3fbbd497413c0ee4069f169f4dc7a260\transformed\webkit-1.12.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\243233694e4ca3136f1ca4dacc434c39\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\243233694e4ca3136f1ca4dacc434c39\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf2bdf699ee56f42fbfc2f03618abe36\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf2bdf699ee56f42fbfc2f03618abe36\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6c8f2b475cbb949dc656efeb2abd4fcd\transformed\activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6c8f2b475cbb949dc656efeb2abd4fcd\transformed\activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eaa603a2c27a5645906131bea5891cf1\transformed\core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eaa603a2c27a5645906131bea5891cf1\transformed\core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\206ff51956c4746556b0b1a85c73686b\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\206ff51956c4746556b0b1a85c73686b\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\111c5c3c3f6ae179fe7713443d447c72\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\111c5c3c3f6ae179fe7713443d447c72\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23941852aafd4e5308edbcd04fec6a27\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23941852aafd4e5308edbcd04fec6a27\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bfc7da70d4fbc8073307b3326dd9704e\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bfc7da70d4fbc8073307b3326dd9704e\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d68bb0f6b9dc7d41a2b6dfc3b4acad60\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d68bb0f6b9dc7d41a2b6dfc3b4acad60\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a75dca28172537968edb11f4713fc67\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a75dca28172537968edb11f4713fc67\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cb5e40cf228b6c33a81f0245b3675c9\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cb5e40cf228b6c33a81f0245b3675c9\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f212eb2fcec7b76a8049b85cea08416b\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f212eb2fcec7b76a8049b85cea08416b\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4b3e930a763ca268d3b38830b586bc2a\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4b3e930a763ca268d3b38830b586bc2a\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f974b30b07e5bde53cb3618bce731f69\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f974b30b07e5bde53cb3618bce731f69\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\998c918bf96ae2f6a4f5c8c644413a6f\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\998c918bf96ae2f6a4f5c8c644413a6f\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19f28f4dfdbce1cf5b5d150bff07fa96\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19f28f4dfdbce1cf5b5d150bff07fa96\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\697a983ff8b6be23efe7df3e3bbc5a94\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\697a983ff8b6be23efe7df3e3bbc5a94\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment:1.8.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a4193c6fbbe5e128015b7f6283124c0\transformed\fragment-1.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.8.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a4193c6fbbe5e128015b7f6283124c0\transformed\fragment-1.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8c531c2afb462bf72eed5fcccc5521b\transformed\activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8c531c2afb462bf72eed5fcccc5521b\transformed\activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60fa4356081104865d164e2dc1a44707\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60fa4356081104865d164e2dc1a44707\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c35e3b0c2fe34519a603108fedf6f64\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c35e3b0c2fe34519a603108fedf6f64\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c246ddff93723fa14095088b03472f98\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c246ddff93723fa14095088b03472f98\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\640934fa0c2deb02232fb188bbe4540c\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\640934fa0c2deb02232fb188bbe4540c\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83cc729ebb343678ef420dbf99d69098\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83cc729ebb343678ef420dbf99d69098\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\27b18f4155bc5ffa642e6d94ad680607\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\27b18f4155bc5ffa642e6d94ad680607\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d59858b3709795c3a4e2c9928bb49778\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d59858b3709795c3a4e2c9928bb49778\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c53ce620ea6072f75d375d7efaf4f97b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c53ce620ea6072f75d375d7efaf4f97b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98f96d669de42549aba9c948d9b3d391\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98f96d669de42549aba9c948d9b3d391\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e166bb1d6f54168e00acc5493c1d998b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e166bb1d6f54168e00acc5493c1d998b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\70b2f935023aef227fa195183aee45e2\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\70b2f935023aef227fa195183aee45e2\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f584d735ff9bfd7953105e9d7907cfc\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f584d735ff9bfd7953105e9d7907cfc\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\67accc8724fe27e8ee25560dda61f8a8\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\67accc8724fe27e8ee25560dda61f8a8\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d911d07782f4978bb71e91acc0c48400\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d911d07782f4978bb71e91acc0c48400\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6b333b6497d110c82f46d53344a04f8\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6b333b6497d110c82f46d53344a04f8\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7519e99783a8412739804580db52b13\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7519e99783a8412739804580db52b13\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [org.apache.cordova:framework:10.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1684f9ee58db97dc3c9598ebbc48c3f5\transformed\framework-10.1.1\AndroidManifest.xml:25:5-44
MERGED from [org.apache.cordova:framework:10.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1684f9ee58db97dc3c9598ebbc48c3f5\transformed\framework-10.1.1\AndroidManifest.xml:25:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml
service#io.capawesome.capacitorjs.plugins.firebase.messaging.MessagingService
ADDED from [:capacitor-firebase-messaging] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:9-16:19
	android:exported
		ADDED from [:capacitor-firebase-messaging] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [:capacitor-firebase-messaging] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-97
receiver#com.capacitorjs.plugins.localnotifications.TimedNotificationPublisher
ADDED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-106
	android:name
		ADDED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:19-103
receiver#com.capacitorjs.plugins.localnotifications.NotificationDismissReceiver
ADDED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-107
	android:name
		ADDED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:19-104
receiver#com.capacitorjs.plugins.localnotifications.LocalNotificationRestoreReceiver
ADDED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-23:20
	android:exported
		ADDED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-37
	android:directBootAware
		ADDED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-43
	android:name
		ADDED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-103
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.LOCKED_BOOT_COMPLETED+action:name:android.intent.action.QUICKBOOT_POWERON
ADDED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-22:29
action#android.intent.action.LOCKED_BOOT_COMPLETED
ADDED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-86
	android:name
		ADDED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:25-83
action#android.intent.action.BOOT_COMPLETED
ADDED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-79
	android:name
		ADDED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:25-76
action#android.intent.action.QUICKBOOT_POWERON
ADDED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:17-82
	android:name
		ADDED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:25-79
service#de.appplant.cordova.plugin.background.ForegroundService
ADDED from [:capacitor-cordova-android-plugins] C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-91
	android:name
		ADDED from [:capacitor-cordova-android-plugins] C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:18-88
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f872c9901e2c95f57da6f5d814a1f18d\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f872c9901e2c95f57da6f5d814a1f18d\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:30:13-78
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:33:13-35:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:34:17-81
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:34:25-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:47:13-82
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d121929483b8667b8bd7f522bf5e661\transformed\firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d121929483b8667b8bd7f522bf5e661\transformed\firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\324fc306ed84dc357040da54cc5f1fbc\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\324fc306ed84dc357040da54cc5f1fbc\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f546ee03c2308c684eeebc8faf0e1407\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f546ee03c2308c684eeebc8faf0e1407\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d59858b3709795c3a4e2c9928bb49778\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d59858b3709795c3a4e2c9928bb49778\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:56:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f546ee03c2308c684eeebc8faf0e1407\transformed\firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f546ee03c2308c684eeebc8faf0e1407\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:55:13-84
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:61:17-119
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3897ee7a3a7e64eb47ff9b7bb8256b24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3897ee7a3a7e64eb47ff9b7bb8256b24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3897ee7a3a7e64eb47ff9b7bb8256b24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3897ee7a3a7e64eb47ff9b7bb8256b24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d121929483b8667b8bd7f522bf5e661\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d121929483b8667b8bd7f522bf5e661\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d121929483b8667b8bd7f522bf5e661\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d121929483b8667b8bd7f522bf5e661\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d121929483b8667b8bd7f522bf5e661\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d121929483b8667b8bd7f522bf5e661\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\324fc306ed84dc357040da54cc5f1fbc\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\324fc306ed84dc357040da54cc5f1fbc\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\324fc306ed84dc357040da54cc5f1fbc\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f546ee03c2308c684eeebc8faf0e1407\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f546ee03c2308c684eeebc8faf0e1407\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f546ee03c2308c684eeebc8faf0e1407\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f546ee03c2308c684eeebc8faf0e1407\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f546ee03c2308c684eeebc8faf0e1407\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f546ee03c2308c684eeebc8faf0e1407\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f546ee03c2308c684eeebc8faf0e1407\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f546ee03c2308c684eeebc8faf0e1407\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f546ee03c2308c684eeebc8faf0e1407\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c35e3b0c2fe34519a603108fedf6f64\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c35e3b0c2fe34519a603108fedf6f64\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
permission#io.ionic.starter.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
uses-permission#io.ionic.starter.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\697a983ff8b6be23efe7df3e3bbc5a94\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\697a983ff8b6be23efe7df3e3bbc5a94\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\697a983ff8b6be23efe7df3e3bbc5a94\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d59858b3709795c3a4e2c9928bb49778\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d59858b3709795c3a4e2c9928bb49778\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d59858b3709795c3a4e2c9928bb49778\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c53ce620ea6072f75d375d7efaf4f97b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e166bb1d6f54168e00acc5493c1d998b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e166bb1d6f54168e00acc5493c1d998b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c53ce620ea6072f75d375d7efaf4f97b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c53ce620ea6072f75d375d7efaf4f97b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c53ce620ea6072f75d375d7efaf4f97b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c53ce620ea6072f75d375d7efaf4f97b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c53ce620ea6072f75d375d7efaf4f97b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e166bb1d6f54168e00acc5493c1d998b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e166bb1d6f54168e00acc5493c1d998b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e166bb1d6f54168e00acc5493c1d998b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e166bb1d6f54168e00acc5493c1d998b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e166bb1d6f54168e00acc5493c1d998b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e166bb1d6f54168e00acc5493c1d998b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e166bb1d6f54168e00acc5493c1d998b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
