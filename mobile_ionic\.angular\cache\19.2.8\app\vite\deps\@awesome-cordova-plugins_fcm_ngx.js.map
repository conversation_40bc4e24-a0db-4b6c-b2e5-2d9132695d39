{"version": 3, "sources": ["../../../../../../node_modules/@awesome-cordova-plugins/core/bootstrap.js", "../../../../../../node_modules/@awesome-cordova-plugins/core/decorators/common.js", "../../../../../../node_modules/@awesome-cordova-plugins/core/util.js", "../../../../../../node_modules/@awesome-cordova-plugins/core/awesome-cordova-plugin.js", "../../../../../../node_modules/@awesome-cordova-plugins/core/decorators/cordova.js", "../../../../../../node_modules/@awesome-cordova-plugins/core/index.js", "../../../../../../node_modules/@awesome-cordova-plugins/fcm/ngx/index.js"], "sourcesContent": ["/**\n *\n */\nexport function checkReady() {\n  if (typeof process === 'undefined') {\n    var win_1 = typeof window !== 'undefined' ? window : {};\n    var DEVICE_READY_TIMEOUT_1 = 5000;\n    // To help developers using cordova, we listen for the device ready event and\n    // log an error if it didn't fire in a reasonable amount of time. Generally,\n    // when this happens, developers should remove and reinstall plugins, since\n    // an inconsistent plugin is often the culprit.\n    var before_1 = Date.now();\n    var didFireReady_1 = false;\n    win_1.document.addEventListener('deviceready', function () {\n      console.log(\"Ionic Native: deviceready event fired after \" + (Date.now() - before_1) + \" ms\");\n      didFireReady_1 = true;\n    });\n    setTimeout(function () {\n      if (!didFireReady_1 && win_1.cordova) {\n        console.warn(\"Ionic Native: deviceready did not fire within \" + DEVICE_READY_TIMEOUT_1 + \"ms. This can happen when plugins are in an inconsistent state. Try removing plugins from plugins/ and reinstalling them.\");\n      }\n    }, DEVICE_READY_TIMEOUT_1);\n  }\n}\n", "import { fromEvent, Observable } from 'rxjs';\nexport var ERR_CORDOVA_NOT_AVAILABLE = {\n  error: 'cordova_not_available'\n};\nexport var ERR_PLUGIN_NOT_INSTALLED = {\n  error: 'plugin_not_installed'\n};\n/**\n * @param callback\n */\nexport function getPromise(callback) {\n  var tryNativePromise = function () {\n    if (Promise) {\n      return new Promise(function (resolve, reject) {\n        callback(resolve, reject);\n      });\n    } else {\n      console.error('No Promise support or polyfill found. To enable Ionic Native support, please add the es6-promise polyfill before this script, or run with a library like Angular or on a recent browser.');\n    }\n  };\n  if (typeof window !== 'undefined' && window.angular) {\n    var doc = window.document;\n    var injector = window.angular.element(doc.querySelector('[ng-app]') || doc.body).injector();\n    if (injector) {\n      var $q = injector.get('$q');\n      return $q(function (resolve, reject) {\n        callback(resolve, reject);\n      });\n    }\n    console.warn(\"Angular 1 was detected but $q couldn't be retrieved. This is usually when the app is not bootstrapped on the html or body tag. Falling back to native promises which won't trigger an automatic digest when promises resolve.\");\n  }\n  return tryNativePromise();\n}\n/**\n * @param pluginObj\n * @param methodName\n * @param args\n * @param opts\n */\nexport function wrapPromise(pluginObj, methodName, args, opts) {\n  if (opts === void 0) {\n    opts = {};\n  }\n  var pluginResult, rej;\n  var p = getPromise(function (resolve, reject) {\n    if (opts.destruct) {\n      pluginResult = callCordovaPlugin(pluginObj, methodName, args, opts, function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n          args[_i] = arguments[_i];\n        }\n        return resolve(args);\n      }, function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n          args[_i] = arguments[_i];\n        }\n        return reject(args);\n      });\n    } else {\n      pluginResult = callCordovaPlugin(pluginObj, methodName, args, opts, resolve, reject);\n    }\n    rej = reject;\n  });\n  // Angular throws an error on unhandled rejection, but in this case we have already printed\n  // a warning that Cordova is undefined or the plugin is uninstalled, so there is no reason\n  // to error\n  if (pluginResult && pluginResult.error) {\n    p.catch(function () {});\n    typeof rej === 'function' && rej(pluginResult.error);\n  }\n  return p;\n}\n/**\n * @param pluginObj\n * @param methodName\n * @param args\n * @param opts\n */\nfunction wrapOtherPromise(pluginObj, methodName, args, opts) {\n  if (opts === void 0) {\n    opts = {};\n  }\n  return getPromise(function (resolve, reject) {\n    var pluginResult = callCordovaPlugin(pluginObj, methodName, args, opts);\n    if (pluginResult) {\n      if (pluginResult.error) {\n        reject(pluginResult.error);\n      } else if (pluginResult.then) {\n        pluginResult.then(resolve).catch(reject);\n      }\n    } else {\n      reject({\n        error: 'unexpected_error'\n      });\n    }\n  });\n}\n/**\n * @param pluginObj\n * @param methodName\n * @param args\n * @param opts\n */\nfunction wrapObservable(pluginObj, methodName, args, opts) {\n  if (opts === void 0) {\n    opts = {};\n  }\n  return new Observable(function (observer) {\n    var pluginResult;\n    if (opts.destruct) {\n      pluginResult = callCordovaPlugin(pluginObj, methodName, args, opts, function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n          args[_i] = arguments[_i];\n        }\n        return observer.next(args);\n      }, function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n          args[_i] = arguments[_i];\n        }\n        return observer.error(args);\n      });\n    } else {\n      pluginResult = callCordovaPlugin(pluginObj, methodName, args, opts, observer.next.bind(observer), observer.error.bind(observer));\n    }\n    if (pluginResult && pluginResult.error) {\n      observer.error(pluginResult.error);\n      observer.complete();\n    }\n    return function () {\n      try {\n        if (opts.clearFunction) {\n          if (opts.clearWithArgs) {\n            return callCordovaPlugin(pluginObj, opts.clearFunction, args, opts, observer.next.bind(observer), observer.error.bind(observer));\n          }\n          return callCordovaPlugin(pluginObj, opts.clearFunction, []);\n        }\n      } catch (e) {\n        console.warn('Unable to clear the previous observable watch for', pluginObj.constructor.getPluginName(), methodName);\n        console.warn(e);\n      }\n    };\n  });\n}\n/**\n * Wrap the event with an observable\n *\n * @private\n * @param event event name\n * @param element The element to attach the event listener to\n * @returns {Observable}\n */\nfunction wrapEventObservable(event, element) {\n  element = typeof window !== 'undefined' && element ? get(window, element) : element || (typeof window !== 'undefined' ? window : {});\n  return fromEvent(element, event);\n}\n/**\n * @param plugin\n * @param methodName\n * @param pluginName\n */\nexport function checkAvailability(plugin, methodName, pluginName) {\n  var pluginRef, pluginPackage;\n  if (typeof plugin === 'string') {\n    pluginRef = plugin;\n  } else {\n    pluginRef = plugin.constructor.getPluginRef();\n    pluginName = plugin.constructor.getPluginName();\n    pluginPackage = plugin.constructor.getPluginInstallName();\n  }\n  var pluginInstance = getPlugin(pluginRef);\n  if (!pluginInstance || !!methodName && typeof pluginInstance[methodName] === 'undefined') {\n    if (typeof window === 'undefined' || !window.cordova) {\n      cordovaWarn(pluginName, methodName);\n      return ERR_CORDOVA_NOT_AVAILABLE;\n    }\n    pluginWarn(pluginName, pluginPackage, methodName);\n    return ERR_PLUGIN_NOT_INSTALLED;\n  }\n  return true;\n}\n/**\n * Checks if _objectInstance exists and has the method/property\n *\n * @param pluginObj\n * @param methodName\n * @private\n */\nexport function instanceAvailability(pluginObj, methodName) {\n  return pluginObj._objectInstance && (!methodName || typeof pluginObj._objectInstance[methodName] !== 'undefined');\n}\n/**\n * @param args\n * @param opts\n * @param resolve\n * @param reject\n */\nexport function setIndex(args, opts, resolve, reject) {\n  if (opts === void 0) {\n    opts = {};\n  }\n  // ignore resolve and reject in case sync\n  if (opts.sync) {\n    return args;\n  }\n  // If the plugin method expects myMethod(success, err, options)\n  if (opts.callbackOrder === 'reverse') {\n    // Get those arguments in the order [resolve, reject, ...restOfArgs]\n    args.unshift(reject);\n    args.unshift(resolve);\n  } else if (opts.callbackStyle === 'node') {\n    args.push(function (err, result) {\n      if (err) {\n        reject(err);\n      } else {\n        resolve(result);\n      }\n    });\n  } else if (opts.callbackStyle === 'object' && opts.successName && opts.errorName) {\n    var obj = {};\n    obj[opts.successName] = resolve;\n    obj[opts.errorName] = reject;\n    args.push(obj);\n  } else if (typeof opts.successIndex !== 'undefined' || typeof opts.errorIndex !== 'undefined') {\n    var setSuccessIndex = function () {\n      // If we've specified a success/error index\n      if (opts.successIndex > args.length) {\n        args[opts.successIndex] = resolve;\n      } else {\n        args.splice(opts.successIndex, 0, resolve);\n      }\n    };\n    var setErrorIndex = function () {\n      // We don't want that the reject cb gets spliced into the position of an optional argument that has not been\n      // defined and thus causing non expected behavior.\n      if (opts.errorIndex > args.length) {\n        args[opts.errorIndex] = reject; // insert the reject fn at the correct specific index\n      } else {\n        args.splice(opts.errorIndex, 0, reject); // otherwise just splice it into the array\n      }\n    };\n    if (opts.successIndex > opts.errorIndex) {\n      setErrorIndex();\n      setSuccessIndex();\n    } else {\n      setSuccessIndex();\n      setErrorIndex();\n    }\n  } else {\n    // Otherwise, let's tack them on to the end of the argument list\n    // which is 90% of cases\n    args.push(resolve);\n    args.push(reject);\n  }\n  return args;\n}\n/**\n * @param pluginObj\n * @param methodName\n * @param args\n * @param opts\n * @param resolve\n * @param reject\n */\nexport function callCordovaPlugin(pluginObj, methodName, args, opts, resolve, reject) {\n  if (opts === void 0) {\n    opts = {};\n  }\n  // Try to figure out where the success/error callbacks need to be bound\n  // to our promise resolve/reject handlers.\n  args = setIndex(args, opts, resolve, reject);\n  var availabilityCheck = checkAvailability(pluginObj, methodName);\n  if (availabilityCheck === true) {\n    var pluginInstance = getPlugin(pluginObj.constructor.getPluginRef());\n    // eslint-disable-next-line prefer-spread\n    return pluginInstance[methodName].apply(pluginInstance, args);\n  } else {\n    return availabilityCheck;\n  }\n}\n/**\n * @param pluginObj\n * @param methodName\n * @param args\n * @param opts\n * @param resolve\n * @param reject\n */\nexport function callInstance(pluginObj, methodName, args, opts, resolve, reject) {\n  if (opts === void 0) {\n    opts = {};\n  }\n  args = setIndex(args, opts, resolve, reject);\n  if (instanceAvailability(pluginObj, methodName)) {\n    // eslint-disable-next-line prefer-spread\n    return pluginObj._objectInstance[methodName].apply(pluginObj._objectInstance, args);\n  }\n}\n/**\n * @param pluginRef\n */\nexport function getPlugin(pluginRef) {\n  if (typeof window !== 'undefined') {\n    return get(window, pluginRef);\n  }\n  return null;\n}\n/**\n * @param element\n * @param path\n */\nexport function get(element, path) {\n  var paths = path.split('.');\n  var obj = element;\n  for (var i = 0; i < paths.length; i++) {\n    if (!obj) {\n      return null;\n    }\n    obj = obj[paths[i]];\n  }\n  return obj;\n}\n/**\n * @param pluginName\n * @param plugin\n * @param method\n */\nexport function pluginWarn(pluginName, plugin, method) {\n  if (method) {\n    console.warn('Native: tried calling ' + pluginName + '.' + method + ', but the ' + pluginName + ' plugin is not installed.');\n  } else {\n    console.warn(\"Native: tried accessing the \" + pluginName + \" plugin but it's not installed.\");\n  }\n  if (plugin) {\n    console.warn(\"Install the \" + pluginName + \" plugin: 'ionic cordova plugin add \" + plugin + \"'\");\n  }\n}\n/**\n * @private\n * @param pluginName\n * @param method\n */\nexport function cordovaWarn(pluginName, method) {\n  if (typeof process === 'undefined') {\n    if (method) {\n      console.warn('Native: tried calling ' + pluginName + '.' + method + ', but Cordova is not available. Make sure to include cordova.js or run in a device/simulator');\n    } else {\n      console.warn('Native: tried accessing the ' + pluginName + ' plugin but Cordova is not available. Make sure to include cordova.js or run in a device/simulator');\n    }\n  }\n}\n/**\n * @param pluginObj\n * @param methodName\n * @param opts\n * @private\n */\nexport var wrap = function (pluginObj, methodName, opts) {\n  if (opts === void 0) {\n    opts = {};\n  }\n  return function () {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    if (opts.sync) {\n      // Sync doesn't wrap the plugin with a promise or observable, it returns the result as-is\n      return callCordovaPlugin(pluginObj, methodName, args, opts);\n    } else if (opts.observable) {\n      return wrapObservable(pluginObj, methodName, args, opts);\n    } else if (opts.eventObservable && opts.event) {\n      return wrapEventObservable(opts.event, opts.element);\n    } else if (opts.otherPromise) {\n      return wrapOtherPromise(pluginObj, methodName, args, opts);\n    } else {\n      return wrapPromise(pluginObj, methodName, args, opts);\n    }\n  };\n};\n/**\n * @param pluginObj\n * @param methodName\n * @param opts\n * @private\n */\nexport function wrapInstance(pluginObj, methodName, opts) {\n  if (opts === void 0) {\n    opts = {};\n  }\n  return function () {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    if (opts.sync) {\n      return callInstance(pluginObj, methodName, args, opts);\n    } else if (opts.observable) {\n      return new Observable(function (observer) {\n        var pluginResult;\n        if (opts.destruct) {\n          pluginResult = callInstance(pluginObj, methodName, args, opts, function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n              args[_i] = arguments[_i];\n            }\n            return observer.next(args);\n          }, function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n              args[_i] = arguments[_i];\n            }\n            return observer.error(args);\n          });\n        } else {\n          pluginResult = callInstance(pluginObj, methodName, args, opts, observer.next.bind(observer), observer.error.bind(observer));\n        }\n        if (pluginResult && pluginResult.error) {\n          observer.error(pluginResult.error);\n        }\n        return function () {\n          try {\n            if (opts.clearWithArgs) {\n              return callInstance(pluginObj, opts.clearFunction, args, opts, observer.next.bind(observer), observer.error.bind(observer));\n            }\n            return callInstance(pluginObj, opts.clearFunction, []);\n          } catch (e) {\n            console.warn('Unable to clear the previous observable watch for', pluginObj.constructor.getPluginName(), methodName);\n            console.warn(e);\n          }\n        };\n      });\n    } else if (opts.otherPromise) {\n      return getPromise(function (resolve, reject) {\n        var result;\n        if (opts.destruct) {\n          result = callInstance(pluginObj, methodName, args, opts, function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n              args[_i] = arguments[_i];\n            }\n            return resolve(args);\n          }, function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n              args[_i] = arguments[_i];\n            }\n            return reject(args);\n          });\n        } else {\n          result = callInstance(pluginObj, methodName, args, opts, resolve, reject);\n        }\n        if (result && result.then) {\n          result.then(resolve, reject);\n        } else {\n          reject();\n        }\n      });\n    } else {\n      var pluginResult_1, rej_1;\n      var p = getPromise(function (resolve, reject) {\n        if (opts.destruct) {\n          pluginResult_1 = callInstance(pluginObj, methodName, args, opts, function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n              args[_i] = arguments[_i];\n            }\n            return resolve(args);\n          }, function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n              args[_i] = arguments[_i];\n            }\n            return reject(args);\n          });\n        } else {\n          pluginResult_1 = callInstance(pluginObj, methodName, args, opts, resolve, reject);\n        }\n        rej_1 = reject;\n      });\n      // Angular throws an error on unhandled rejection, but in this case we have already printed\n      // a warning that Cordova is undefined or the plugin is uninstalled, so there is no reason\n      // to error\n      if (pluginResult_1 && pluginResult_1.error) {\n        p.catch(function () {});\n        typeof rej_1 === 'function' && rej_1(pluginResult_1.error);\n      }\n      return p;\n    }\n  };\n}\n", "/**\n * @param element\n * @param path\n * @private\n */\nexport function get(element, path) {\n  var paths = path.split('.');\n  var obj = element;\n  for (var i = 0; i < paths.length; i++) {\n    if (!obj) {\n      return null;\n    }\n    obj = obj[paths[i]];\n  }\n  return obj;\n}\n/**\n * @param callback\n * @private\n */\nexport function getPromise(callback) {\n  if (callback === void 0) {\n    callback = function () {};\n  }\n  var tryNativePromise = function () {\n    if (typeof Promise === 'function' || typeof window !== 'undefined' && window.Promise) {\n      return new Promise(function (resolve, reject) {\n        callback(resolve, reject);\n      });\n    } else {\n      console.error('No Promise support or polyfill found. To enable Ionic Native support, please add the es6-promise polyfill before this script, or run with a library like Angular or on a recent browser.');\n    }\n  };\n  return tryNativePromise();\n}\n", "import { checkAvailability } from './decorators/common';\nimport { get } from './util';\nvar AwesomeCordovaNativePlugin = /** @class */function () {\n  function AwesomeCordovaNativePlugin() {}\n  /**\n   * Returns a boolean that indicates whether the plugin is installed\n   *\n   * @returns {boolean}\n   */\n  AwesomeCordovaNativePlugin.installed = function () {\n    var isAvailable = checkAvailability(this.pluginRef) === true;\n    return isAvailable;\n  };\n  /**\n   * Returns the original plugin object\n   */\n  AwesomeCordovaNativePlugin.getPlugin = function () {\n    if (typeof window !== 'undefined') {\n      return get(window, this.pluginRef);\n    }\n    return null;\n  };\n  /**\n   * Returns the plugin's name\n   */\n  AwesomeCordovaNativePlugin.getPluginName = function () {\n    var pluginName = this.pluginName;\n    return pluginName;\n  };\n  /**\n   * Returns the plugin's reference\n   */\n  AwesomeCordovaNativePlugin.getPluginRef = function () {\n    var pluginRef = this.pluginRef;\n    return pluginRef;\n  };\n  /**\n   * Returns the plugin's install name\n   */\n  AwesomeCordovaNativePlugin.getPluginInstallName = function () {\n    var plugin = this.plugin;\n    return plugin;\n  };\n  /**\n   * Returns the plugin's supported platforms\n   */\n  AwesomeCordovaNativePlugin.getSupportedPlatforms = function () {\n    var platform = this.platforms;\n    return platform;\n  };\n  AwesomeCordovaNativePlugin.pluginName = '';\n  AwesomeCordovaNativePlugin.pluginRef = '';\n  AwesomeCordovaNativePlugin.plugin = '';\n  AwesomeCordovaNativePlugin.repo = '';\n  AwesomeCordovaNativePlugin.platforms = [];\n  AwesomeCordovaNativePlugin.install = '';\n  return AwesomeCordovaNativePlugin;\n}();\nexport { AwesomeCordovaNativePlugin };\n", "import { wrap } from './common';\n/**\n * @param pluginObj\n * @param methodName\n * @param config\n * @param args\n */\nexport function cordova(pluginObj, methodName, config, args) {\n  return wrap(pluginObj, methodName, config).apply(this, args);\n}\n", "import { checkReady } from './bootstrap';\nexport { AwesomeCordovaNativePlugin } from './awesome-cordova-plugin';\n// Decorators\nexport { checkAvailability, instanceAvailability, wrap, getPromise } from './decorators/common';\nexport * from './decorators/cordova';\nexport * from './decorators/cordova-function-override';\nexport * from './decorators/cordova-instance';\nexport * from './decorators/cordova-property';\nexport * from './decorators/instance-property';\nexport * from './decorators/interfaces';\ncheckReady();\n", "import { __decorate, __extends } from \"tslib\";\nimport { AwesomeCordovaNativePlugin, cordova } from '@awesome-cordova-plugins/core';\nimport { Injectable } from '@angular/core';\nimport { Observable } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nvar FCM = /** @class */function (_super) {\n  __extends(FCM, _super);\n  function FCM() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  FCM.prototype.getAPNSToken = function () {\n    return cordova(this, \"getAPNSToken\", {}, arguments);\n  };\n  FCM.prototype.getToken = function () {\n    return cordova(this, \"getToken\", {}, arguments);\n  };\n  FCM.prototype.onTokenRefresh = function () {\n    return cordova(this, \"onTokenRefresh\", {\n      \"observable\": true\n    }, arguments);\n  };\n  FCM.prototype.subscribeToTopic = function (topic) {\n    return cordova(this, \"subscribeToTopic\", {}, arguments);\n  };\n  FCM.prototype.unsubscribeFromTopic = function (topic) {\n    return cordova(this, \"unsubscribeFromTopic\", {}, arguments);\n  };\n  FCM.prototype.hasPermission = function () {\n    return cordova(this, \"hasPermission\", {}, arguments);\n  };\n  FCM.prototype.onNotification = function () {\n    return cordova(this, \"onNotification\", {\n      \"observable\": true,\n      \"successIndex\": 0,\n      \"errorIndex\": 2\n    }, arguments);\n  };\n  FCM.prototype.clearAllNotifications = function () {\n    return cordova(this, \"clearAllNotifications\", {}, arguments);\n  };\n  FCM.prototype.requestPushPermissionIOS = function (options) {\n    return cordova(this, \"requestPushPermissionIOS\", {}, arguments);\n  };\n  FCM.prototype.createNotificationChannelAndroid = function (channelConfig) {\n    return cordova(this, \"createNotificationChannelAndroid\", {}, arguments);\n  };\n  FCM.ɵfac = /* @__PURE__ */(() => {\n    let ɵFCM_BaseFactory;\n    return function FCM_Factory(__ngFactoryType__) {\n      return (ɵFCM_BaseFactory || (ɵFCM_BaseFactory = i0.ɵɵgetInheritedFactory(FCM)))(__ngFactoryType__ || FCM);\n    };\n  })();\n  FCM.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: FCM,\n    factory: FCM.ɵfac\n  });\n  FCM.pluginName = \"FCM\";\n  FCM.plugin = \"cordova-plugin-fcm-with-dependecy-updated\";\n  FCM.pluginRef = \"FCMPlugin\";\n  FCM.repo = \"https://github.com/andrehtissot/cordova-plugin-fcm-with-dependecy-updated\";\n  FCM.platforms = [\"Android\", \"iOS\"];\n  FCM = __decorate([], FCM);\n  return FCM;\n}(AwesomeCordovaNativePlugin);\nexport { FCM };\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FCM, [{\n    type: Injectable\n  }], null, {\n    getAPNSToken: [],\n    getToken: [],\n    onTokenRefresh: [],\n    subscribeToTopic: [],\n    unsubscribeFromTopic: [],\n    hasPermission: [],\n    onNotification: [],\n    clearAllNotifications: [],\n    requestPushPermissionIOS: [],\n    createNotificationChannelAndroid: []\n  });\n})();\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAGO,SAAS,aAAa;AAC3B,MAAI,OAAO,YAAY,aAAa;AAClC,QAAI,QAAQ,OAAO,WAAW,cAAc,SAAS,CAAC;AACtD,QAAI,yBAAyB;AAK7B,QAAI,WAAW,KAAK,IAAI;AACxB,QAAI,iBAAiB;AACrB,UAAM,SAAS,iBAAiB,eAAe,WAAY;AACzD,cAAQ,IAAI,kDAAkD,KAAK,IAAI,IAAI,YAAY,KAAK;AAC5F,uBAAiB;AAAA,IACnB,CAAC;AACD,eAAW,WAAY;AACrB,UAAI,CAAC,kBAAkB,MAAM,SAAS;AACpC,gBAAQ,KAAK,mDAAmD,yBAAyB,0HAA0H;AAAA,MACrN;AAAA,IACF,GAAG,sBAAsB;AAAA,EAC3B;AACF;;;ACtBO,IAAI,4BAA4B;AAAA,EACrC,OAAO;AACT;AACO,IAAI,2BAA2B;AAAA,EACpC,OAAO;AACT;AAIO,SAAS,WAAW,UAAU;AACnC,MAAI,mBAAmB,WAAY;AACjC,QAAI,SAAS;AACX,aAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,iBAAS,SAAS,MAAM;AAAA,MAC1B,CAAC;AAAA,IACH,OAAO;AACL,cAAQ,MAAM,0LAA0L;AAAA,IAC1M;AAAA,EACF;AACA,MAAI,OAAO,WAAW,eAAe,OAAO,SAAS;AACnD,QAAI,MAAM,OAAO;AACjB,QAAI,WAAW,OAAO,QAAQ,QAAQ,IAAI,cAAc,UAAU,KAAK,IAAI,IAAI,EAAE,SAAS;AAC1F,QAAI,UAAU;AACZ,UAAI,KAAK,SAAS,IAAI,IAAI;AAC1B,aAAO,GAAG,SAAU,SAAS,QAAQ;AACnC,iBAAS,SAAS,MAAM;AAAA,MAC1B,CAAC;AAAA,IACH;AACA,YAAQ,KAAK,+NAA+N;AAAA,EAC9O;AACA,SAAO,iBAAiB;AAC1B;AAOO,SAAS,YAAY,WAAW,YAAY,MAAM,MAAM;AAC7D,MAAI,SAAS,QAAQ;AACnB,WAAO,CAAC;AAAA,EACV;AACA,MAAI,cAAc;AAClB,MAAI,IAAI,WAAW,SAAU,SAAS,QAAQ;AAC5C,QAAI,KAAK,UAAU;AACjB,qBAAe,kBAAkB,WAAW,YAAY,MAAM,MAAM,WAAY;AAC9E,YAAIA,QAAO,CAAC;AACZ,iBAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,UAAAA,MAAK,EAAE,IAAI,UAAU,EAAE;AAAA,QACzB;AACA,eAAO,QAAQA,KAAI;AAAA,MACrB,GAAG,WAAY;AACb,YAAIA,QAAO,CAAC;AACZ,iBAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,UAAAA,MAAK,EAAE,IAAI,UAAU,EAAE;AAAA,QACzB;AACA,eAAO,OAAOA,KAAI;AAAA,MACpB,CAAC;AAAA,IACH,OAAO;AACL,qBAAe,kBAAkB,WAAW,YAAY,MAAM,MAAM,SAAS,MAAM;AAAA,IACrF;AACA,UAAM;AAAA,EACR,CAAC;AAID,MAAI,gBAAgB,aAAa,OAAO;AACtC,MAAE,MAAM,WAAY;AAAA,IAAC,CAAC;AACtB,WAAO,QAAQ,cAAc,IAAI,aAAa,KAAK;AAAA,EACrD;AACA,SAAO;AACT;AAOA,SAAS,iBAAiB,WAAW,YAAY,MAAM,MAAM;AAC3D,MAAI,SAAS,QAAQ;AACnB,WAAO,CAAC;AAAA,EACV;AACA,SAAO,WAAW,SAAU,SAAS,QAAQ;AAC3C,QAAI,eAAe,kBAAkB,WAAW,YAAY,MAAM,IAAI;AACtE,QAAI,cAAc;AAChB,UAAI,aAAa,OAAO;AACtB,eAAO,aAAa,KAAK;AAAA,MAC3B,WAAW,aAAa,MAAM;AAC5B,qBAAa,KAAK,OAAO,EAAE,MAAM,MAAM;AAAA,MACzC;AAAA,IACF,OAAO;AACL,aAAO;AAAA,QACL,OAAO;AAAA,MACT,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACH;AAOA,SAAS,eAAe,WAAW,YAAY,MAAM,MAAM;AACzD,MAAI,SAAS,QAAQ;AACnB,WAAO,CAAC;AAAA,EACV;AACA,SAAO,IAAI,WAAW,SAAU,UAAU;AACxC,QAAI;AACJ,QAAI,KAAK,UAAU;AACjB,qBAAe,kBAAkB,WAAW,YAAY,MAAM,MAAM,WAAY;AAC9E,YAAIA,QAAO,CAAC;AACZ,iBAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,UAAAA,MAAK,EAAE,IAAI,UAAU,EAAE;AAAA,QACzB;AACA,eAAO,SAAS,KAAKA,KAAI;AAAA,MAC3B,GAAG,WAAY;AACb,YAAIA,QAAO,CAAC;AACZ,iBAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,UAAAA,MAAK,EAAE,IAAI,UAAU,EAAE;AAAA,QACzB;AACA,eAAO,SAAS,MAAMA,KAAI;AAAA,MAC5B,CAAC;AAAA,IACH,OAAO;AACL,qBAAe,kBAAkB,WAAW,YAAY,MAAM,MAAM,SAAS,KAAK,KAAK,QAAQ,GAAG,SAAS,MAAM,KAAK,QAAQ,CAAC;AAAA,IACjI;AACA,QAAI,gBAAgB,aAAa,OAAO;AACtC,eAAS,MAAM,aAAa,KAAK;AACjC,eAAS,SAAS;AAAA,IACpB;AACA,WAAO,WAAY;AACjB,UAAI;AACF,YAAI,KAAK,eAAe;AACtB,cAAI,KAAK,eAAe;AACtB,mBAAO,kBAAkB,WAAW,KAAK,eAAe,MAAM,MAAM,SAAS,KAAK,KAAK,QAAQ,GAAG,SAAS,MAAM,KAAK,QAAQ,CAAC;AAAA,UACjI;AACA,iBAAO,kBAAkB,WAAW,KAAK,eAAe,CAAC,CAAC;AAAA,QAC5D;AAAA,MACF,SAAS,GAAG;AACV,gBAAQ,KAAK,qDAAqD,UAAU,YAAY,cAAc,GAAG,UAAU;AACnH,gBAAQ,KAAK,CAAC;AAAA,MAChB;AAAA,IACF;AAAA,EACF,CAAC;AACH;AASA,SAAS,oBAAoB,OAAO,SAAS;AAC3C,YAAU,OAAO,WAAW,eAAe,UAAU,IAAI,QAAQ,OAAO,IAAI,YAAY,OAAO,WAAW,cAAc,SAAS,CAAC;AAClI,SAAO,UAAU,SAAS,KAAK;AACjC;AAMO,SAAS,kBAAkB,QAAQ,YAAY,YAAY;AAChE,MAAI,WAAW;AACf,MAAI,OAAO,WAAW,UAAU;AAC9B,gBAAY;AAAA,EACd,OAAO;AACL,gBAAY,OAAO,YAAY,aAAa;AAC5C,iBAAa,OAAO,YAAY,cAAc;AAC9C,oBAAgB,OAAO,YAAY,qBAAqB;AAAA,EAC1D;AACA,MAAI,iBAAiB,UAAU,SAAS;AACxC,MAAI,CAAC,kBAAkB,CAAC,CAAC,cAAc,OAAO,eAAe,UAAU,MAAM,aAAa;AACxF,QAAI,OAAO,WAAW,eAAe,CAAC,OAAO,SAAS;AACpD,kBAAY,YAAY,UAAU;AAClC,aAAO;AAAA,IACT;AACA,eAAW,YAAY,eAAe,UAAU;AAChD,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAiBO,SAAS,SAAS,MAAM,MAAM,SAAS,QAAQ;AACpD,MAAI,SAAS,QAAQ;AACnB,WAAO,CAAC;AAAA,EACV;AAEA,MAAI,KAAK,MAAM;AACb,WAAO;AAAA,EACT;AAEA,MAAI,KAAK,kBAAkB,WAAW;AAEpC,SAAK,QAAQ,MAAM;AACnB,SAAK,QAAQ,OAAO;AAAA,EACtB,WAAW,KAAK,kBAAkB,QAAQ;AACxC,SAAK,KAAK,SAAU,KAAK,QAAQ;AAC/B,UAAI,KAAK;AACP,eAAO,GAAG;AAAA,MACZ,OAAO;AACL,gBAAQ,MAAM;AAAA,MAChB;AAAA,IACF,CAAC;AAAA,EACH,WAAW,KAAK,kBAAkB,YAAY,KAAK,eAAe,KAAK,WAAW;AAChF,QAAI,MAAM,CAAC;AACX,QAAI,KAAK,WAAW,IAAI;AACxB,QAAI,KAAK,SAAS,IAAI;AACtB,SAAK,KAAK,GAAG;AAAA,EACf,WAAW,OAAO,KAAK,iBAAiB,eAAe,OAAO,KAAK,eAAe,aAAa;AAC7F,QAAI,kBAAkB,WAAY;AAEhC,UAAI,KAAK,eAAe,KAAK,QAAQ;AACnC,aAAK,KAAK,YAAY,IAAI;AAAA,MAC5B,OAAO;AACL,aAAK,OAAO,KAAK,cAAc,GAAG,OAAO;AAAA,MAC3C;AAAA,IACF;AACA,QAAI,gBAAgB,WAAY;AAG9B,UAAI,KAAK,aAAa,KAAK,QAAQ;AACjC,aAAK,KAAK,UAAU,IAAI;AAAA,MAC1B,OAAO;AACL,aAAK,OAAO,KAAK,YAAY,GAAG,MAAM;AAAA,MACxC;AAAA,IACF;AACA,QAAI,KAAK,eAAe,KAAK,YAAY;AACvC,oBAAc;AACd,sBAAgB;AAAA,IAClB,OAAO;AACL,sBAAgB;AAChB,oBAAc;AAAA,IAChB;AAAA,EACF,OAAO;AAGL,SAAK,KAAK,OAAO;AACjB,SAAK,KAAK,MAAM;AAAA,EAClB;AACA,SAAO;AACT;AASO,SAAS,kBAAkB,WAAW,YAAY,MAAM,MAAM,SAAS,QAAQ;AACpF,MAAI,SAAS,QAAQ;AACnB,WAAO,CAAC;AAAA,EACV;AAGA,SAAO,SAAS,MAAM,MAAM,SAAS,MAAM;AAC3C,MAAI,oBAAoB,kBAAkB,WAAW,UAAU;AAC/D,MAAI,sBAAsB,MAAM;AAC9B,QAAI,iBAAiB,UAAU,UAAU,YAAY,aAAa,CAAC;AAEnE,WAAO,eAAe,UAAU,EAAE,MAAM,gBAAgB,IAAI;AAAA,EAC9D,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAsBO,SAAS,UAAU,WAAW;AACnC,MAAI,OAAO,WAAW,aAAa;AACjC,WAAO,IAAI,QAAQ,SAAS;AAAA,EAC9B;AACA,SAAO;AACT;AAKO,SAAS,IAAI,SAAS,MAAM;AACjC,MAAI,QAAQ,KAAK,MAAM,GAAG;AAC1B,MAAI,MAAM;AACV,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,QAAI,CAAC,KAAK;AACR,aAAO;AAAA,IACT;AACA,UAAM,IAAI,MAAM,CAAC,CAAC;AAAA,EACpB;AACA,SAAO;AACT;AAMO,SAAS,WAAW,YAAY,QAAQ,QAAQ;AACrD,MAAI,QAAQ;AACV,YAAQ,KAAK,2BAA2B,aAAa,MAAM,SAAS,eAAe,aAAa,2BAA2B;AAAA,EAC7H,OAAO;AACL,YAAQ,KAAK,iCAAiC,aAAa,iCAAiC;AAAA,EAC9F;AACA,MAAI,QAAQ;AACV,YAAQ,KAAK,iBAAiB,aAAa,wCAAwC,SAAS,GAAG;AAAA,EACjG;AACF;AAMO,SAAS,YAAY,YAAY,QAAQ;AAC9C,MAAI,OAAO,YAAY,aAAa;AAClC,QAAI,QAAQ;AACV,cAAQ,KAAK,2BAA2B,aAAa,MAAM,SAAS,8FAA8F;AAAA,IACpK,OAAO;AACL,cAAQ,KAAK,iCAAiC,aAAa,oGAAoG;AAAA,IACjK;AAAA,EACF;AACF;AAOO,IAAI,OAAO,SAAU,WAAW,YAAY,MAAM;AACvD,MAAI,SAAS,QAAQ;AACnB,WAAO,CAAC;AAAA,EACV;AACA,SAAO,WAAY;AACjB,QAAI,OAAO,CAAC;AACZ,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,WAAK,EAAE,IAAI,UAAU,EAAE;AAAA,IACzB;AACA,QAAI,KAAK,MAAM;AAEb,aAAO,kBAAkB,WAAW,YAAY,MAAM,IAAI;AAAA,IAC5D,WAAW,KAAK,YAAY;AAC1B,aAAO,eAAe,WAAW,YAAY,MAAM,IAAI;AAAA,IACzD,WAAW,KAAK,mBAAmB,KAAK,OAAO;AAC7C,aAAO,oBAAoB,KAAK,OAAO,KAAK,OAAO;AAAA,IACrD,WAAW,KAAK,cAAc;AAC5B,aAAO,iBAAiB,WAAW,YAAY,MAAM,IAAI;AAAA,IAC3D,OAAO;AACL,aAAO,YAAY,WAAW,YAAY,MAAM,IAAI;AAAA,IACtD;AAAA,EACF;AACF;;;ACxXO,SAASC,KAAI,SAAS,MAAM;AACjC,MAAI,QAAQ,KAAK,MAAM,GAAG;AAC1B,MAAI,MAAM;AACV,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,QAAI,CAAC,KAAK;AACR,aAAO;AAAA,IACT;AACA,UAAM,IAAI,MAAM,CAAC,CAAC;AAAA,EACpB;AACA,SAAO;AACT;;;ACbA,IAAI;AAAA;AAAA,EAA0C,WAAY;AACxD,aAASC,8BAA6B;AAAA,IAAC;AAMvC,IAAAA,4BAA2B,YAAY,WAAY;AACjD,UAAI,cAAc,kBAAkB,KAAK,SAAS,MAAM;AACxD,aAAO;AAAA,IACT;AAIA,IAAAA,4BAA2B,YAAY,WAAY;AACjD,UAAI,OAAO,WAAW,aAAa;AACjC,eAAOC,KAAI,QAAQ,KAAK,SAAS;AAAA,MACnC;AACA,aAAO;AAAA,IACT;AAIA,IAAAD,4BAA2B,gBAAgB,WAAY;AACrD,UAAI,aAAa,KAAK;AACtB,aAAO;AAAA,IACT;AAIA,IAAAA,4BAA2B,eAAe,WAAY;AACpD,UAAI,YAAY,KAAK;AACrB,aAAO;AAAA,IACT;AAIA,IAAAA,4BAA2B,uBAAuB,WAAY;AAC5D,UAAI,SAAS,KAAK;AAClB,aAAO;AAAA,IACT;AAIA,IAAAA,4BAA2B,wBAAwB,WAAY;AAC7D,UAAI,WAAW,KAAK;AACpB,aAAO;AAAA,IACT;AACA,IAAAA,4BAA2B,aAAa;AACxC,IAAAA,4BAA2B,YAAY;AACvC,IAAAA,4BAA2B,SAAS;AACpC,IAAAA,4BAA2B,OAAO;AAClC,IAAAA,4BAA2B,YAAY,CAAC;AACxC,IAAAA,4BAA2B,UAAU;AACrC,WAAOA;AAAA,EACT,EAAE;AAAA;;;AClDK,SAAS,QAAQ,WAAW,YAAY,QAAQ,MAAM;AAC3D,SAAO,KAAK,WAAW,YAAY,MAAM,EAAE,MAAM,MAAM,IAAI;AAC7D;;;ACCA,WAAW;;;ACLX,IAAI;AAAA;AAAA,EAAmB,SAAU,QAAQ;AACvC,cAAUE,MAAK,MAAM;AACrB,aAASA,OAAM;AACb,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC7D;AACA,IAAAA,KAAI,UAAU,eAAe,WAAY;AACvC,aAAO,QAAQ,MAAM,gBAAgB,CAAC,GAAG,SAAS;AAAA,IACpD;AACA,IAAAA,KAAI,UAAU,WAAW,WAAY;AACnC,aAAO,QAAQ,MAAM,YAAY,CAAC,GAAG,SAAS;AAAA,IAChD;AACA,IAAAA,KAAI,UAAU,iBAAiB,WAAY;AACzC,aAAO,QAAQ,MAAM,kBAAkB;AAAA,QACrC,cAAc;AAAA,MAChB,GAAG,SAAS;AAAA,IACd;AACA,IAAAA,KAAI,UAAU,mBAAmB,SAAU,OAAO;AAChD,aAAO,QAAQ,MAAM,oBAAoB,CAAC,GAAG,SAAS;AAAA,IACxD;AACA,IAAAA,KAAI,UAAU,uBAAuB,SAAU,OAAO;AACpD,aAAO,QAAQ,MAAM,wBAAwB,CAAC,GAAG,SAAS;AAAA,IAC5D;AACA,IAAAA,KAAI,UAAU,gBAAgB,WAAY;AACxC,aAAO,QAAQ,MAAM,iBAAiB,CAAC,GAAG,SAAS;AAAA,IACrD;AACA,IAAAA,KAAI,UAAU,iBAAiB,WAAY;AACzC,aAAO,QAAQ,MAAM,kBAAkB;AAAA,QACrC,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB,GAAG,SAAS;AAAA,IACd;AACA,IAAAA,KAAI,UAAU,wBAAwB,WAAY;AAChD,aAAO,QAAQ,MAAM,yBAAyB,CAAC,GAAG,SAAS;AAAA,IAC7D;AACA,IAAAA,KAAI,UAAU,2BAA2B,SAAU,SAAS;AAC1D,aAAO,QAAQ,MAAM,4BAA4B,CAAC,GAAG,SAAS;AAAA,IAChE;AACA,IAAAA,KAAI,UAAU,mCAAmC,SAAU,eAAe;AACxE,aAAO,QAAQ,MAAM,oCAAoC,CAAC,GAAG,SAAS;AAAA,IACxE;AACA,IAAAA,KAAI,OAAuB,uBAAM;AAC/B,UAAI;AACJ,aAAO,SAAS,YAAY,mBAAmB;AAC7C,gBAAQ,qBAAqB,mBAAsB,sBAAsBA,IAAG,IAAI,qBAAqBA,IAAG;AAAA,MAC1G;AAAA,IACF,GAAG;AACH,IAAAA,KAAI,QAA0B,mBAAmB;AAAA,MAC/C,OAAOA;AAAA,MACP,SAASA,KAAI;AAAA,IACf,CAAC;AACD,IAAAA,KAAI,aAAa;AACjB,IAAAA,KAAI,SAAS;AACb,IAAAA,KAAI,YAAY;AAChB,IAAAA,KAAI,OAAO;AACX,IAAAA,KAAI,YAAY,CAAC,WAAW,KAAK;AACjC,IAAAA,OAAM,WAAW,CAAC,GAAGA,IAAG;AACxB,WAAOA;AAAA,EACT,EAAE,0BAA0B;AAAA;CAE3B,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,KAAK,CAAC;AAAA,IAC5E,MAAM;AAAA,EACR,CAAC,GAAG,MAAM;AAAA,IACR,cAAc,CAAC;AAAA,IACf,UAAU,CAAC;AAAA,IACX,gBAAgB,CAAC;AAAA,IACjB,kBAAkB,CAAC;AAAA,IACnB,sBAAsB,CAAC;AAAA,IACvB,eAAe,CAAC;AAAA,IAChB,gBAAgB,CAAC;AAAA,IACjB,uBAAuB,CAAC;AAAA,IACxB,0BAA0B,CAAC;AAAA,IAC3B,kCAAkC,CAAC;AAAA,EACrC,CAAC;AACH,GAAG;", "names": ["args", "get", "AwesomeCordovaNativePlugin", "get", "FCM"]}