{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/components/capacitor.js", "../../../../../../node_modules/@ionic/core/components/keyboard.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as win } from './index6.js';\nconst getCapacitor = () => {\n  if (win !== undefined) {\n    return win.Capacitor;\n  }\n  return undefined;\n};\nexport { getCapacitor as g };", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { g as getCapacitor } from './capacitor.js';\nvar ExceptionCode;\n(function (ExceptionCode) {\n  /**\n   * API is not implemented.\n   *\n   * This usually means the API can't be used because it is not implemented for\n   * the current platform.\n   */\n  ExceptionCode[\"Unimplemented\"] = \"UNIMPLEMENTED\";\n  /**\n   * API is not available.\n   *\n   * This means the API can't be used right now because:\n   *   - it is currently missing a prerequisite, such as network connectivity\n   *   - it requires a particular platform or browser version\n   */\n  ExceptionCode[\"Unavailable\"] = \"UNAVAILABLE\";\n})(ExceptionCode || (ExceptionCode = {}));\nvar KeyboardResize;\n(function (KeyboardResize) {\n  /**\n   * Only the `body` HTML element will be resized.\n   * Relative units are not affected, because the viewport does not change.\n   *\n   * @since 1.0.0\n   */\n  KeyboardResize[\"Body\"] = \"body\";\n  /**\n   * Only the `ion-app` HTML element will be resized.\n   * Use it only for Ionic Framework apps.\n   *\n   * @since 1.0.0\n   */\n  KeyboardResize[\"Ionic\"] = \"ionic\";\n  /**\n   * The whole native Web View will be resized when the keyboard shows/hides.\n   * This affects the `vh` relative unit.\n   *\n   * @since 1.0.0\n   */\n  KeyboardResize[\"Native\"] = \"native\";\n  /**\n   * Neither the app nor the Web View are resized.\n   *\n   * @since 1.0.0\n   */\n  KeyboardResize[\"None\"] = \"none\";\n})(KeyboardResize || (KeyboardResize = {}));\nconst Keyboard = {\n  getEngine() {\n    const capacitor = getCapacitor();\n    if (capacitor === null || capacitor === void 0 ? void 0 : capacitor.isPluginAvailable('Keyboard')) {\n      return capacitor.Plugins.Keyboard;\n    }\n    return undefined;\n  },\n  getResizeMode() {\n    const engine = this.getEngine();\n    if (!(engine === null || engine === void 0 ? void 0 : engine.getResizeMode)) {\n      return Promise.resolve(undefined);\n    }\n    return engine.getResizeMode().catch(e => {\n      if (e.code === ExceptionCode.Unimplemented) {\n        // If the native implementation is not available\n        // we treat it the same as if the plugin is not available.\n        return undefined;\n      }\n      throw e;\n    });\n  }\n};\nexport { Keyboard as K, KeyboardResize as a };"], "mappings": ";;;;;AAIA,IAAM,eAAe,MAAM;AACzB,MAAI,QAAQ,QAAW;AACrB,WAAO,IAAI;AAAA,EACb;AACA,SAAO;AACT;;;ACLA,IAAI;AAAA,CACH,SAAUA,gBAAe;AAOxB,EAAAA,eAAc,eAAe,IAAI;AAQjC,EAAAA,eAAc,aAAa,IAAI;AACjC,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AACxC,IAAI;AAAA,CACH,SAAUC,iBAAgB;AAOzB,EAAAA,gBAAe,MAAM,IAAI;AAOzB,EAAAA,gBAAe,OAAO,IAAI;AAO1B,EAAAA,gBAAe,QAAQ,IAAI;AAM3B,EAAAA,gBAAe,MAAM,IAAI;AAC3B,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAC1C,IAAM,WAAW;AAAA,EACf,YAAY;AACV,UAAM,YAAY,aAAa;AAC/B,QAAI,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,kBAAkB,UAAU,GAAG;AACjG,aAAO,UAAU,QAAQ;AAAA,IAC3B;AACA,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB;AACd,UAAM,SAAS,KAAK,UAAU;AAC9B,QAAI,EAAE,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,gBAAgB;AAC3E,aAAO,QAAQ,QAAQ,MAAS;AAAA,IAClC;AACA,WAAO,OAAO,cAAc,EAAE,MAAM,OAAK;AACvC,UAAI,EAAE,SAAS,cAAc,eAAe;AAG1C,eAAO;AAAA,MACT;AACA,YAAM;AAAA,IACR,CAAC;AAAA,EACH;AACF;", "names": ["ExceptionCode", "KeyboardResize"]}