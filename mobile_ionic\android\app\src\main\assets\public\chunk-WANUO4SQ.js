import{a as _,b as m}from"./chunk-OMB27VJZ.js";import{a as M}from"./chunk-H6ETZ4HT.js";import{a as Y}from"./chunk-GOWQWRZR.js";import{a as D}from"./chunk-H2PHEXKY.js";import{c as $,d as H,f as V,g as q,p as B}from"./chunk-EPGIQT2W.js";import"./chunk-DY4KE6AI.js";import{a as O,b as W,c as C}from"./chunk-GIGBYVJT.js";import"./chunk-KGEDUKSE.js";import"./chunk-MCRJI3T3.js";import{d as A,i as F}from"./chunk-IFNCDCK6.js";import{b as a}from"./chunk-QVY4QQUF.js";import{b as f,f as s,g as u,j as g,k as h,l as S}from"./chunk-2HRRFJKF.js";import{d as L,k as y,l as P}from"./chunk-UYQ7EZNZ.js";import{e as k}from"./chunk-BAKMWPBW.js";import"./chunk-OBXDPQ3V.js";import{g as p}from"./chunk-2R6CW7ES.js";var X=":host{--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--placeholder-color:currentColor;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);--background:transparent;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;min-height:44px;font-family:var(--ion-font-family, inherit);white-space:nowrap;cursor:pointer;z-index:2}:host(.select-label-placement-floating),:host(.select-label-placement-stacked){min-height:56px}:host(.ion-color){--highlight-color-focused:var(--ion-color-base)}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0}:host(.select-disabled){pointer-events:none}:host(.has-focus) button{border:2px solid #5e9ed6}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}.select-placeholder{color:var(--placeholder-color);opacity:var(--placeholder-opacity)}button{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.select-icon{-webkit-margin-start:4px;margin-inline-start:4px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0;position:relative;-ms-flex-negative:0;flex-shrink:0}:host(.in-item-color) .select-icon{color:inherit}:host(.select-label-placement-stacked) .select-icon,:host(.select-label-placement-floating) .select-icon{position:absolute;height:100%}:host(.select-ltr.select-label-placement-stacked) .select-icon,:host(.select-ltr.select-label-placement-floating) .select-icon{right:var(--padding-end, 0)}:host(.select-rtl.select-label-placement-stacked) .select-icon,:host(.select-rtl.select-label-placement-floating) .select-icon{left:var(--padding-start, 0)}.select-text{-ms-flex:1;flex:1;min-width:16px;font-size:inherit;text-overflow:ellipsis;white-space:inherit;overflow:hidden}.select-wrapper{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal;cursor:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}.select-wrapper .select-placeholder{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.select-wrapper-inner{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;overflow:hidden}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{-ms-flex-positive:1;flex-grow:1}:host(.ion-touched.ion-invalid){--highlight-color:var(--highlight-color-invalid)}:host(.ion-valid){--highlight-color:var(--highlight-color-valid)}.select-bottom{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:5px;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;border-top:var(--border-width) var(--border-style) var(--border-color);font-size:0.75rem;white-space:normal}:host(.has-focus.ion-valid),:host(.select-expanded.ion-valid),:host(.ion-touched.ion-invalid),:host(.select-expanded.ion-touched.ion-invalid){--border-color:var(--highlight-color)}.select-bottom .error-text{display:none;color:var(--highlight-color-invalid)}.select-bottom .helper-text{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}:host(.ion-touched.ion-invalid) .select-bottom .error-text{display:block}:host(.ion-touched.ion-invalid) .select-bottom .helper-text{display:none}.label-text-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text,::slotted([slot=label]){text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden,.select-outline-notch-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);overflow:hidden}:host(.select-justify-space-between) .select-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.select-justify-start) .select-wrapper{-ms-flex-pack:start;justify-content:start}:host(.select-justify-end) .select-wrapper{-ms-flex-pack:end;justify-content:end}:host(.select-label-placement-start) .select-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.select-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-end) .select-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.select-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.select-label-placement-stacked) .select-wrapper,:host(.select-label-placement-floating) .select-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}:host(.select-label-placement-stacked) .label-text-wrapper,:host(.select-label-placement-floating) .label-text-wrapper{max-width:100%}:host(.select-ltr.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-label-placement-stacked) .native-wrapper,:host(.select-label-placement-floating) .native-wrapper{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0;-ms-flex-positive:1;flex-grow:1;width:100%}:host(.select-label-placement-floating) .label-text-wrapper{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}:host(.select-label-placement-floating:not(.label-floating)) .native-wrapper .select-placeholder{opacity:0}:host(.select-expanded.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.has-focus.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.has-value.select-label-placement-floating) .native-wrapper .select-placeholder{opacity:1}:host(.label-floating) .label-text-wrapper{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}::slotted([slot=start]),::slotted([slot=end]){-ms-flex-negative:0;flex-shrink:0}::slotted([slot=start]:last-of-type){-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}::slotted([slot=end]:first-of-type){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host{--border-width:0.55px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))));--highlight-height:0px}.select-icon{width:1.125rem;height:1.125rem;color:var(--ion-color-step-650, var(--ion-text-color-step-350, #595959))}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{width:calc(100% - 1.125rem - 4px)}:host(.select-disabled){opacity:0.3}::slotted(ion-button[slot=start].button-has-icon-only),::slotted(ion-button[slot=end].button-has-icon-only){--border-radius:50%;--padding-start:0;--padding-end:0;--padding-top:0;--padding-bottom:0;aspect-ratio:1}",Z=X,ee=":host{--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--placeholder-color:currentColor;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);--background:transparent;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;min-height:44px;font-family:var(--ion-font-family, inherit);white-space:nowrap;cursor:pointer;z-index:2}:host(.select-label-placement-floating),:host(.select-label-placement-stacked){min-height:56px}:host(.ion-color){--highlight-color-focused:var(--ion-color-base)}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0}:host(.select-disabled){pointer-events:none}:host(.has-focus) button{border:2px solid #5e9ed6}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}.select-placeholder{color:var(--placeholder-color);opacity:var(--placeholder-opacity)}button{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.select-icon{-webkit-margin-start:4px;margin-inline-start:4px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0;position:relative;-ms-flex-negative:0;flex-shrink:0}:host(.in-item-color) .select-icon{color:inherit}:host(.select-label-placement-stacked) .select-icon,:host(.select-label-placement-floating) .select-icon{position:absolute;height:100%}:host(.select-ltr.select-label-placement-stacked) .select-icon,:host(.select-ltr.select-label-placement-floating) .select-icon{right:var(--padding-end, 0)}:host(.select-rtl.select-label-placement-stacked) .select-icon,:host(.select-rtl.select-label-placement-floating) .select-icon{left:var(--padding-start, 0)}.select-text{-ms-flex:1;flex:1;min-width:16px;font-size:inherit;text-overflow:ellipsis;white-space:inherit;overflow:hidden}.select-wrapper{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal;cursor:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}.select-wrapper .select-placeholder{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.select-wrapper-inner{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;overflow:hidden}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{-ms-flex-positive:1;flex-grow:1}:host(.ion-touched.ion-invalid){--highlight-color:var(--highlight-color-invalid)}:host(.ion-valid){--highlight-color:var(--highlight-color-valid)}.select-bottom{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:5px;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;border-top:var(--border-width) var(--border-style) var(--border-color);font-size:0.75rem;white-space:normal}:host(.has-focus.ion-valid),:host(.select-expanded.ion-valid),:host(.ion-touched.ion-invalid),:host(.select-expanded.ion-touched.ion-invalid){--border-color:var(--highlight-color)}.select-bottom .error-text{display:none;color:var(--highlight-color-invalid)}.select-bottom .helper-text{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}:host(.ion-touched.ion-invalid) .select-bottom .error-text{display:block}:host(.ion-touched.ion-invalid) .select-bottom .helper-text{display:none}.label-text-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text,::slotted([slot=label]){text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden,.select-outline-notch-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);overflow:hidden}:host(.select-justify-space-between) .select-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.select-justify-start) .select-wrapper{-ms-flex-pack:start;justify-content:start}:host(.select-justify-end) .select-wrapper{-ms-flex-pack:end;justify-content:end}:host(.select-label-placement-start) .select-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.select-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-end) .select-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.select-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.select-label-placement-stacked) .select-wrapper,:host(.select-label-placement-floating) .select-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}:host(.select-label-placement-stacked) .label-text-wrapper,:host(.select-label-placement-floating) .label-text-wrapper{max-width:100%}:host(.select-ltr.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-label-placement-stacked) .native-wrapper,:host(.select-label-placement-floating) .native-wrapper{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0;-ms-flex-positive:1;flex-grow:1;width:100%}:host(.select-label-placement-floating) .label-text-wrapper{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}:host(.select-label-placement-floating:not(.label-floating)) .native-wrapper .select-placeholder{opacity:0}:host(.select-expanded.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.has-focus.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.has-value.select-label-placement-floating) .native-wrapper .select-placeholder{opacity:1}:host(.label-floating) .label-text-wrapper{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}::slotted([slot=start]),::slotted([slot=end]){-ms-flex-negative:0;flex-shrink:0}::slotted([slot=start]:last-of-type){-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}::slotted([slot=end]:first-of-type){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.select-fill-solid){--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--border-color:var(--ion-color-step-500, var(--ion-background-color-step-500, gray));--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}:host(.select-fill-solid) .select-wrapper{border-bottom:var(--border-width) var(--border-style) var(--border-color)}:host(.select-expanded.select-fill-solid.ion-valid),:host(.has-focus.select-fill-solid.ion-valid),:host(.select-fill-solid.ion-touched.ion-invalid){--border-color:var(--highlight-color)}:host(.select-fill-solid) .select-bottom{border-top:none}@media (any-hover: hover){:host(.select-fill-solid:hover){--background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6));--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}}:host(.select-fill-solid.select-expanded),:host(.select-fill-solid.has-focus){--background:var(--ion-color-step-150, var(--ion-background-color-step-150, #d9d9d9));--border-color:var(--highlight-color)}:host(.select-fill-solid) .select-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0px;border-end-start-radius:0px}:host(.label-floating.select-fill-solid) .label-text-wrapper{max-width:calc(100% / 0.75)}:host(.in-item.select-expanded.select-fill-solid) .select-wrapper .select-icon,:host(.in-item.has-focus.select-fill-solid) .select-wrapper .select-icon,:host(.in-item.has-focus.ion-valid.select-fill-solid) .select-wrapper .select-icon,:host(.in-item.ion-touched.ion-invalid.select-fill-solid) .select-wrapper .select-icon{color:var(--highlight-color)}:host(.select-fill-outline){--border-color:var(--ion-color-step-300, var(--ion-background-color-step-300, #b3b3b3));--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}:host(.select-fill-outline.select-shape-round){--border-radius:28px;--padding-start:32px;--padding-end:32px}:host(.has-focus.select-fill-outline.ion-valid),:host(.select-fill-outline.ion-touched.ion-invalid){--border-color:var(--highlight-color)}@media (any-hover: hover){:host(.select-fill-outline:hover){--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}}:host(.select-fill-outline.select-expanded),:host(.select-fill-outline.has-focus){--border-width:var(--highlight-height);--border-color:var(--highlight-color)}:host(.select-fill-outline) .select-bottom{border-top:none}:host(.select-fill-outline) .select-wrapper{border-bottom:none}:host(.select-ltr.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-fill-outline.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-fill-outline.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-fill-outline.select-label-placement-floating) .label-text-wrapper{position:absolute;max-width:calc(100% - var(--padding-start) - var(--padding-end))}:host(.select-fill-outline) .label-text-wrapper{position:relative;z-index:1}:host(.label-floating.select-fill-outline) .label-text-wrapper{-webkit-transform:translateY(-32%) scale(0.75);transform:translateY(-32%) scale(0.75);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;max-width:calc((100% - var(--padding-start) - var(--padding-end) - 8px) / 0.75)}:host(.select-fill-outline.select-label-placement-stacked) select,:host(.select-fill-outline.select-label-placement-floating) select{margin-left:0;margin-right:0;margin-top:6px;margin-bottom:6px}:host(.select-fill-outline) .select-outline-container{left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;width:100%;height:100%}:host(.select-fill-outline) .select-outline-start,:host(.select-fill-outline) .select-outline-end{pointer-events:none}:host(.select-fill-outline) .select-outline-start,:host(.select-fill-outline) .select-outline-notch,:host(.select-fill-outline) .select-outline-end{border-top:var(--border-width) var(--border-style) var(--border-color);border-bottom:var(--border-width) var(--border-style) var(--border-color);-webkit-box-sizing:border-box;box-sizing:border-box}:host(.select-fill-outline) .select-outline-notch{max-width:calc(100% - var(--padding-start) - var(--padding-end))}:host(.select-fill-outline) .notch-spacer{-webkit-padding-end:8px;padding-inline-end:8px;font-size:calc(1em * 0.75);opacity:0;pointer-events:none}:host(.select-fill-outline) .select-outline-start{-webkit-border-start:var(--border-width) var(--border-style) var(--border-color);border-inline-start:var(--border-width) var(--border-style) var(--border-color)}:host(.select-fill-outline) .select-outline-start{border-start-start-radius:var(--border-radius);border-start-end-radius:0px;border-end-end-radius:0px;border-end-start-radius:var(--border-radius)}:host(.select-fill-outline) .select-outline-start{width:calc(var(--padding-start) - 4px)}:host(.select-fill-outline) .select-outline-end{-webkit-border-end:var(--border-width) var(--border-style) var(--border-color);border-inline-end:var(--border-width) var(--border-style) var(--border-color)}:host(.select-fill-outline) .select-outline-end{border-start-start-radius:0px;border-start-end-radius:var(--border-radius);border-end-end-radius:var(--border-radius);border-end-start-radius:0px}:host(.select-fill-outline) .select-outline-end{-ms-flex-positive:1;flex-grow:1}:host(.label-floating.select-fill-outline) .select-outline-notch{border-top:none}:host(.in-item.select-expanded.select-fill-outline) .select-wrapper .select-icon,:host(.in-item.has-focus.select-fill-outline) .select-wrapper .select-icon,:host(.in-item.has-focus.ion-valid.select-fill-outline) .select-wrapper .select-icon,:host(.in-item.ion-touched.ion-invalid.select-fill-outline) .select-wrapper .select-icon{color:var(--highlight-color)}:host{--border-width:1px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));--highlight-height:2px}:host(.select-label-placement-floating.select-expanded) .label-text-wrapper,:host(.select-label-placement-floating.has-focus) .label-text-wrapper,:host(.select-label-placement-stacked.select-expanded) .label-text-wrapper,:host(.select-label-placement-stacked.has-focus) .label-text-wrapper{color:var(--highlight-color)}:host(.has-focus.select-label-placement-floating.ion-valid) .label-text-wrapper,:host(.select-label-placement-floating.ion-touched.ion-invalid) .label-text-wrapper,:host(.has-focus.select-label-placement-stacked.ion-valid) .label-text-wrapper,:host(.select-label-placement-stacked.ion-touched.ion-invalid) .label-text-wrapper{color:var(--highlight-color)}.select-highlight{bottom:-1px;position:absolute;width:100%;height:var(--highlight-height);-webkit-transform:scale(0);transform:scale(0);-webkit-transition:-webkit-transform 200ms;transition:-webkit-transform 200ms;transition:transform 200ms;transition:transform 200ms, -webkit-transform 200ms;background:var(--highlight-color)}.select-highlight{inset-inline-start:0}:host(.select-expanded) .select-highlight,:host(.has-focus) .select-highlight{-webkit-transform:scale(1);transform:scale(1)}:host(.in-item) .select-highlight{bottom:0}:host(.in-item) .select-highlight{inset-inline-start:0}.select-icon{width:0.8125rem;-webkit-transition:-webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:-webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:transform 0.15s cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);color:var(--ion-color-step-500, var(--ion-text-color-step-500, gray))}:host(.select-expanded:not(.has-expanded-icon)) .select-icon{-webkit-transform:rotate(180deg);transform:rotate(180deg)}:host(.in-item.select-expanded) .select-wrapper .select-icon,:host(.in-item.has-focus) .select-wrapper .select-icon,:host(.in-item.has-focus.ion-valid) .select-wrapper .select-icon,:host(.in-item.ion-touched.ion-invalid) .select-wrapper .select-icon{color:var(--ion-color-step-500, var(--ion-text-color-step-500, gray))}:host(.select-expanded) .select-wrapper .select-icon,:host(.has-focus.ion-valid) .select-wrapper .select-icon,:host(.ion-touched.ion-invalid) .select-wrapper .select-icon,:host(.has-focus) .select-wrapper .select-icon{color:var(--highlight-color)}:host(.select-shape-round){--border-radius:16px}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{width:calc(100% - 0.8125rem - 4px)}:host(.select-disabled){opacity:0.38}::slotted(ion-button[slot=start].button-has-icon-only),::slotted(ion-button[slot=end].button-has-icon-only){--border-radius:50%;--padding-start:8px;--padding-end:8px;--padding-top:8px;--padding-bottom:8px;aspect-ratio:1;min-height:40px}",te=ee,ie=class{constructor(t){f(this,t),this.ionChange=h(this,"ionChange",7),this.ionCancel=h(this,"ionCancel",7),this.ionDismiss=h(this,"ionDismiss",7),this.ionFocus=h(this,"ionFocus",7),this.ionBlur=h(this,"ionBlur",7),this.ionStyle=h(this,"ionStyle",7),this.inputId=`ion-sel-${se++}`,this.helperTextId=`${this.inputId}-helper-text`,this.errorTextId=`${this.inputId}-error-text`,this.inheritedAttributes={},this.onClick=e=>{let i=e.target,l=i.closest('[slot="start"], [slot="end"]');i===this.el||l===null?(this.setFocus(),this.open(e)):e.preventDefault()},this.onFocus=()=>{this.hasFocus=!0,this.ionFocus.emit()},this.onBlur=()=>{this.hasFocus=!1,this.ionBlur.emit()},this.isExpanded=!1,this.hasFocus=!1,this.cancelText="Cancel",this.color=void 0,this.compareWith=void 0,this.disabled=!1,this.fill=void 0,this.errorText=void 0,this.helperText=void 0,this.interface="alert",this.interfaceOptions={},this.justify=void 0,this.label=void 0,this.labelPlacement="start",this.multiple=!1,this.name=this.inputId,this.okText="OK",this.placeholder=void 0,this.selectedText=void 0,this.toggleIcon=void 0,this.expandedIcon=void 0,this.shape=void 0,this.value=void 0,this.required=!1}styleChanged(){this.emitStyle()}setValue(t){this.value=t,this.ionChange.emit({value:t})}connectedCallback(){return p(this,null,function*(){let{el:t}=this;this.notchController=M(t,()=>this.notchSpacerEl,()=>this.labelSlot),this.updateOverlayOptions(),this.emitStyle(),this.mutationO=Y(this.el,"ion-select-option",()=>p(this,null,function*(){this.updateOverlayOptions(),S(this)}))})}componentWillLoad(){this.inheritedAttributes=L(this.el,["aria-label"])}componentDidLoad(){this.emitStyle()}disconnectedCallback(){this.mutationO&&(this.mutationO.disconnect(),this.mutationO=void 0),this.notchController&&(this.notchController.destroy(),this.notchController=void 0)}open(t){return p(this,null,function*(){if(this.disabled||this.isExpanded)return;this.isExpanded=!0;let e=this.overlay=yield this.createOverlay(t),i=()=>{let l=this.childOpts.findIndex(o=>o.value===this.value);if(l>-1){let o=e.querySelector(`.select-interface-option:nth-child(${l+1})`);if(o){let r=o.querySelector("ion-radio, ion-checkbox");r&&(o.scrollIntoView({block:"nearest"}),r.setFocus()),y(o)}}else{let o=e.querySelector("ion-radio:not(.radio-disabled), ion-checkbox:not(.checkbox-disabled)");o&&(o.setFocus(),y(o.closest("ion-item")))}};if(this.interface==="modal")e.addEventListener("ionModalWillPresent",i,{once:!0});else if(this.interface==="popover")e.addEventListener("ionPopoverWillPresent",i,{once:!0});else{let l=()=>{requestAnimationFrame(()=>{i()})};this.interface==="alert"?e.addEventListener("ionAlertWillPresent",l,{once:!0}):this.interface==="action-sheet"&&e.addEventListener("ionActionSheetWillPresent",l,{once:!0})}return e.onDidDismiss().then(()=>{this.overlay=void 0,this.isExpanded=!1,this.ionDismiss.emit(),this.setFocus()}),yield e.present(),e})}createOverlay(t){let e=this.interface;return e==="action-sheet"&&this.multiple&&(k(`[ion-select] - Interface cannot be "${e}" with a multi-value select. Using the "alert" interface instead.`),e="alert"),e==="popover"&&!t&&(k(`[ion-select] - Interface cannot be a "${e}" without passing an event. Using the "alert" interface instead.`),e="alert"),e==="action-sheet"?this.openActionSheet():e==="popover"?this.openPopover(t):e==="modal"?this.openModal():this.openAlert()}updateOverlayOptions(){let t=this.overlay;if(!t)return;let e=this.childOpts,i=this.value;switch(this.interface){case"action-sheet":t.buttons=this.createActionSheetButtons(e,i);break;case"popover":let l=t.querySelector("ion-select-popover");l&&(l.options=this.createOverlaySelectOptions(e,i));break;case"modal":let o=t.querySelector("ion-select-modal");o&&(o.options=this.createOverlaySelectOptions(e,i));break;case"alert":let r=this.multiple?"checkbox":"radio";t.inputs=this.createAlertInputs(e,r,i);break}}createActionSheetButtons(t,e){let i=t.map(l=>{let o=x(l),r=Array.from(l.classList).filter(n=>n!=="hydrated").join(" "),c=`${I} ${r}`;return{role:m(e,o,this.compareWith)?"selected":"",text:l.textContent,cssClass:c,handler:()=>{this.setValue(o)}}});return i.push({text:this.cancelText,role:"cancel",handler:()=>{this.ionCancel.emit()}}),i}createAlertInputs(t,e,i){return t.map(o=>{let r=x(o),c=Array.from(o.classList).filter(d=>d!=="hydrated").join(" "),n=`${I} ${c}`;return{type:e,cssClass:n,label:o.textContent||"",value:r,checked:m(i,r,this.compareWith),disabled:o.disabled}})}createOverlaySelectOptions(t,e){return t.map(l=>{let o=x(l),r=Array.from(l.classList).filter(n=>n!=="hydrated").join(" "),c=`${I} ${r}`;return{text:l.textContent||"",cssClass:c,value:o,checked:m(e,o,this.compareWith),disabled:l.disabled,handler:n=>{this.setValue(n),this.multiple||this.close()}}})}openPopover(t){return p(this,null,function*(){let{fill:e,labelPlacement:i}=this,l=this.interfaceOptions,o=a(this),r=o!=="md",c=this.multiple,n=this.value,d=t,b="auto";i==="floating"||i==="stacked"||o==="md"&&e!==void 0?b="cover":d=Object.assign(Object.assign({},t),{detail:{ionShadowTarget:this.nativeWrapperEl}});let v=Object.assign(Object.assign({mode:o,event:d,alignment:"center",size:b,showBackdrop:r},l),{component:"ion-select-popover",cssClass:["select-popover",l.cssClass],componentProps:{header:l.header,subHeader:l.subHeader,message:l.message,multiple:c,value:n,options:this.createOverlaySelectOptions(this.childOpts,n)}});return q.create(v)})}openActionSheet(){return p(this,null,function*(){let t=a(this),e=this.interfaceOptions,i=Object.assign(Object.assign({mode:t},e),{buttons:this.createActionSheetButtons(this.childOpts,this.value),cssClass:["select-action-sheet",e.cssClass]});return H.create(i)})}openAlert(){return p(this,null,function*(){let t=this.interfaceOptions,e=this.multiple?"checkbox":"radio",i=a(this),l=Object.assign(Object.assign({mode:i},t),{header:t.header?t.header:this.labelText,inputs:this.createAlertInputs(this.childOpts,e,this.value),buttons:[{text:this.cancelText,role:"cancel",handler:()=>{this.ionCancel.emit()}},{text:this.okText,handler:o=>{this.setValue(o)}}],cssClass:["select-alert",t.cssClass,this.multiple?"multiple-select-alert":"single-select-alert"]});return $.create(l)})}openModal(){let{multiple:t,value:e,interfaceOptions:i}=this,l=a(this),o=Object.assign(Object.assign({},i),{mode:l,cssClass:["select-modal",i.cssClass],component:"ion-select-modal",componentProps:{header:i.header,multiple:t,value:e,options:this.createOverlaySelectOptions(this.childOpts,e)}});return V.create(o)}close(){return this.overlay?this.overlay.dismiss():Promise.resolve(!1)}hasValue(){return this.getText()!==""}get childOpts(){return Array.from(this.el.querySelectorAll("ion-select-option"))}get labelText(){let{label:t}=this;if(t!==void 0)return t;let{labelSlot:e}=this;if(e!==null)return e.textContent}getText(){let t=this.selectedText;return t!=null&&t!==""?t:oe(this.childOpts,this.value,this.compareWith)}setFocus(){this.focusEl&&this.focusEl.focus()}emitStyle(){let{disabled:t}=this,e={"interactive-disabled":t};this.ionStyle.emit(e)}renderLabel(){let{label:t}=this;return s("div",{class:{"label-text-wrapper":!0,"label-text-wrapper-hidden":!this.hasLabel},part:"label"},t===void 0?s("slot",{name:"label"}):s("div",{class:"label-text"},t))}componentDidRender(){var t;(t=this.notchController)===null||t===void 0||t.calculateNotchWidth()}get labelSlot(){return this.el.querySelector('[slot="label"]')}get hasLabel(){return this.label!==void 0||this.labelSlot!==null}renderLabelContainer(){return a(this)==="md"&&this.fill==="outline"?[s("div",{class:"select-outline-container"},s("div",{class:"select-outline-start"}),s("div",{class:{"select-outline-notch":!0,"select-outline-notch-hidden":!this.hasLabel}},s("div",{class:"notch-spacer","aria-hidden":"true",ref:i=>this.notchSpacerEl=i},this.label)),s("div",{class:"select-outline-end"})),this.renderLabel()]:this.renderLabel()}renderSelectText(){let{placeholder:t}=this,e=this.getText(),i=!1,l=e;return l===""&&t!==void 0&&(l=t,i=!0),s("div",{"aria-hidden":"true",class:{"select-text":!0,"select-placeholder":i},part:i?"placeholder":"text"},l)}renderSelectIcon(){let t=a(this),{isExpanded:e,toggleIcon:i,expandedIcon:l}=this,o;return e&&l!==void 0?o=l:o=i??(t==="ios"?F:A),s("ion-icon",{class:"select-icon",part:"icon","aria-hidden":"true",icon:o})}get ariaLabel(){var t;let{placeholder:e,inheritedAttributes:i}=this,l=this.getText(),o=(t=i["aria-label"])!==null&&t!==void 0?t:this.labelText,r=l;return r===""&&e!==void 0&&(r=e),o!==void 0&&(r=r===""?o:`${o}, ${r}`),r}renderListbox(){let{disabled:t,inputId:e,isExpanded:i,required:l}=this;return s("button",{disabled:t,id:e,"aria-label":this.ariaLabel,"aria-haspopup":"dialog","aria-expanded":`${i}`,"aria-describedby":this.getHintTextID(),"aria-invalid":this.getHintTextID()===this.errorTextId,"aria-required":`${l}`,onFocus:this.onFocus,onBlur:this.onBlur,ref:o=>this.focusEl=o})}getHintTextID(){let{el:t,helperText:e,errorText:i,helperTextId:l,errorTextId:o}=this;if(t.classList.contains("ion-touched")&&t.classList.contains("ion-invalid")&&i)return o;if(e)return l}renderHintText(){let{helperText:t,errorText:e,helperTextId:i,errorTextId:l}=this;return[s("div",{id:i,class:"helper-text",part:"supporting-text helper-text"},t),s("div",{id:l,class:"error-text",part:"supporting-text error-text"},e)]}renderBottomContent(){let{helperText:t,errorText:e}=this;if(t||e)return s("div",{class:"select-bottom"},this.renderHintText())}render(){let{disabled:t,el:e,isExpanded:i,expandedIcon:l,labelPlacement:o,justify:r,placeholder:c,fill:n,shape:d,name:b,value:z,hasFocus:v}=this,T=a(this),w=o==="floating"||o==="stacked",U=!w&&r!==void 0,N=D(e)?"rtl":"ltr",j=O("ion-item",this.el),K=T==="md"&&n!=="outline"&&!j,E=this.hasValue(),G=e.querySelector('[slot="start"], [slot="end"]')!==null;P(!0,e,b,le(z),t);let J=o==="stacked"||o==="floating"&&(E||i||G);return s(u,{key:"e6c0498d6c275f89344f4b5146752a047058ad88",onClick:this.onClick,class:W(this.color,{[T]:!0,"in-item":j,"in-item-color":O("ion-item.ion-color",e),"select-disabled":t,"select-expanded":i,"has-expanded-icon":l!==void 0,"has-value":E,"label-floating":J,"has-placeholder":c!==void 0,"has-focus":v,"ion-focusable":!0,[`select-${N}`]:!0,[`select-fill-${n}`]:n!==void 0,[`select-justify-${r}`]:U,[`select-shape-${d}`]:d!==void 0,[`select-label-placement-${o}`]:!0})},s("label",{key:"f030b6bd329f8014c7227f5e5f1aeb7efa0e641a",class:"select-wrapper",id:"select-label"},this.renderLabelContainer(),s("div",{key:"7480e1b40d09e53a2942295d6c9dae474c9de810",class:"select-wrapper-inner"},s("slot",{key:"250fd2ff08b3e6ed04c7062455a044863481fe1c",name:"start"}),s("div",{key:"11b73ad5b7decfe2d307f9d54293c21c0df3ddb8",class:"native-wrapper",ref:Q=>this.nativeWrapperEl=Q,part:"container"},this.renderSelectText(),this.renderListbox()),s("slot",{key:"ddedafc89061372567bd46354ef972f08c60e19d",name:"end"}),!w&&this.renderSelectIcon()),w&&this.renderSelectIcon(),K&&s("div",{key:"792ce27aea18a0020c17dceb0f0e293171ded3a3",class:"select-highlight"})),this.renderBottomContent())}get el(){return g(this)}static get watchers(){return{disabled:["styleChanged"],isExpanded:["styleChanged"],placeholder:["styleChanged"],value:["styleChanged"]}}},x=t=>{let e=t.value;return e===void 0?t.textContent||"":e},le=t=>{if(t!=null)return Array.isArray(t)?t.join(","):t.toString()},oe=(t,e,i)=>e===void 0?"":Array.isArray(e)?e.map(l=>R(t,l,i)).filter(l=>l!==null).join(", "):R(t,e,i)||"",R=(t,e,i)=>{let l=t.find(o=>_(e,x(o),i));return l?l.textContent:null},se=0,I="select-interface-option";ie.style={ios:Z,md:te};var re=":host{display:none}",ne=re,ae=class{constructor(t){f(this,t),this.inputId=`ion-selopt-${ce++}`,this.disabled=!1,this.value=void 0}render(){return s(u,{key:"8c96c199ce3a3065de3fe446500f567236e0610a",role:"option",id:this.inputId,class:a(this)})}get el(){return g(this)}},ce=0;ae.style=ne;var de=".sc-ion-select-popover-ios-h ion-list.sc-ion-select-popover-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-list-header.sc-ion-select-popover-ios,ion-label.sc-ion-select-popover-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-select-popover-ios-h{overflow-y:auto}",pe=de,he=".sc-ion-select-popover-md-h ion-list.sc-ion-select-popover-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-list-header.sc-ion-select-popover-md,ion-label.sc-ion-select-popover-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-select-popover-md-h{overflow-y:auto}ion-list.sc-ion-select-popover-md ion-radio.sc-ion-select-popover-md::part(container){display:none}ion-list.sc-ion-select-popover-md ion-radio.sc-ion-select-popover-md::part(label){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-item.sc-ion-select-popover-md{--inner-border-width:0}.item-radio-checked.sc-ion-select-popover-md{--background:rgba(var(--ion-color-primary-rgb, 0, 84, 233), 0.08);--background-focused:var(--ion-color-primary, #0054e9);--background-focused-opacity:0.2;--background-hover:var(--ion-color-primary, #0054e9);--background-hover-opacity:0.12}.item-checkbox-checked.sc-ion-select-popover-md{--background-activated:var(--ion-item-color, var(--ion-text-color, #000));--background-focused:var(--ion-item-color, var(--ion-text-color, #000));--background-hover:var(--ion-item-color, var(--ion-text-color, #000));--color:var(--ion-color-primary, #0054e9)}",be=he,je=(()=>{let t=class{constructor(e){f(this,e),this.header=void 0,this.subHeader=void 0,this.message=void 0,this.multiple=void 0,this.options=[]}findOptionFromEvent(e){let{options:i}=this;return i.find(l=>l.value===e.target.value)}callOptionHandler(e){let i=this.findOptionFromEvent(e),l=this.getValues(e);i?.handler&&B(i.handler,l)}dismissParentPopover(){let e=this.el.closest("ion-popover");e&&e.dismiss()}setChecked(e){let{multiple:i}=this,l=this.findOptionFromEvent(e);i&&l&&(l.checked=e.detail.checked)}getValues(e){let{multiple:i,options:l}=this;if(i)return l.filter(r=>r.checked).map(r=>r.value);let o=this.findOptionFromEvent(e);return o?o.value:void 0}renderOptions(e){let{multiple:i}=this;switch(i){case!0:return this.renderCheckboxOptions(e);default:return this.renderRadioOptions(e)}}renderCheckboxOptions(e){return e.map(i=>s("ion-item",{class:Object.assign({"item-checkbox-checked":i.checked},C(i.cssClass))},s("ion-checkbox",{value:i.value,disabled:i.disabled,checked:i.checked,justify:"start",labelPlacement:"end",onIonChange:l=>{this.setChecked(l),this.callOptionHandler(l),S(this)}},i.text)))}renderRadioOptions(e){let i=e.filter(l=>l.checked).map(l=>l.value)[0];return s("ion-radio-group",{value:i,onIonChange:l=>this.callOptionHandler(l)},e.map(l=>s("ion-item",{class:Object.assign({"item-radio-checked":l.value===i},C(l.cssClass))},s("ion-radio",{value:l.value,disabled:l.disabled,onClick:()=>this.dismissParentPopover(),onKeyUp:o=>{o.key===" "&&this.dismissParentPopover()}},l.text))))}render(){let{header:e,message:i,options:l,subHeader:o}=this,r=o!==void 0||i!==void 0;return s(u,{key:"542367ab8fb72bfebf7e65630b91017d68827fd6",class:a(this)},s("ion-list",{key:"f2f0f37e1365cd7780b02de1a1698700d0df48a7"},e!==void 0&&s("ion-list-header",{key:"4b8800a68e800f19277a44b7074ca24b70218daf"},e),r&&s("ion-item",{key:"932b7903daf97d5a57d289b7ee49e868bb9b0cf5"},s("ion-label",{key:"fc3f1b69aa2a0bc6125d35692dcad3a8a99fd160",class:"ion-text-wrap"},o!==void 0&&s("h3",{key:"eceab2f47afa95f04b138342b0bdbfa1f50919a8"},o),i!==void 0&&s("p",{key:"70f4e27ad1316318efd0c17efce31e5e45c8fa02"},i))),this.renderOptions(l)))}get el(){return g(this)}};return t.style={ios:pe,md:be},t})();export{ie as ion_select,ae as ion_select_option,je as ion_select_popover};
