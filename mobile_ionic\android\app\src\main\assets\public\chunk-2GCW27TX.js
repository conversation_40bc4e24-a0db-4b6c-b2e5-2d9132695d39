import{A as $e,B as _e,C as qe,D as Ne,E as Ke,F as Ue,G as Ze,H as Ge,I as Xe,J as Je,K as Qe,L as et,M as ie,N as tt,O as it,P as at,Q as nt,R as ot,a as H,b as R,c as $,d as Me,e as Ae,f as Oe,g as ee,h as Se,i as D,j as Ie,k as te,l as Ve,m as Te,n as je,o as We,p as S,q as Fe,r as ze,s as Le,t as Ee,u as Re,v as Ye,w as _,x as q,y as Be,z as He}from"./chunk-ZW2X6MKF.js";import{a as W}from"./chunk-H2PHEXKY.js";import{c as pe,d as me,e as ue}from"./chunk-UJQTAKBF.js";import{a as ge}from"./chunk-2IVBBB3H.js";import"./chunk-WMEG6PAA.js";import{a as De}from"./chunk-GJZKZXL4.js";import{i as fe,j as be,l as ye,m as xe,n as J,o as Q,p as ke,q as ve,t as we,u as Pe}from"./chunk-EPGIQT2W.js";import{a as O}from"./chunk-RS5W3JWO.js";import"./chunk-DY4KE6AI.js";import{b as Ce,c as B}from"./chunk-GIGBYVJT.js";import"./chunk-KGEDUKSE.js";import"./chunk-MCRJI3T3.js";import{d as le,e as de,g as ce,h as he,j as X}from"./chunk-IFNCDCK6.js";import{b as j}from"./chunk-QVY4QQUF.js";import{b as N,d as Y,f as o,g as K,j as U,k as x}from"./chunk-2HRRFJKF.js";import{h as re,i as E,l as se,m as G}from"./chunk-UYQ7EZNZ.js";import{e as C,f as T}from"./chunk-BAKMWPBW.js";import"./chunk-OBXDPQ3V.js";import{g as P}from"./chunk-2R6CW7ES.js";var xt=(t,e,i)=>!!(e&&e.year>t||i&&i.year<t),pt=(t,e,i,a)=>!!(t.day===null||a!==void 0&&!a.includes(t.day)||e&&R(t,e)||i&&$(t,i)),kt=(t,e,i,a,n,s,r)=>{let d=(Array.isArray(i)?i:[i]).find(f=>H(e,f))!==void 0,c=H(e,a);return{disabled:pt(e,n,s,r),isActive:d,isToday:c,ariaSelected:d?"true":null,ariaLabel:Ke(t,c,e),text:e.day!=null?Ze(t,e):null}},Z=(t,{minParts:e,maxParts:i})=>!!(xt(t.year,e,i)||e&&R(t,e)||i&&$(t,i)),vt=(t,e,i)=>{let a=Object.assign(Object.assign({},_(t)),{day:null});return Z(a,{minParts:e,maxParts:i})},wt=(t,e)=>{let i=Object.assign(Object.assign({},q(t)),{day:null});return Z(i,{maxParts:e})},Pt=(t,e,i)=>{if(Array.isArray(t)){let a=e.split("T")[0],n=t.find(s=>s.date===a);if(n)return{textColor:n.textColor,backgroundColor:n.backgroundColor}}else try{return t(e)}catch(a){T("[ion-datetime] - Exception thrown from provided `highlightedDates` callback. Please check your function and try again.",i,a)}},rt=(t,e)=>{var i,a,n,s;(!((i=e?.date)===null||i===void 0)&&i.timeZone||!((a=e?.date)===null||a===void 0)&&a.timeZoneName||!((n=e?.time)===null||n===void 0)&&n.timeZone||!((s=e?.time)===null||s===void 0)&&s.timeZoneName)&&C('[ion-datetime] - "timeZone" and "timeZoneName" are not supported in "formatOptions".',t)},ae=(t,e,i)=>{if(i)switch(e){case"date":case"month-year":case"month":case"year":i.date===void 0&&C(`[ion-datetime] - The '${e}' presentation requires a date object in formatOptions.`,t);break;case"time":i.time===void 0&&C("[ion-datetime] - The 'time' presentation requires a time object in formatOptions.",t);break;case"date-time":case"time-date":i.date===void 0&&i.time===void 0&&C(`[ion-datetime] - The '${e}' presentation requires either a date or time object (or both) in formatOptions.`,t);break}},Ct=':host{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;background:var(--background);overflow:hidden}:host(.datetime-size-fixed){width:auto;height:auto}:host(.datetime-size-fixed:not(.datetime-prefer-wheel)){max-width:350px}:host(.datetime-size-fixed.datetime-prefer-wheel){min-width:350px;max-width:-webkit-max-content;max-width:-moz-max-content;max-width:max-content}:host(.datetime-size-cover){width:100%}:host .calendar-body,:host .datetime-year{opacity:0}:host(:not(.datetime-ready)) .datetime-year{position:absolute;pointer-events:none}:host(.datetime-ready) .calendar-body{opacity:1}:host(.datetime-ready) .datetime-year{display:none;opacity:1}:host .wheel-order-year-first .day-column{-ms-flex-order:3;order:3;text-align:end}:host .wheel-order-year-first .month-column{-ms-flex-order:2;order:2;text-align:end}:host .wheel-order-year-first .year-column{-ms-flex-order:1;order:1;text-align:start}:host .datetime-calendar,:host .datetime-year{display:-ms-flexbox;display:flex;-ms-flex:1 1 auto;flex:1 1 auto;-ms-flex-flow:column;flex-flow:column}:host(.show-month-and-year) .datetime-year{display:-ms-flexbox;display:flex}:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{display:none}:host(.month-year-picker-open) .datetime-footer{display:none}:host(.datetime-disabled){pointer-events:none}:host(.datetime-disabled) .calendar-days-of-week,:host(.datetime-disabled) .datetime-time{opacity:0.4}:host(.datetime-readonly){pointer-events:none;}:host(.datetime-readonly) .calendar-action-buttons,:host(.datetime-readonly) .calendar-body,:host(.datetime-readonly) .datetime-year{pointer-events:initial}:host(.datetime-readonly) .calendar-day[disabled]:not(.calendar-day-constrained),:host(.datetime-readonly) .datetime-action-buttons ion-button[disabled]{opacity:1}:host .datetime-header .datetime-title{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host .datetime-action-buttons.has-clear-button{width:100%}:host .datetime-action-buttons ion-buttons{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}.datetime-action-buttons .datetime-action-buttons-container{display:-ms-flexbox;display:flex}:host .calendar-action-buttons{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host .calendar-action-buttons ion-button{--background:transparent}:host .calendar-days-of-week{display:grid;grid-template-columns:repeat(7, 1fr);text-align:center}.calendar-days-of-week .day-of-week{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0}:host .calendar-body{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-webkit-scroll-snap-type:x mandatory;-ms-scroll-snap-type:x mandatory;scroll-snap-type:x mandatory;overflow-x:scroll;overflow-y:hidden;scrollbar-width:none;outline:none}:host .calendar-body .calendar-month{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;scroll-snap-align:start;scroll-snap-stop:always;-ms-flex-negative:0;flex-shrink:0;width:100%}:host .calendar-body .calendar-month-disabled{scroll-snap-align:none}:host .calendar-body::-webkit-scrollbar{display:none}:host .calendar-body .calendar-month-grid{display:grid;grid-template-columns:repeat(7, 1fr)}:host .calendar-day-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;min-width:0;min-height:0;overflow:visible}.calendar-day{border-radius:50%;-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:0px;padding-bottom:0px;-webkit-margin-start:0px;margin-inline-start:0px;-webkit-margin-end:0px;margin-inline-end:0px;margin-top:0px;margin-bottom:0px;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;border:none;outline:none;background:none;color:currentColor;font-family:var(--ion-font-family, inherit);cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;z-index:0}:host .calendar-day[disabled]{pointer-events:none;opacity:0.4}.calendar-day:focus{background:rgba(var(--ion-color-base-rgb), 0.2);-webkit-box-shadow:0px 0px 0px 4px rgba(var(--ion-color-base-rgb), 0.2);box-shadow:0px 0px 0px 4px rgba(var(--ion-color-base-rgb), 0.2)}:host .datetime-time{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host(.datetime-presentation-time) .datetime-time{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0}:host ion-popover{--height:200px}:host .time-header{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}:host .time-body{border-radius:8px;-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:6px;padding-bottom:6px;display:-ms-flexbox;display:flex;border:none;background:var(--ion-color-step-300, var(--ion-background-color-step-300, #edeef0));color:var(--ion-text-color, #000);font-family:inherit;font-size:inherit;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}:host .time-body-active{color:var(--ion-color-base)}:host(.in-item){position:static}:host(.show-month-and-year) .calendar-action-buttons .calendar-month-year-toggle{color:var(--ion-color-base)}.calendar-month-year{min-width:0}.calendar-month-year-toggle{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;position:relative;border:0;outline:none;background:transparent;cursor:pointer;z-index:1}.calendar-month-year-toggle::after{left:0;right:0;top:0;bottom:0;position:absolute;content:"";opacity:0;-webkit-transition:opacity 15ms linear, background-color 15ms linear;transition:opacity 15ms linear, background-color 15ms linear;z-index:-1}.calendar-month-year-toggle.ion-focused::after{background:currentColor}.calendar-month-year-toggle:disabled{opacity:0.3;pointer-events:none}.calendar-month-year-toggle ion-icon{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:0;padding-inline-end:0;padding-top:0;padding-bottom:0;-ms-flex-negative:0;flex-shrink:0}.calendar-month-year-toggle #toggle-wrapper{display:-ms-inline-flexbox;display:inline-flex;-ms-flex-align:center;align-items:center}ion-picker{--highlight-background:var(--wheel-highlight-background);--highlight-border-radius:var(--wheel-highlight-border-radius);--fade-background-rgb:var(--wheel-fade-background-rgb)}:host{--background:var(--ion-color-light, #f4f5f8);--background-rgb:var(--ion-color-light-rgb, 244, 245, 248);--title-color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666))}:host(.datetime-presentation-date-time:not(.datetime-prefer-wheel)),:host(.datetime-presentation-time-date:not(.datetime-prefer-wheel)),:host(.datetime-presentation-date:not(.datetime-prefer-wheel)){min-height:350px}:host .datetime-header{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:16px;padding-bottom:16px;border-bottom:0.55px solid var(--ion-color-step-200, var(--ion-background-color-step-200, #cccccc));font-size:min(0.875rem, 22.4px)}:host .datetime-header .datetime-title{color:var(--title-color)}:host .datetime-header .datetime-selected-date{margin-top:10px}.calendar-month-year-toggle{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:0px;padding-bottom:0px;min-height:44px;font-size:min(1rem, 25.6px);font-weight:600}.calendar-month-year-toggle.ion-focused::after{opacity:0.15}.calendar-month-year-toggle #toggle-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:8px;margin-inline-end:8px;margin-top:10px;margin-bottom:10px}:host .calendar-action-buttons .calendar-month-year-toggle ion-icon,:host .calendar-action-buttons ion-buttons ion-button{color:var(--ion-color-base)}:host .calendar-action-buttons ion-buttons{padding-left:0;padding-right:0;padding-top:8px;padding-bottom:0}:host .calendar-action-buttons ion-buttons ion-button{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}:host .calendar-days-of-week{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:0;padding-bottom:0;color:var(--ion-color-step-300, var(--ion-text-color-step-700, #b3b3b3));font-size:min(0.75rem, 19.2px);font-weight:600;line-height:24px;text-transform:uppercase}@supports (border-radius: mod(1px, 1px)){.calendar-days-of-week .day-of-week{width:clamp(20px, calc(mod(min(1rem, 24px), 24px) * 10), 100%);height:24px;overflow:hidden}.calendar-day{border-radius:max(8px, mod(min(1rem, 24px), 24px) * 10)}}@supports ((border-radius: mod(1px, 1px)) and (background: -webkit-named-image(apple-pay-logo-black)) and (not (contain-intrinsic-size: none))) or (not (border-radius: mod(1px, 1px))){.calendar-days-of-week .day-of-week{width:auto;height:auto;overflow:initial}.calendar-day{border-radius:32px}}:host .calendar-body .calendar-month .calendar-month-grid{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:8px;padding-bottom:8px;-ms-flex-align:center;align-items:center;height:calc(100% - 16px)}:host .calendar-day-wrapper{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;height:0;min-height:1rem}:host .calendar-day{width:40px;min-width:40px;height:40px;font-size:min(1.25rem, 32px)}.calendar-day.calendar-day-active{background:rgba(var(--ion-color-base-rgb), 0.2);font-size:min(1.375rem, 35.2px)}:host .calendar-day.calendar-day-today{color:var(--ion-color-base)}:host .calendar-day.calendar-day-active{color:var(--ion-color-base);font-weight:600}:host .calendar-day.calendar-day-today.calendar-day-active{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host .datetime-time{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:8px;padding-bottom:16px;font-size:min(1rem, 25.6px)}:host .datetime-time .time-header{font-weight:600}:host .datetime-buttons{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:8px;padding-bottom:8px;border-top:0.55px solid var(--ion-color-step-200, var(--ion-background-color-step-200, #cccccc))}:host .datetime-buttons ::slotted(ion-buttons),:host .datetime-buttons ion-buttons{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between}:host .datetime-action-buttons{width:100%}',Dt=Ct,Mt=':host{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;background:var(--background);overflow:hidden}:host(.datetime-size-fixed){width:auto;height:auto}:host(.datetime-size-fixed:not(.datetime-prefer-wheel)){max-width:350px}:host(.datetime-size-fixed.datetime-prefer-wheel){min-width:350px;max-width:-webkit-max-content;max-width:-moz-max-content;max-width:max-content}:host(.datetime-size-cover){width:100%}:host .calendar-body,:host .datetime-year{opacity:0}:host(:not(.datetime-ready)) .datetime-year{position:absolute;pointer-events:none}:host(.datetime-ready) .calendar-body{opacity:1}:host(.datetime-ready) .datetime-year{display:none;opacity:1}:host .wheel-order-year-first .day-column{-ms-flex-order:3;order:3;text-align:end}:host .wheel-order-year-first .month-column{-ms-flex-order:2;order:2;text-align:end}:host .wheel-order-year-first .year-column{-ms-flex-order:1;order:1;text-align:start}:host .datetime-calendar,:host .datetime-year{display:-ms-flexbox;display:flex;-ms-flex:1 1 auto;flex:1 1 auto;-ms-flex-flow:column;flex-flow:column}:host(.show-month-and-year) .datetime-year{display:-ms-flexbox;display:flex}:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{display:none}:host(.month-year-picker-open) .datetime-footer{display:none}:host(.datetime-disabled){pointer-events:none}:host(.datetime-disabled) .calendar-days-of-week,:host(.datetime-disabled) .datetime-time{opacity:0.4}:host(.datetime-readonly){pointer-events:none;}:host(.datetime-readonly) .calendar-action-buttons,:host(.datetime-readonly) .calendar-body,:host(.datetime-readonly) .datetime-year{pointer-events:initial}:host(.datetime-readonly) .calendar-day[disabled]:not(.calendar-day-constrained),:host(.datetime-readonly) .datetime-action-buttons ion-button[disabled]{opacity:1}:host .datetime-header .datetime-title{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host .datetime-action-buttons.has-clear-button{width:100%}:host .datetime-action-buttons ion-buttons{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}.datetime-action-buttons .datetime-action-buttons-container{display:-ms-flexbox;display:flex}:host .calendar-action-buttons{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host .calendar-action-buttons ion-button{--background:transparent}:host .calendar-days-of-week{display:grid;grid-template-columns:repeat(7, 1fr);text-align:center}.calendar-days-of-week .day-of-week{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0}:host .calendar-body{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-webkit-scroll-snap-type:x mandatory;-ms-scroll-snap-type:x mandatory;scroll-snap-type:x mandatory;overflow-x:scroll;overflow-y:hidden;scrollbar-width:none;outline:none}:host .calendar-body .calendar-month{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;scroll-snap-align:start;scroll-snap-stop:always;-ms-flex-negative:0;flex-shrink:0;width:100%}:host .calendar-body .calendar-month-disabled{scroll-snap-align:none}:host .calendar-body::-webkit-scrollbar{display:none}:host .calendar-body .calendar-month-grid{display:grid;grid-template-columns:repeat(7, 1fr)}:host .calendar-day-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;min-width:0;min-height:0;overflow:visible}.calendar-day{border-radius:50%;-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:0px;padding-bottom:0px;-webkit-margin-start:0px;margin-inline-start:0px;-webkit-margin-end:0px;margin-inline-end:0px;margin-top:0px;margin-bottom:0px;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;border:none;outline:none;background:none;color:currentColor;font-family:var(--ion-font-family, inherit);cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;z-index:0}:host .calendar-day[disabled]{pointer-events:none;opacity:0.4}.calendar-day:focus{background:rgba(var(--ion-color-base-rgb), 0.2);-webkit-box-shadow:0px 0px 0px 4px rgba(var(--ion-color-base-rgb), 0.2);box-shadow:0px 0px 0px 4px rgba(var(--ion-color-base-rgb), 0.2)}:host .datetime-time{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host(.datetime-presentation-time) .datetime-time{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0}:host ion-popover{--height:200px}:host .time-header{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}:host .time-body{border-radius:8px;-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:6px;padding-bottom:6px;display:-ms-flexbox;display:flex;border:none;background:var(--ion-color-step-300, var(--ion-background-color-step-300, #edeef0));color:var(--ion-text-color, #000);font-family:inherit;font-size:inherit;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}:host .time-body-active{color:var(--ion-color-base)}:host(.in-item){position:static}:host(.show-month-and-year) .calendar-action-buttons .calendar-month-year-toggle{color:var(--ion-color-base)}.calendar-month-year{min-width:0}.calendar-month-year-toggle{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;position:relative;border:0;outline:none;background:transparent;cursor:pointer;z-index:1}.calendar-month-year-toggle::after{left:0;right:0;top:0;bottom:0;position:absolute;content:"";opacity:0;-webkit-transition:opacity 15ms linear, background-color 15ms linear;transition:opacity 15ms linear, background-color 15ms linear;z-index:-1}.calendar-month-year-toggle.ion-focused::after{background:currentColor}.calendar-month-year-toggle:disabled{opacity:0.3;pointer-events:none}.calendar-month-year-toggle ion-icon{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:0;padding-inline-end:0;padding-top:0;padding-bottom:0;-ms-flex-negative:0;flex-shrink:0}.calendar-month-year-toggle #toggle-wrapper{display:-ms-inline-flexbox;display:inline-flex;-ms-flex-align:center;align-items:center}ion-picker{--highlight-background:var(--wheel-highlight-background);--highlight-border-radius:var(--wheel-highlight-border-radius);--fade-background-rgb:var(--wheel-fade-background-rgb)}:host{--background:var(--ion-color-step-100, var(--ion-background-color-step-100, #ffffff));--title-color:var(--ion-color-contrast)}:host .datetime-header{-webkit-padding-start:20px;padding-inline-start:20px;-webkit-padding-end:20px;padding-inline-end:20px;padding-top:20px;padding-bottom:20px;background:var(--ion-color-base);color:var(--title-color)}:host .datetime-header .datetime-title{font-size:0.75rem;text-transform:uppercase}:host .datetime-header .datetime-selected-date{margin-top:30px;font-size:2.125rem}:host .calendar-action-buttons ion-button{--color:var(--ion-color-step-650, var(--ion-text-color-step-350, #595959))}.calendar-month-year-toggle{-webkit-padding-start:20px;padding-inline-start:20px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:12px;padding-bottom:12px;min-height:48px;background:transparent;color:var(--ion-color-step-650, var(--ion-text-color-step-350, #595959));z-index:1}.calendar-month-year-toggle.ion-focused::after{opacity:0.04}.calendar-month-year-toggle ion-ripple-effect{color:currentColor}@media (any-hover: hover){.calendar-month-year-toggle.ion-activatable:not(.ion-focused):hover::after{background:currentColor;opacity:0.04}}:host .calendar-days-of-week{-webkit-padding-start:10px;padding-inline-start:10px;-webkit-padding-end:10px;padding-inline-end:10px;padding-top:0px;padding-bottom:0px;color:var(--ion-color-step-500, var(--ion-text-color-step-500, gray));font-size:0.875rem;line-height:36px}:host .calendar-body .calendar-month .calendar-month-grid{-webkit-padding-start:10px;padding-inline-start:10px;-webkit-padding-end:10px;padding-inline-end:10px;padding-top:4px;padding-bottom:4px;grid-template-rows:repeat(6, 1fr)}:host .calendar-day{width:42px;min-width:42px;height:42px;font-size:0.875rem}:host .calendar-day.calendar-day-today{border:1px solid var(--ion-color-base);color:var(--ion-color-base)}:host .calendar-day.calendar-day-active{color:var(--ion-color-contrast)}.calendar-day.calendar-day-active{border:1px solid var(--ion-color-base);background:var(--ion-color-base)}:host .datetime-time{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:8px;padding-bottom:8px}:host .time-header{color:var(--ion-color-step-650, var(--ion-text-color-step-350, #595959))}:host(.datetime-presentation-month) .datetime-year,:host(.datetime-presentation-year) .datetime-year,:host(.datetime-presentation-month-year) .datetime-year{margin-top:20px;margin-bottom:20px}:host .datetime-buttons{-webkit-padding-start:10px;padding-inline-start:10px;-webkit-padding-end:10px;padding-inline-end:10px;padding-top:10px;padding-bottom:10px;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:end;justify-content:flex-end}',At=Mt,Ot=class{constructor(t){N(this,t),this.ionCancel=x(this,"ionCancel",7),this.ionChange=x(this,"ionChange",7),this.ionValueChange=x(this,"ionValueChange",7),this.ionFocus=x(this,"ionFocus",7),this.ionBlur=x(this,"ionBlur",7),this.ionStyle=x(this,"ionStyle",7),this.ionRender=x(this,"ionRender",7),this.inputId=`ion-dt-${St++}`,this.prevPresentation=null,this.warnIfIncorrectValueUsage=()=>{let{multiple:e,value:i}=this;!e&&Array.isArray(i)&&C(`[ion-datetime] - An array of values was passed, but multiple is "false". This is incorrect usage and may result in unexpected behaviors. To dismiss this warning, pass a string to the "value" property when multiple="false".

  Value Passed: [${i.map(a=>`'${a}'`).join(", ")}]
`,this.el)},this.setValue=e=>{this.value=e,this.ionChange.emit({value:e})},this.getActivePartsWithFallback=()=>{var e;let{defaultParts:i}=this;return(e=this.getActivePart())!==null&&e!==void 0?e:i},this.getActivePart=()=>{let{activeParts:e}=this;return Array.isArray(e)?e[0]:e},this.closeParentOverlay=e=>{let i=this.el.closest("ion-modal, ion-popover");i&&i.dismiss(void 0,e)},this.setWorkingParts=e=>{this.workingParts=Object.assign({},e)},this.setActiveParts=(e,i=!1)=>{if(this.readonly)return;let{multiple:a,minParts:n,maxParts:s,activeParts:r}=this,l=_e(e,n,s);if(this.setWorkingParts(l),a){let c=Array.isArray(r)?r:[r];i?this.activeParts=c.filter(p=>!H(p,l)):this.activeParts=[...c,l]}else this.activeParts=Object.assign({},l);this.el.querySelector('[slot="buttons"]')!==null||this.showDefaultButtons||this.confirm()},this.initializeKeyboardListeners=()=>{let e=this.calendarBodyRef;if(!e)return;let i=this.el.shadowRoot,a=e.querySelector(".calendar-month:nth-of-type(2)"),n=r=>{var l;!((l=r[0].oldValue)===null||l===void 0)&&l.includes("ion-focused")||!e.classList.contains("ion-focused")||this.focusWorkingDay(a)},s=new MutationObserver(n);s.observe(e,{attributeFilter:["class"],attributeOldValue:!0}),this.destroyKeyboardMO=()=>{s?.disconnect()},e.addEventListener("keydown",r=>{let l=i.activeElement;if(!l||!l.classList.contains("calendar-day"))return;let d=Ie(l),c;switch(r.key){case"ArrowDown":r.preventDefault(),c=Ye(d);break;case"ArrowUp":r.preventDefault(),c=Re(d);break;case"ArrowRight":r.preventDefault(),c=Le(d);break;case"ArrowLeft":r.preventDefault(),c=Ee(d);break;case"Home":r.preventDefault(),c=Fe(d);break;case"End":r.preventDefault(),c=ze(d);break;case"PageUp":r.preventDefault(),c=r.shiftKey?Be(d):_(d);break;case"PageDown":r.preventDefault(),c=r.shiftKey?He(d):q(d);break;default:return}pt(c,this.minParts,this.maxParts)||(this.setWorkingParts(Object.assign(Object.assign({},this.workingParts),c)),requestAnimationFrame(()=>this.focusWorkingDay(a)))})},this.focusWorkingDay=e=>{let i=e.querySelectorAll(".calendar-day-padding"),{day:a}=this.workingParts;if(a===null)return;let n=e.querySelector(`.calendar-day-wrapper:nth-of-type(${i.length+a}) .calendar-day`);n&&n.focus()},this.processMinParts=()=>{let{min:e,defaultParts:i}=this;if(e===void 0){this.minParts=void 0;return}this.minParts=We(e,i)},this.processMaxParts=()=>{let{max:e,defaultParts:i}=this;if(e===void 0){this.maxParts=void 0;return}this.maxParts=je(e,i)},this.initializeCalendarListener=()=>{let e=this.calendarBodyRef;if(!e)return;let i=e.querySelectorAll(".calendar-month"),a=i[0],n=i[1],s=i[2],l=j(this)==="ios"&&typeof navigator<"u"&&navigator.maxTouchPoints>1;Y(()=>{e.scrollLeft=a.clientWidth*(W(this.el)?-1:1);let d=h=>{let g=e.getBoundingClientRect(),u=(W(this.el)?e.scrollLeft>=-2:e.scrollLeft<=2)?a:s,b=u.getBoundingClientRect();if(Math.abs(b.x-g.x)>2)return;let{forceRenderDate:k}=this;return k!==void 0?{month:k.month,year:k.year,day:k.day}:u===a?_(h):u===s?q(h):void 0},c=()=>{l&&(e.style.removeProperty("pointer-events"),f=!1);let h=d(this.workingParts);if(!h)return;let{month:g,day:y,year:u}=h;Z({month:g,year:u,day:null},{minParts:Object.assign(Object.assign({},this.minParts),{day:null}),maxParts:Object.assign(Object.assign({},this.maxParts),{day:null})})||(e.style.setProperty("overflow","hidden"),Y(()=>{this.setWorkingParts(Object.assign(Object.assign({},this.workingParts),{month:g,day:y,year:u})),e.scrollLeft=n.clientWidth*(W(this.el)?-1:1),e.style.removeProperty("overflow"),this.resolveForceDateScrolling&&this.resolveForceDateScrolling()}))},p,f=!1,m=()=>{p&&clearTimeout(p),!f&&l&&(e.style.setProperty("pointer-events","none"),f=!0),p=setTimeout(c,50)};e.addEventListener("scroll",m),this.destroyCalendarListener=()=>{e.removeEventListener("scroll",m)}})},this.destroyInteractionListeners=()=>{let{destroyCalendarListener:e,destroyKeyboardMO:i}=this;e!==void 0&&e(),i!==void 0&&i()},this.processValue=e=>{let i=e!=null&&e!==""&&(!Array.isArray(e)||e.length>0),a=i?te(e):this.defaultParts,{minParts:n,maxParts:s,workingParts:r,el:l}=this;if(this.warnIfIncorrectValueUsage(),!a)return;i&&Me(a,n,s);let d=Array.isArray(a)?a[0]:a,c=Ve(d,n,s),{month:p,day:f,year:m,hour:h,minute:g}=c,y=Te(h);i?Array.isArray(a)?this.activeParts=[...a]:this.activeParts={month:p,day:f,year:m,hour:h,minute:g,ampm:y}:this.activeParts=[];let u=p!==void 0&&p!==r.month||m!==void 0&&m!==r.year,b=l.classList.contains("datetime-ready"),{isGridStyle:k,showMonthAndYear:A}=this,w=!0;if(Array.isArray(a)){let M=a[0].month;for(let V of a)if(V.month!==M){w=!1;break}}w&&(k&&u&&b&&!A?this.animateToDate(c):this.setWorkingParts({month:p,day:f,year:m,hour:h,minute:g,ampm:y}))},this.animateToDate=e=>P(this,null,function*(){let{workingParts:i}=this;this.forceRenderDate=e;let a=new Promise(s=>{this.resolveForceDateScrolling=s});R(e,i)?this.prevMonth():this.nextMonth(),yield a,this.resolveForceDateScrolling=void 0,this.forceRenderDate=void 0}),this.onFocus=()=>{this.ionFocus.emit()},this.onBlur=()=>{this.ionBlur.emit()},this.hasValue=()=>this.value!=null,this.nextMonth=()=>{let e=this.calendarBodyRef;if(!e)return;let i=e.querySelector(".calendar-month:last-of-type");if(!i)return;let a=i.offsetWidth*2;e.scrollTo({top:0,left:a*(W(this.el)?-1:1),behavior:"smooth"})},this.prevMonth=()=>{let e=this.calendarBodyRef;!e||!e.querySelector(".calendar-month:first-of-type")||e.scrollTo({top:0,left:0,behavior:"smooth"})},this.toggleMonthAndYearView=()=>{this.showMonthAndYear=!this.showMonthAndYear},this.showMonthAndYear=!1,this.activeParts=[],this.workingParts={month:5,day:28,year:2021,hour:13,minute:52,ampm:"pm"},this.isTimePopoverOpen=!1,this.forceRenderDate=void 0,this.color="primary",this.name=this.inputId,this.disabled=!1,this.formatOptions=void 0,this.readonly=!1,this.isDateEnabled=void 0,this.min=void 0,this.max=void 0,this.presentation="date-time",this.cancelText="Cancel",this.doneText="Done",this.clearText="Clear",this.yearValues=void 0,this.monthValues=void 0,this.dayValues=void 0,this.hourValues=void 0,this.minuteValues=void 0,this.locale="default",this.firstDayOfWeek=0,this.titleSelectedDatesFormatter=void 0,this.multiple=!1,this.highlightedDates=void 0,this.value=void 0,this.showDefaultTitle=!1,this.showDefaultButtons=!1,this.showClearButton=!1,this.showDefaultTimeLabel=!0,this.hourCycle=void 0,this.size="fixed",this.preferWheel=!1}formatOptionsChanged(){let{el:t,formatOptions:e,presentation:i}=this;ae(t,i,e),rt(t,e)}disabledChanged(){this.emitStyle()}minChanged(){this.processMinParts()}maxChanged(){this.processMaxParts()}presentationChanged(){let{el:t,formatOptions:e,presentation:i}=this;ae(t,i,e)}get isGridStyle(){let{presentation:t,preferWheel:e}=this;return(t==="date"||t==="date-time"||t==="time-date")&&!e}yearValuesChanged(){this.parsedYearValues=D(this.yearValues)}monthValuesChanged(){this.parsedMonthValues=D(this.monthValues)}dayValuesChanged(){this.parsedDayValues=D(this.dayValues)}hourValuesChanged(){this.parsedHourValues=D(this.hourValues)}minuteValuesChanged(){this.parsedMinuteValues=D(this.minuteValues)}valueChanged(){return P(this,null,function*(){let{value:t}=this;this.hasValue()&&this.processValue(t),this.emitStyle(),this.ionValueChange.emit({value:t})})}confirm(t=!1){return P(this,null,function*(){let{isCalendarPicker:e,activeParts:i,preferWheel:a,workingParts:n}=this;(i!==void 0||!e)&&(Array.isArray(i)&&i.length===0?a?this.setValue(S(n)):this.setValue(void 0):this.setValue(S(i))),t&&this.closeParentOverlay(Vt)})}reset(t){return P(this,null,function*(){this.processValue(t)})}cancel(t=!1){return P(this,null,function*(){this.ionCancel.emit(),t&&this.closeParentOverlay(It)})}get isCalendarPicker(){let{presentation:t}=this;return t==="date"||t==="date-time"||t==="time-date"}connectedCallback(){this.clearFocusVisible=De(this.el).destroy}disconnectedCallback(){this.clearFocusVisible&&(this.clearFocusVisible(),this.clearFocusVisible=void 0)}initializeListeners(){this.initializeCalendarListener(),this.initializeKeyboardListeners()}componentDidLoad(){let{el:t,intersectionTrackerRef:e}=this,i=l=>{l[0].isIntersecting&&(this.initializeListeners(),Y(()=>{this.el.classList.add("datetime-ready")}))},a=new IntersectionObserver(i,{threshold:.01,root:t});E(()=>a?.observe(e));let n=l=>{l[0].isIntersecting||(this.destroyInteractionListeners(),this.showMonthAndYear=!1,Y(()=>{this.el.classList.remove("datetime-ready")}))},s=new IntersectionObserver(n,{threshold:0,root:t});E(()=>s?.observe(e));let r=re(this.el);r.addEventListener("ionFocus",l=>l.stopPropagation()),r.addEventListener("ionBlur",l=>l.stopPropagation())}componentDidRender(){let{presentation:t,prevPresentation:e,calendarBodyRef:i,minParts:a,preferWheel:n,forceRenderDate:s}=this,r=!n&&["date-time","time-date","date"].includes(t);if(a!==void 0&&r&&i){let l=i.querySelector(".calendar-month:nth-of-type(1)");l&&s===void 0&&(i.scrollLeft=l.clientWidth*(W(this.el)?-1:1))}if(e===null){this.prevPresentation=t;return}t!==e&&(this.prevPresentation=t,this.destroyInteractionListeners(),this.initializeListeners(),this.showMonthAndYear=!1,E(()=>{this.ionRender.emit()}))}componentWillLoad(){let{el:t,formatOptions:e,highlightedDates:i,multiple:a,presentation:n,preferWheel:s}=this;a&&(n!=="date"&&C('[ion-datetime] - Multiple date selection is only supported for presentation="date".',t),s&&C('[ion-datetime] - Multiple date selection is not supported with preferWheel="true".',t)),i!==void 0&&(n!=="date"&&n!=="date-time"&&n!=="time-date"&&C("[ion-datetime] - The highlightedDates property is only supported with the date, date-time, and time-date presentations.",t),s&&C('[ion-datetime] - The highlightedDates property is not supported with preferWheel="true".',t)),e&&(ae(t,n,e),rt(t,e));let r=this.parsedHourValues=D(this.hourValues),l=this.parsedMinuteValues=D(this.minuteValues),d=this.parsedMonthValues=D(this.monthValues),c=this.parsedYearValues=D(this.yearValues),p=this.parsedDayValues=D(this.dayValues),f=this.todayParts=te(Je());this.processMinParts(),this.processMaxParts(),this.defaultParts=qe({refParts:f,monthValues:d,dayValues:p,yearValues:c,hourValues:r,minuteValues:l,minParts:this.minParts,maxParts:this.maxParts}),this.processValue(this.value),this.emitStyle()}emitStyle(){this.ionStyle.emit({interactive:!0,datetime:!0,"interactive-disabled":this.disabled})}renderFooter(){let{disabled:t,readonly:e,showDefaultButtons:i,showClearButton:a}=this,n=t||e;if(!(this.el.querySelector('[slot="buttons"]')!==null)&&!i&&!a)return;let r=()=>{this.reset(),this.setValue(void 0)};return o("div",{class:"datetime-footer"},o("div",{class:"datetime-buttons"},o("div",{class:{"datetime-action-buttons":!0,"has-clear-button":this.showClearButton}},o("slot",{name:"buttons"},o("ion-buttons",null,i&&o("ion-button",{id:"cancel-button",color:this.color,onClick:()=>this.cancel(!0),disabled:n},this.cancelText),o("div",{class:"datetime-action-buttons-container"},a&&o("ion-button",{id:"clear-button",color:this.color,onClick:()=>r(),disabled:n},this.clearText),i&&o("ion-button",{id:"confirm-button",color:this.color,onClick:()=>this.confirm(!0),disabled:n},this.doneText)))))))}renderWheelPicker(t=this.presentation){let e=t==="time-date"?[this.renderTimePickerColumns(t),this.renderDatePickerColumns(t)]:[this.renderDatePickerColumns(t),this.renderTimePickerColumns(t)];return o("ion-picker",null,e)}renderDatePickerColumns(t){return t==="date-time"||t==="time-date"?this.renderCombinedDatePickerColumn():this.renderIndividualDatePickerColumns(t)}renderCombinedDatePickerColumn(){let{defaultParts:t,disabled:e,workingParts:i,locale:a,minParts:n,maxParts:s,todayParts:r,isDateEnabled:l}=this,d=this.getActivePartsWithFallback(),c=ie(i),p=c[c.length-1];c[0].day=1,p.day=Oe(p.month,p.year);let f=n!==void 0&&$(n,c[0])?n:c[0],m=s!==void 0&&R(s,p)?s:p,h=nt(a,r,f,m,this.parsedDayValues,this.parsedMonthValues),g=h.items,y=h.parts;l&&(g=g.map((b,k)=>{let A=y[k],w;try{w=!l(S(A))}catch(M){T("[ion-datetime] - Exception thrown from provided `isDateEnabled` function. Please check your function and try again.",M)}return Object.assign(Object.assign({},b),{disabled:w})}));let u=i.day!==null?`${i.year}-${i.month}-${i.day}`:`${t.year}-${t.month}-${t.day}`;return o("ion-picker-column",{"aria-label":"Select a date",class:"date-column",color:this.color,disabled:e,value:u,onIonChange:b=>{let{value:k}=b.detail,A=y.find(({month:w,day:M,year:V})=>k===`${V}-${w}-${M}`);this.setWorkingParts(Object.assign(Object.assign({},i),A)),this.setActiveParts(Object.assign(Object.assign({},d),A)),b.stopPropagation()}},g.map(b=>o("ion-picker-column-option",{part:b.value===u?`${v} ${I}`:v,key:b.value,disabled:b.disabled,value:b.value},b.text)))}renderIndividualDatePickerColumns(t){let{workingParts:e,isDateEnabled:i}=this,n=t!=="year"&&t!=="time"?tt(this.locale,e,this.minParts,this.maxParts,this.parsedMonthValues):[],r=t==="date"?it(this.locale,e,this.minParts,this.maxParts,this.parsedDayValues):[];i&&(r=r.map(f=>{let{value:m}=f,h=typeof m=="string"?parseInt(m):m,g={month:e.month,day:h,year:e.year},y;try{y=!i(S(g))}catch(u){T("[ion-datetime] - Exception thrown from provided `isDateEnabled` function. Please check your function and try again.",u)}return Object.assign(Object.assign({},f),{disabled:y})}));let d=t!=="month"&&t!=="time"?at(this.locale,this.defaultParts,this.minParts,this.maxParts,this.parsedYearValues):[],c=ee(this.locale,{month:"numeric",day:"numeric"}),p=[];return c?p=[this.renderMonthPickerColumn(n),this.renderDayPickerColumn(r),this.renderYearPickerColumn(d)]:p=[this.renderDayPickerColumn(r),this.renderMonthPickerColumn(n),this.renderYearPickerColumn(d)],p}renderDayPickerColumn(t){var e;if(t.length===0)return[];let{disabled:i,workingParts:a}=this,n=this.getActivePartsWithFallback(),s=(e=a.day!==null?a.day:this.defaultParts.day)!==null&&e!==void 0?e:void 0;return o("ion-picker-column",{"aria-label":"Select a day",class:"day-column",color:this.color,disabled:i,value:s,onIonChange:r=>{this.setWorkingParts(Object.assign(Object.assign({},a),{day:r.detail.value})),this.setActiveParts(Object.assign(Object.assign({},n),{day:r.detail.value})),r.stopPropagation()}},t.map(r=>o("ion-picker-column-option",{part:r.value===s?`${v} ${I}`:v,key:r.value,disabled:r.disabled,value:r.value},r.text)))}renderMonthPickerColumn(t){if(t.length===0)return[];let{disabled:e,workingParts:i}=this,a=this.getActivePartsWithFallback();return o("ion-picker-column",{"aria-label":"Select a month",class:"month-column",color:this.color,disabled:e,value:i.month,onIonChange:n=>{this.setWorkingParts(Object.assign(Object.assign({},i),{month:n.detail.value})),this.setActiveParts(Object.assign(Object.assign({},a),{month:n.detail.value})),n.stopPropagation()}},t.map(n=>o("ion-picker-column-option",{part:n.value===i.month?`${v} ${I}`:v,key:n.value,disabled:n.disabled,value:n.value},n.text)))}renderYearPickerColumn(t){if(t.length===0)return[];let{disabled:e,workingParts:i}=this,a=this.getActivePartsWithFallback();return o("ion-picker-column",{"aria-label":"Select a year",class:"year-column",color:this.color,disabled:e,value:i.year,onIonChange:n=>{this.setWorkingParts(Object.assign(Object.assign({},i),{year:n.detail.value})),this.setActiveParts(Object.assign(Object.assign({},a),{year:n.detail.value})),n.stopPropagation()}},t.map(n=>o("ion-picker-column-option",{part:n.value===i.year?`${v} ${I}`:v,key:n.value,disabled:n.disabled,value:n.value},n.text)))}renderTimePickerColumns(t){if(["date","month","month-year","year"].includes(t))return[];let i=this.getActivePart()!==void 0,{hoursData:a,minutesData:n,dayPeriodData:s}=ot(this.locale,this.workingParts,this.hourCycle,i?this.minParts:void 0,i?this.maxParts:void 0,this.parsedHourValues,this.parsedMinuteValues);return[this.renderHourPickerColumn(a),this.renderMinutePickerColumn(n),this.renderDayPeriodPickerColumn(s)]}renderHourPickerColumn(t){let{disabled:e,workingParts:i}=this;if(t.length===0)return[];let a=this.getActivePartsWithFallback();return o("ion-picker-column",{"aria-label":"Select an hour",color:this.color,disabled:e,value:a.hour,numericInput:!0,onIonChange:n=>{this.setWorkingParts(Object.assign(Object.assign({},i),{hour:n.detail.value})),this.setActiveParts(Object.assign(Object.assign({},this.getActivePartsWithFallback()),{hour:n.detail.value})),n.stopPropagation()}},t.map(n=>o("ion-picker-column-option",{part:n.value===a.hour?`${v} ${I}`:v,key:n.value,disabled:n.disabled,value:n.value},n.text)))}renderMinutePickerColumn(t){let{disabled:e,workingParts:i}=this;if(t.length===0)return[];let a=this.getActivePartsWithFallback();return o("ion-picker-column",{"aria-label":"Select a minute",color:this.color,disabled:e,value:a.minute,numericInput:!0,onIonChange:n=>{this.setWorkingParts(Object.assign(Object.assign({},i),{minute:n.detail.value})),this.setActiveParts(Object.assign(Object.assign({},this.getActivePartsWithFallback()),{minute:n.detail.value})),n.stopPropagation()}},t.map(n=>o("ion-picker-column-option",{part:n.value===a.minute?`${v} ${I}`:v,key:n.value,disabled:n.disabled,value:n.value},n.text)))}renderDayPeriodPickerColumn(t){let{disabled:e,workingParts:i}=this;if(t.length===0)return[];let a=this.getActivePartsWithFallback(),n=Se(this.locale);return o("ion-picker-column",{"aria-label":"Select a day period",style:n?{order:"-1"}:{},color:this.color,disabled:e,value:a.ampm,onIonChange:s=>{let r=$e(i,s.detail.value);this.setWorkingParts(Object.assign(Object.assign({},i),{ampm:s.detail.value,hour:r})),this.setActiveParts(Object.assign(Object.assign({},this.getActivePartsWithFallback()),{ampm:s.detail.value,hour:r})),s.stopPropagation()}},t.map(s=>o("ion-picker-column-option",{part:s.value===a.ampm?`${v} ${I}`:v,key:s.value,disabled:s.disabled,value:s.value},s.text)))}renderWheelView(t){let{locale:e}=this,a=ee(e)?"month-first":"year-first";return o("div",{class:{[`wheel-order-${a}`]:!0}},this.renderWheelPicker(t))}renderCalendarHeader(t){let{disabled:e}=this,i=t==="ios"?he:de,a=t==="ios"?X:le,n=e||vt(this.workingParts,this.minParts,this.maxParts),s=e||wt(this.workingParts,this.maxParts),r=this.el.getAttribute("dir")||void 0;return o("div",{class:"calendar-header"},o("div",{class:"calendar-action-buttons"},o("div",{class:"calendar-month-year"},o("button",{class:{"calendar-month-year-toggle":!0,"ion-activatable":!0,"ion-focusable":!0},part:"month-year-button",disabled:e,"aria-label":this.showMonthAndYear?"Hide year picker":"Show year picker",onClick:()=>this.toggleMonthAndYearView()},o("span",{id:"toggle-wrapper"},Ue(this.locale,this.workingParts),o("ion-icon",{"aria-hidden":"true",icon:this.showMonthAndYear?i:a,lazy:!1,flipRtl:!0})),t==="md"&&o("ion-ripple-effect",null))),o("div",{class:"calendar-next-prev"},o("ion-buttons",null,o("ion-button",{"aria-label":"Previous month",disabled:n,onClick:()=>this.prevMonth()},o("ion-icon",{dir:r,"aria-hidden":"true",slot:"icon-only",icon:ce,lazy:!1,flipRtl:!0})),o("ion-button",{"aria-label":"Next month",disabled:s,onClick:()=>this.nextMonth()},o("ion-icon",{dir:r,"aria-hidden":"true",slot:"icon-only",icon:X,lazy:!1,flipRtl:!0}))))),o("div",{class:"calendar-days-of-week","aria-hidden":"true"},Qe(this.locale,t,this.firstDayOfWeek%7).map(l=>o("div",{class:"day-of-week"},l))))}renderMonth(t,e){let{disabled:i,readonly:a}=this,n=this.parsedYearValues===void 0||this.parsedYearValues.includes(e),s=this.parsedMonthValues===void 0||this.parsedMonthValues.includes(t),r=!n||!s,l=i||a,d=i||Z({month:t,year:e,day:null},{minParts:Object.assign(Object.assign({},this.minParts),{day:null}),maxParts:Object.assign(Object.assign({},this.maxParts),{day:null})}),c=this.workingParts.month===t&&this.workingParts.year===e,p=this.getActivePartsWithFallback();return o("div",{"aria-hidden":c?null:"true",class:{"calendar-month":!0,"calendar-month-disabled":!c&&d}},o("div",{class:"calendar-month-grid"},et(t,e,this.firstDayOfWeek%7).map((f,m)=>{let{day:h,dayOfWeek:g}=f,{el:y,highlightedDates:u,isDateEnabled:b,multiple:k}=this,A={month:t,day:h,year:e},w=h===null,{isActive:M,isToday:V,ariaLabel:mt,ariaSelected:ut,disabled:gt,text:ft}=kt(this.locale,A,this.activeParts,this.todayParts,this.minParts,this.maxParts,this.parsedDayValues),ne=S(A),F=r||gt;if(!F&&b!==void 0)try{F=!b(ne)}catch(L){T("[ion-datetime] - Exception thrown from provided `isDateEnabled` function. Please check your function and try again.",y,L)}let bt=F&&l,yt=F||l,z;u!==void 0&&!M&&h!==null&&(z=Pt(u,ne,y));let oe;return w||(oe=`calendar-day${M?" active":""}${V?" today":""}${F?" disabled":""}`),o("div",{class:"calendar-day-wrapper"},o("button",{ref:L=>{L&&(L.style.setProperty("color",`${z?z.textColor:""}`,"important"),L.style.setProperty("background-color",`${z?z.backgroundColor:""}`,"important"))},tabindex:"-1","data-day":h,"data-month":t,"data-year":e,"data-index":m,"data-day-of-week":g,disabled:yt,class:{"calendar-day-padding":w,"calendar-day":!0,"calendar-day-active":M,"calendar-day-constrained":bt,"calendar-day-today":V},part:oe,"aria-hidden":w?"true":null,"aria-selected":ut,"aria-label":mt,onClick:()=>{w||(this.setWorkingParts(Object.assign(Object.assign({},this.workingParts),{month:t,day:h,year:e})),k?this.setActiveParts({month:t,day:h,year:e},M):this.setActiveParts(Object.assign(Object.assign({},p),{month:t,day:h,year:e})))}},ft))})))}renderCalendarBody(){return o("div",{class:"calendar-body ion-focusable",ref:t=>this.calendarBodyRef=t,tabindex:"0"},ie(this.workingParts,this.forceRenderDate).map(({month:t,year:e})=>this.renderMonth(t,e)))}renderCalendar(t){return o("div",{class:"datetime-calendar",key:"datetime-calendar"},this.renderCalendarHeader(t),this.renderCalendarBody())}renderTimeLabel(){if(!(!(this.el.querySelector('[slot="time-label"]')!==null)&&!this.showDefaultTimeLabel))return o("slot",{name:"time-label"},"Time")}renderTimeOverlay(){let{disabled:t,hourCycle:e,isTimePopoverOpen:i,locale:a,formatOptions:n}=this,s=Ae(a,e),r=this.getActivePartsWithFallback();return[o("div",{class:"time-header"},this.renderTimeLabel()),o("button",{class:{"time-body":!0,"time-body-active":i},part:`time-button${i?" active":""}`,"aria-expanded":"false","aria-haspopup":"true",disabled:t,onClick:l=>P(this,null,function*(){let{popoverRef:d}=this;d&&(this.isTimePopoverOpen=!0,d.present(new CustomEvent("ionShadowTarget",{detail:{ionShadowTarget:l.target}})),yield d.onWillDismiss(),this.isTimePopoverOpen=!1)})},Ne(a,r,s,n?.time)),o("ion-popover",{alignment:"center",translucent:!0,overlayIndex:1,arrow:!1,onWillPresent:l=>{l.target.querySelectorAll("ion-picker-column").forEach(c=>c.scrollActiveItemIntoView())},style:{"--offset-y":"-10px","--min-width":"fit-content"},keyboardEvents:!0,ref:l=>this.popoverRef=l},this.renderWheelPicker("time"))]}getHeaderSelectedDateText(){var t;let{activeParts:e,formatOptions:i,multiple:a,titleSelectedDatesFormatter:n}=this,s=Array.isArray(e),r;if(a&&s&&e.length!==1){if(r=`${e.length} days`,n!==void 0)try{r=n(S(e))}catch(l){T("[ion-datetime] - Exception in provided `titleSelectedDatesFormatter`:",l)}}else r=Ge(this.locale,this.getActivePartsWithFallback(),(t=i?.date)!==null&&t!==void 0?t:{weekday:"short",month:"short",day:"numeric"});return r}renderHeader(t=!0){if(!(!(this.el.querySelector('[slot="title"]')!==null)&&!this.showDefaultTitle))return o("div",{class:"datetime-header"},o("div",{class:"datetime-title"},o("slot",{name:"title"},"Select Date")),t&&o("div",{class:"datetime-selected-date"},this.getHeaderSelectedDateText()))}renderTime(){let{presentation:t}=this;return o("div",{class:"datetime-time"},t==="time"?this.renderWheelPicker():this.renderTimeOverlay())}renderCalendarViewMonthYearPicker(){return o("div",{class:"datetime-year"},this.renderWheelView("month-year"))}renderDatetime(t){let{presentation:e,preferWheel:i}=this;if(i&&(e==="date"||e==="date-time"||e==="time-date"))return[this.renderHeader(!1),this.renderWheelView(),this.renderFooter()];switch(e){case"date-time":return[this.renderHeader(),this.renderCalendar(t),this.renderCalendarViewMonthYearPicker(),this.renderTime(),this.renderFooter()];case"time-date":return[this.renderHeader(),this.renderTime(),this.renderCalendar(t),this.renderCalendarViewMonthYearPicker(),this.renderFooter()];case"time":return[this.renderHeader(!1),this.renderTime(),this.renderFooter()];case"month":case"month-year":case"year":return[this.renderHeader(!1),this.renderWheelView(),this.renderFooter()];default:return[this.renderHeader(),this.renderCalendar(t),this.renderCalendarViewMonthYearPicker(),this.renderFooter()]}}render(){let{name:t,value:e,disabled:i,el:a,color:n,readonly:s,showMonthAndYear:r,preferWheel:l,presentation:d,size:c,isGridStyle:p}=this,f=j(this),m=d==="year"||d==="month"||d==="month-year",h=r||m,g=r&&!m,u=(d==="date"||d==="date-time"||d==="time-date")&&l;return se(!0,a,t,Xe(e),i),o(K,{key:"c3dfea8f46fcbcef38eb9e8a69b1b46a4e4b82fd","aria-disabled":i?"true":null,onFocus:this.onFocus,onBlur:this.onBlur,class:Object.assign({},Ce(n,{[f]:!0,"datetime-readonly":s,"datetime-disabled":i,"show-month-and-year":h,"month-year-picker-open":g,[`datetime-presentation-${d}`]:!0,[`datetime-size-${c}`]:!0,"datetime-prefer-wheel":u,"datetime-grid":p}))},o("div",{key:"75c91243cf6a51f44b83d7cf7d8c0c96bfd3c83f",class:"intersection-tracker",ref:b=>this.intersectionTrackerRef=b}),this.renderDatetime(f))}get el(){return U(this)}static get watchers(){return{formatOptions:["formatOptionsChanged"],disabled:["disabledChanged"],min:["minChanged"],max:["maxChanged"],presentation:["presentationChanged"],yearValues:["yearValuesChanged"],monthValues:["monthValuesChanged"],dayValues:["dayValuesChanged"],hourValues:["hourValuesChanged"],minuteValues:["minuteValuesChanged"],value:["valueChanged"]}}},St=0,It="datetime-cancel",Vt="datetime-confirm",v="wheel-item",I="active";Ot.style={ios:Dt,md:At};var st=t=>{let e=O(),i=O(),a=O();return i.addElement(t.querySelector("ion-backdrop")).fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),a.addElement(t.querySelector(".picker-wrapper")).fromTo("transform","translateY(100%)","translateY(0%)"),e.addElement(t).easing("cubic-bezier(.36,.66,.04,1)").duration(400).addAnimation([i,a])},lt=t=>{let e=O(),i=O(),a=O();return i.addElement(t.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)",.01),a.addElement(t.querySelector(".picker-wrapper")).fromTo("transform","translateY(0%)","translateY(100%)"),e.addElement(t).easing("cubic-bezier(.36,.66,.04,1)").duration(400).addAnimation([i,a])},Tt=".sc-ion-picker-legacy-ios-h{--border-radius:0;--border-style:solid;--min-width:auto;--width:100%;--max-width:500px;--min-height:auto;--max-height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;top:0;display:block;position:absolute;width:100%;height:100%;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.sc-ion-picker-legacy-ios-h{inset-inline-start:0}.overlay-hidden.sc-ion-picker-legacy-ios-h{display:none}.picker-wrapper.sc-ion-picker-legacy-ios{border-radius:var(--border-radius);left:0;right:0;bottom:0;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:auto;margin-bottom:auto;-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;overflow:hidden;z-index:10}.picker-toolbar.sc-ion-picker-legacy-ios{width:100%;background:transparent;contain:strict;z-index:1}.picker-button.sc-ion-picker-legacy-ios{border:0;font-family:inherit}.picker-button.sc-ion-picker-legacy-ios:active,.picker-button.sc-ion-picker-legacy-ios:focus{outline:none}.picker-columns.sc-ion-picker-legacy-ios{display:-ms-flexbox;display:flex;position:relative;-ms-flex-pack:center;justify-content:center;margin-bottom:var(--ion-safe-area-bottom, 0);contain:strict;overflow:hidden}.picker-above-highlight.sc-ion-picker-legacy-ios,.picker-below-highlight.sc-ion-picker-legacy-ios{display:none;pointer-events:none}.sc-ion-picker-legacy-ios-h{--background:var(--ion-background-color, #fff);--border-width:1px 0 0;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))));--height:260px;--backdrop-opacity:var(--ion-backdrop-opacity, 0.26);color:var(--ion-item-color, var(--ion-text-color, #000))}.picker-toolbar.sc-ion-picker-legacy-ios{display:-ms-flexbox;display:flex;height:44px;border-bottom:0.55px solid var(--border-color)}.picker-toolbar-button.sc-ion-picker-legacy-ios{-ms-flex:1;flex:1;text-align:end}.picker-toolbar-button.sc-ion-picker-legacy-ios:last-child .picker-button.sc-ion-picker-legacy-ios{font-weight:600}.picker-toolbar-button.sc-ion-picker-legacy-ios:first-child{font-weight:normal;text-align:start}.picker-button.sc-ion-picker-legacy-ios,.picker-button.ion-activated.sc-ion-picker-legacy-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:1em;padding-inline-start:1em;-webkit-padding-end:1em;padding-inline-end:1em;padding-top:0;padding-bottom:0;height:44px;background:transparent;color:var(--ion-color-primary, #0054e9);font-size:16px}.picker-columns.sc-ion-picker-legacy-ios{height:215px;-webkit-perspective:1000px;perspective:1000px}.picker-above-highlight.sc-ion-picker-legacy-ios{top:0;-webkit-transform:translate3d(0,  0,  90px);transform:translate3d(0,  0,  90px);display:block;position:absolute;width:100%;height:81px;border-bottom:1px solid var(--border-color);background:-webkit-gradient(linear, left top, left bottom, color-stop(20%, var(--background, var(--ion-background-color, #fff))), to(rgba(var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255)), 0.8)));background:linear-gradient(to bottom, var(--background, var(--ion-background-color, #fff)) 20%, rgba(var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255)), 0.8) 100%);z-index:10}.picker-above-highlight.sc-ion-picker-legacy-ios{inset-inline-start:0}.picker-below-highlight.sc-ion-picker-legacy-ios{top:115px;-webkit-transform:translate3d(0,  0,  90px);transform:translate3d(0,  0,  90px);display:block;position:absolute;width:100%;height:119px;border-top:1px solid var(--border-color);background:-webkit-gradient(linear, left bottom, left top, color-stop(30%, var(--background, var(--ion-background-color, #fff))), to(rgba(var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255)), 0.8)));background:linear-gradient(to top, var(--background, var(--ion-background-color, #fff)) 30%, rgba(var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255)), 0.8) 100%);z-index:11}.picker-below-highlight.sc-ion-picker-legacy-ios{inset-inline-start:0}",jt=Tt,Wt=".sc-ion-picker-legacy-md-h{--border-radius:0;--border-style:solid;--min-width:auto;--width:100%;--max-width:500px;--min-height:auto;--max-height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;top:0;display:block;position:absolute;width:100%;height:100%;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.sc-ion-picker-legacy-md-h{inset-inline-start:0}.overlay-hidden.sc-ion-picker-legacy-md-h{display:none}.picker-wrapper.sc-ion-picker-legacy-md{border-radius:var(--border-radius);left:0;right:0;bottom:0;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:auto;margin-bottom:auto;-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;overflow:hidden;z-index:10}.picker-toolbar.sc-ion-picker-legacy-md{width:100%;background:transparent;contain:strict;z-index:1}.picker-button.sc-ion-picker-legacy-md{border:0;font-family:inherit}.picker-button.sc-ion-picker-legacy-md:active,.picker-button.sc-ion-picker-legacy-md:focus{outline:none}.picker-columns.sc-ion-picker-legacy-md{display:-ms-flexbox;display:flex;position:relative;-ms-flex-pack:center;justify-content:center;margin-bottom:var(--ion-safe-area-bottom, 0);contain:strict;overflow:hidden}.picker-above-highlight.sc-ion-picker-legacy-md,.picker-below-highlight.sc-ion-picker-legacy-md{display:none;pointer-events:none}.sc-ion-picker-legacy-md-h{--background:var(--ion-background-color, #fff);--border-width:0.55px 0 0;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));--height:260px;--backdrop-opacity:var(--ion-backdrop-opacity, 0.26);color:var(--ion-item-color, var(--ion-text-color, #000))}.picker-toolbar.sc-ion-picker-legacy-md{display:-ms-flexbox;display:flex;-ms-flex-pack:end;justify-content:flex-end;height:44px}.picker-button.sc-ion-picker-legacy-md,.picker-button.ion-activated.sc-ion-picker-legacy-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:1.1em;padding-inline-start:1.1em;-webkit-padding-end:1.1em;padding-inline-end:1.1em;padding-top:0;padding-bottom:0;height:44px;background:transparent;color:var(--ion-color-primary, #0054e9);font-size:14px;font-weight:500;text-transform:uppercase;-webkit-box-shadow:none;box-shadow:none}.picker-columns.sc-ion-picker-legacy-md{height:216px;-webkit-perspective:1800px;perspective:1800px}.picker-above-highlight.sc-ion-picker-legacy-md{top:0;-webkit-transform:translate3d(0,  0,  90px);transform:translate3d(0,  0,  90px);position:absolute;width:100%;height:81px;border-bottom:1px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));background:-webkit-gradient(linear, left top, left bottom, color-stop(20%, var(--ion-background-color, #fff)), to(rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8)));background:linear-gradient(to bottom, var(--ion-background-color, #fff) 20%, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8) 100%);z-index:10}.picker-above-highlight.sc-ion-picker-legacy-md{inset-inline-start:0}.picker-below-highlight.sc-ion-picker-legacy-md{top:115px;-webkit-transform:translate3d(0,  0,  90px);transform:translate3d(0,  0,  90px);position:absolute;width:100%;height:119px;border-top:1px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));background:-webkit-gradient(linear, left bottom, left top, color-stop(30%, var(--ion-background-color, #fff)), to(rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8)));background:linear-gradient(to top, var(--ion-background-color, #fff) 30%, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8) 100%);z-index:11}.picker-below-highlight.sc-ion-picker-legacy-md{inset-inline-start:0}",Ft=Wt,zt=class{constructor(t){N(this,t),this.didPresent=x(this,"ionPickerDidPresent",7),this.willPresent=x(this,"ionPickerWillPresent",7),this.willDismiss=x(this,"ionPickerWillDismiss",7),this.didDismiss=x(this,"ionPickerDidDismiss",7),this.didPresentShorthand=x(this,"didPresent",7),this.willPresentShorthand=x(this,"willPresent",7),this.willDismissShorthand=x(this,"willDismiss",7),this.didDismissShorthand=x(this,"didDismiss",7),this.delegateController=we(this),this.lockController=ge(),this.triggerController=Pe(),this.onBackdropTap=()=>{this.dismiss(void 0,ve)},this.dispatchCancelHandler=e=>{let i=e.detail.role;if(Q(i)){let a=this.buttons.find(n=>n.role==="cancel");this.callButtonHandler(a)}},this.presented=!1,this.overlayIndex=void 0,this.delegate=void 0,this.hasController=!1,this.keyboardClose=!0,this.enterAnimation=void 0,this.leaveAnimation=void 0,this.buttons=[],this.columns=[],this.cssClass=void 0,this.duration=0,this.showBackdrop=!0,this.backdropDismiss=!0,this.animated=!0,this.htmlAttributes=void 0,this.isOpen=!1,this.trigger=void 0}onIsOpenChange(t,e){t===!0&&e===!1?this.present():t===!1&&e===!0&&this.dismiss()}triggerChanged(){let{trigger:t,el:e,triggerController:i}=this;t&&i.addClickListener(e,t)}connectedCallback(){fe(this.el),this.triggerChanged()}disconnectedCallback(){this.triggerController.removeClickListener()}componentWillLoad(){var t;!((t=this.htmlAttributes)===null||t===void 0)&&t.id||be(this.el)}componentDidLoad(){C("[ion-picker-legacy] - ion-picker-legacy and ion-picker-legacy-column have been deprecated in favor of new versions of the ion-picker and ion-picker-column components. These new components display inline with your page content allowing for more presentation flexibility than before.",this.el),this.isOpen===!0&&E(()=>this.present()),this.triggerChanged()}present(){return P(this,null,function*(){let t=yield this.lockController.lock();yield this.delegateController.attachViewToDom(),yield ye(this,"pickerEnter",st,st,void 0),this.duration>0&&(this.durationTimeout=setTimeout(()=>this.dismiss(),this.duration)),t()})}dismiss(t,e){return P(this,null,function*(){let i=yield this.lockController.lock();this.durationTimeout&&clearTimeout(this.durationTimeout);let a=yield xe(this,t,e,"pickerLeave",lt,lt);return a&&this.delegateController.removeViewFromDom(),i(),a})}onDidDismiss(){return J(this.el,"ionPickerDidDismiss")}onWillDismiss(){return J(this.el,"ionPickerWillDismiss")}getColumn(t){return Promise.resolve(this.columns.find(e=>e.name===t))}buttonClick(t){return P(this,null,function*(){let e=t.role;return Q(e)?this.dismiss(void 0,e):(yield this.callButtonHandler(t))?this.dismiss(this.getSelected(),t.role):Promise.resolve()})}callButtonHandler(t){return P(this,null,function*(){return!(t&&(yield ke(t.handler,this.getSelected()))===!1)})}getSelected(){let t={};return this.columns.forEach((e,i)=>{let a=e.selectedIndex!==void 0?e.options[e.selectedIndex]:void 0;t[e.name]={text:a?a.text:void 0,value:a?a.value:void 0,columnIndex:i}}),t}render(){let{htmlAttributes:t}=this,e=j(this);return o(K,Object.assign({key:"b6b6ca6f9aa74681e6d67f64b366f5965fec2a6d","aria-modal":"true",tabindex:"-1"},t,{style:{zIndex:`${2e4+this.overlayIndex}`},class:Object.assign({[e]:!0,[`picker-${e}`]:!0,"overlay-hidden":!0},B(this.cssClass)),onIonBackdropTap:this.onBackdropTap,onIonPickerWillDismiss:this.dispatchCancelHandler}),o("ion-backdrop",{key:"20202ca1d7b6cd5f517a802879b39efb79033cb1",visible:this.showBackdrop,tappable:this.backdropDismiss}),o("div",{key:"72fe76a1e1748593cdf38deab5100087bfa75983",tabindex:"0","aria-hidden":"true"}),o("div",{key:"921954cfc716f3774aab66677563754ff479a44a",class:"picker-wrapper ion-overlay-wrapper",role:"dialog"},o("div",{key:"224413950bfcf2a948e58c2554c2a37a4e6d0319",class:"picker-toolbar"},this.buttons.map(i=>o("div",{class:Lt(i)},o("button",{type:"button",onClick:()=>this.buttonClick(i),class:Et(i)},i.text)))),o("div",{key:"7e688c2d0705940ec8a9ace493b679e6a9b68860",class:"picker-columns"},o("div",{key:"0ec2db79a9ca9e2a0b324b6c4b90176a0eb33df3",class:"picker-above-highlight"}),this.presented&&this.columns.map(i=>o("ion-picker-legacy-column",{col:i})),o("div",{key:"b8344f4f342fddc3f773435515567ef8f3accbb0",class:"picker-below-highlight"}))),o("div",{key:"374c7a6b31b0a00ab3913faeea0ec3d6c02274b9",tabindex:"0","aria-hidden":"true"}))}get el(){return U(this)}static get watchers(){return{isOpen:["onIsOpenChange"],trigger:["triggerChanged"]}}},Lt=t=>({[`picker-toolbar-${t.role}`]:t.role!==void 0,"picker-toolbar-button":!0}),Et=t=>Object.assign({"picker-button":!0,"ion-activatable":!0},B(t.cssClass));zt.style={ios:jt,md:Ft};var Rt=".picker-col{display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-pack:center;justify-content:center;height:100%;-webkit-box-sizing:content-box;box-sizing:content-box;contain:content}.picker-opts{position:relative;-ms-flex:1;flex:1;max-width:100%}.picker-opt{top:0;display:block;position:absolute;width:100%;border:0;text-align:center;text-overflow:ellipsis;white-space:nowrap;contain:strict;overflow:hidden;will-change:transform}.picker-opt{inset-inline-start:0}.picker-opt.picker-opt-disabled{pointer-events:none}.picker-opt-disabled{opacity:0}.picker-opts-left{-ms-flex-pack:start;justify-content:flex-start}.picker-opts-right{-ms-flex-pack:end;justify-content:flex-end}.picker-opt:active,.picker-opt:focus{outline:none}.picker-prefix{position:relative;-ms-flex:1;flex:1;text-align:end;white-space:nowrap}.picker-suffix{position:relative;-ms-flex:1;flex:1;text-align:start;white-space:nowrap}.picker-col{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:0;padding-bottom:0;-webkit-transform-style:preserve-3d;transform-style:preserve-3d}.picker-prefix,.picker-suffix,.picker-opts{top:77px;-webkit-transform-style:preserve-3d;transform-style:preserve-3d;color:inherit;font-size:20px;line-height:42px;pointer-events:none}.picker-opt{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-transform-origin:center center;transform-origin:center center;height:46px;-webkit-transform-style:preserve-3d;transform-style:preserve-3d;-webkit-transition-timing-function:ease-out;transition-timing-function:ease-out;background:transparent;color:inherit;font-size:20px;line-height:42px;-webkit-backface-visibility:hidden;backface-visibility:hidden;pointer-events:auto}:host-context([dir=rtl]) .picker-opt{-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}[dir=rtl] .picker-opt{-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}@supports selector(:dir(rtl)){.picker-opt:dir(rtl){-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}}",Yt=Rt,Bt=".picker-col{display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-pack:center;justify-content:center;height:100%;-webkit-box-sizing:content-box;box-sizing:content-box;contain:content}.picker-opts{position:relative;-ms-flex:1;flex:1;max-width:100%}.picker-opt{top:0;display:block;position:absolute;width:100%;border:0;text-align:center;text-overflow:ellipsis;white-space:nowrap;contain:strict;overflow:hidden;will-change:transform}.picker-opt{inset-inline-start:0}.picker-opt.picker-opt-disabled{pointer-events:none}.picker-opt-disabled{opacity:0}.picker-opts-left{-ms-flex-pack:start;justify-content:flex-start}.picker-opts-right{-ms-flex-pack:end;justify-content:flex-end}.picker-opt:active,.picker-opt:focus{outline:none}.picker-prefix{position:relative;-ms-flex:1;flex:1;text-align:end;white-space:nowrap}.picker-suffix{position:relative;-ms-flex:1;flex:1;text-align:start;white-space:nowrap}.picker-col{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:0;padding-bottom:0;-webkit-transform-style:preserve-3d;transform-style:preserve-3d}.picker-prefix,.picker-suffix,.picker-opts{top:77px;-webkit-transform-style:preserve-3d;transform-style:preserve-3d;color:inherit;font-size:22px;line-height:42px;pointer-events:none}.picker-opt{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;height:43px;-webkit-transition-timing-function:ease-out;transition-timing-function:ease-out;background:transparent;color:inherit;font-size:22px;line-height:42px;-webkit-backface-visibility:hidden;backface-visibility:hidden;pointer-events:auto}.picker-prefix,.picker-suffix,.picker-opt.picker-opt-selected{color:var(--ion-color-primary, #0054e9)}",Ht=Bt,$t=class{constructor(t){N(this,t),this.ionPickerColChange=x(this,"ionPickerColChange",7),this.optHeight=0,this.rotateFactor=0,this.scaleFactor=1,this.velocity=0,this.y=0,this.noAnimate=!0,this.colDidChange=!1,this.col=void 0}colChanged(){this.colDidChange=!0}connectedCallback(){return P(this,null,function*(){let t=0,e=.81;j(this)==="ios"&&(t=-.46,e=1),this.rotateFactor=t,this.scaleFactor=e,this.gesture=(yield import("./chunk-F5F7W64E.js")).createGesture({el:this.el,gestureName:"picker-swipe",gesturePriority:100,threshold:0,passive:!1,onStart:a=>this.onStart(a),onMove:a=>this.onMove(a),onEnd:a=>this.onEnd(a)}),this.gesture.enable(),this.tmrId=setTimeout(()=>{this.noAnimate=!1,this.refresh(!0)},250)})}componentDidLoad(){this.onDomChange()}componentDidUpdate(){this.colDidChange&&(this.onDomChange(!0,!1),this.colDidChange=!1)}disconnectedCallback(){this.rafId!==void 0&&cancelAnimationFrame(this.rafId),this.tmrId&&clearTimeout(this.tmrId),this.gesture&&(this.gesture.destroy(),this.gesture=void 0)}emitColChange(){this.ionPickerColChange.emit(this.col)}setSelected(t,e){let i=t>-1?-(t*this.optHeight):0;this.velocity=0,this.rafId!==void 0&&cancelAnimationFrame(this.rafId),this.update(i,e,!0),this.emitColChange()}update(t,e,i){if(!this.optsEl)return;let a=0,n=0,{col:s,rotateFactor:r}=this,l=s.selectedIndex,d=s.selectedIndex=this.indexForY(-t),c=e===0?"":e+"ms",p=`scale(${this.scaleFactor})`,f=this.optsEl.children;for(let m=0;m<f.length;m++){let h=f[m],g=s.options[m],y=m*this.optHeight+t,u="";if(r!==0){let k=y*r;Math.abs(k)<=90?(a=0,n=90,u=`rotateX(${k}deg) `):a=-9999}else n=0,a=y;let b=d===m;u+=`translate3d(0px,${a}px,${n}px) `,this.scaleFactor!==1&&!b&&(u+=p),this.noAnimate?(g.duration=0,h.style.transitionDuration=""):e!==g.duration&&(g.duration=e,h.style.transitionDuration=c),u!==g.transform&&(g.transform=u),h.style.transform=u,g.selected=b,b?h.classList.add(dt):h.classList.remove(dt)}this.col.prevSelected=l,i&&(this.y=t),this.lastIndex!==d&&(me(),this.lastIndex=d)}decelerate(){if(this.velocity!==0){this.velocity*=_t,this.velocity=this.velocity>0?Math.max(this.velocity,1):Math.min(this.velocity,-1);let t=this.y+this.velocity;t>this.minY?(t=this.minY,this.velocity=0):t<this.maxY&&(t=this.maxY,this.velocity=0),this.update(t,0,!0),Math.round(t)%this.optHeight!==0||Math.abs(this.velocity)>1?this.rafId=requestAnimationFrame(()=>this.decelerate()):(this.velocity=0,this.emitColChange(),ue())}else if(this.y%this.optHeight!==0){let t=Math.abs(this.y%this.optHeight);this.velocity=t>this.optHeight/2?1:-1,this.decelerate()}}indexForY(t){return Math.min(Math.max(Math.abs(Math.round(t/this.optHeight)),0),this.col.options.length-1)}onStart(t){t.event.cancelable&&t.event.preventDefault(),t.event.stopPropagation(),pe(),this.rafId!==void 0&&cancelAnimationFrame(this.rafId);let e=this.col.options,i=e.length-1,a=0;for(let n=0;n<e.length;n++)e[n].disabled||(i=Math.min(i,n),a=Math.max(a,n));this.minY=-(i*this.optHeight),this.maxY=-(a*this.optHeight)}onMove(t){t.event.cancelable&&t.event.preventDefault(),t.event.stopPropagation();let e=this.y+t.deltaY;e>this.minY?(e=Math.pow(e,.8),this.bounceFrom=e):e<this.maxY?(e+=Math.pow(this.maxY-e,.9),this.bounceFrom=e):this.bounceFrom=0,this.update(e,0,!1)}onEnd(t){if(this.bounceFrom>0){this.update(this.minY,100,!0),this.emitColChange();return}else if(this.bounceFrom<0){this.update(this.maxY,100,!0),this.emitColChange();return}if(this.velocity=G(-ct,t.velocityY*23,ct),this.velocity===0&&t.deltaY===0){let e=t.event.target.closest(".picker-opt");e?.hasAttribute("opt-index")&&this.setSelected(parseInt(e.getAttribute("opt-index"),10),ht)}else{if(this.y+=t.deltaY,Math.abs(t.velocityY)<.05){let e=t.deltaY>0,i=Math.abs(this.y)%this.optHeight/this.optHeight;e&&i>.5?this.velocity=Math.abs(this.velocity)*-1:!e&&i<=.5&&(this.velocity=Math.abs(this.velocity))}this.decelerate()}}refresh(t,e){var i;let a=this.col.options.length-1,n=0,s=this.col.options;for(let l=0;l<s.length;l++)s[l].disabled||(a=Math.min(a,l),n=Math.max(n,l));if(this.velocity!==0)return;let r=G(a,(i=this.col.selectedIndex)!==null&&i!==void 0?i:0,n);if(this.col.prevSelected!==r||t){let l=r*this.optHeight*-1,d=e?ht:0;this.velocity=0,this.update(l,d,!0)}}onDomChange(t,e){let i=this.optsEl;i&&(this.optHeight=i.firstElementChild?i.firstElementChild.clientHeight:0),this.refresh(t,e)}render(){let t=this.col,e=j(this);return o(K,{key:"88a3c9397c9ac92dd814074c8ae6ecf8e3420a2c",class:Object.assign({[e]:!0,"picker-col":!0,"picker-opts-left":this.col.align==="left","picker-opts-right":this.col.align==="right"},B(t.cssClass)),style:{"max-width":this.col.columnWidth}},t.prefix&&o("div",{key:"4491a705d15337e6f45f3cf6fd21af5242474729",class:"picker-prefix",style:{width:t.prefixWidth}},t.prefix),o("div",{key:"b0dd4b7a7a4c1edc4b73e7fb134ac85264072365",class:"picker-opts",style:{maxWidth:t.optionsWidth},ref:i=>this.optsEl=i},t.options.map((i,a)=>o("button",{"aria-label":i.ariaLabel,class:{"picker-opt":!0,"picker-opt-disabled":!!i.disabled},"opt-index":a},i.text))),t.suffix&&o("div",{key:"c16419ce6481d60fc3ba6b8d102a4edf0ede02aa",class:"picker-suffix",style:{width:t.suffixWidth}},t.suffix))}get el(){return U(this)}static get watchers(){return{col:["colChanged"]}}},dt="picker-opt-selected",_t=.97,ct=90,ht=150;$t.style={ios:Yt,md:Ht};export{Ot as ion_datetime,zt as ion_picker_legacy,$t as ion_picker_legacy_column};
