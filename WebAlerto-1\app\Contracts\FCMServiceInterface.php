<?php

namespace App\Contracts;

interface FCMServiceInterface
{
    /**
     * Send a notification to a single device
     *
     * @param string $token The device token
     * @param array $notification The notification data
     * @return string The message ID
     * @throws \App\Exceptions\FCMException
     */
    public function send(string $token, array $notification);

    /**
     * Send notifications to multiple devices
     *
     * @param array $tokens Array of device tokens
     * @param array $notification The notification data
     * @param bool $detailed Whether to return detailed results
     * @return array The send results
     * @throws \App\Exceptions\FCMException
     */
    public function sendMulticast(array $tokens, array $notification, bool $detailed = false): array;

    /**
     * Validate a device token
     *
     * @param string $token The device token to validate
     * @return bool Whether the token is valid
     */
    public function validateToken(string $token): bool;

    /**
     * Get the delivery status of a message
     *
     * @param string $messageId The message ID
     * @return array The delivery status
     * @throws \App\Exceptions\FCMException
     */
    public function getDeliveryStatus(string $messageId): array;
} 