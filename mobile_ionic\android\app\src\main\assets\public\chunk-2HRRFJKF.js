import{a as $e,c as ce,g as D}from"./chunk-2R6CW7ES.js";var Je=ce({"./ion-accordion_2.entry.js":()=>import("./chunk-NBYDQADW.js"),"./ion-action-sheet.entry.js":()=>import("./chunk-FM7G2Q5G.js"),"./ion-alert.entry.js":()=>import("./chunk-FQG4RNZL.js"),"./ion-app_8.entry.js":()=>import("./chunk-QML2SRNW.js"),"./ion-avatar_3.entry.js":()=>import("./chunk-7TILIC5Z.js"),"./ion-back-button.entry.js":()=>import("./chunk-VZIO5RA6.js"),"./ion-backdrop.entry.js":()=>import("./chunk-ROIAEOSV.js"),"./ion-breadcrumb_2.entry.js":()=>import("./chunk-5WTQIOE7.js"),"./ion-button_2.entry.js":()=>import("./chunk-QQULDWX4.js"),"./ion-card_5.entry.js":()=>import("./chunk-2TTJTTAQ.js"),"./ion-checkbox.entry.js":()=>import("./chunk-YPTRCEOB.js"),"./ion-chip.entry.js":()=>import("./chunk-SWNPWSOK.js"),"./ion-col_3.entry.js":()=>import("./chunk-QSSSEVFS.js"),"./ion-datetime-button.entry.js":()=>import("./chunk-Z32BREST.js"),"./ion-datetime_3.entry.js":()=>import("./chunk-2GCW27TX.js"),"./ion-fab_3.entry.js":()=>import("./chunk-5IN5NBHO.js"),"./ion-img.entry.js":()=>import("./chunk-7GIW4BZS.js"),"./ion-infinite-scroll_2.entry.js":()=>import("./chunk-EOH3VD6R.js"),"./ion-input-password-toggle.entry.js":()=>import("./chunk-ZI2CD7SQ.js"),"./ion-input.entry.js":()=>import("./chunk-IFH3KYMM.js"),"./ion-item-option_3.entry.js":()=>import("./chunk-KQOOLG2C.js"),"./ion-item_8.entry.js":()=>import("./chunk-HP4XFCX2.js"),"./ion-loading.entry.js":()=>import("./chunk-G33IKECX.js"),"./ion-menu_3.entry.js":()=>import("./chunk-YCRQ3Z34.js"),"./ion-modal.entry.js":()=>import("./chunk-HX6JQYDB.js"),"./ion-nav_2.entry.js":()=>import("./chunk-54KRLLBK.js"),"./ion-picker-column-option.entry.js":()=>import("./chunk-2Q6MJFJ7.js"),"./ion-picker-column.entry.js":()=>import("./chunk-X7CCNRCK.js"),"./ion-picker.entry.js":()=>import("./chunk-VX2S7P5I.js"),"./ion-popover.entry.js":()=>import("./chunk-GTG5RU5D.js"),"./ion-progress-bar.entry.js":()=>import("./chunk-Z2RM2QSS.js"),"./ion-radio_2.entry.js":()=>import("./chunk-IR5VB6EQ.js"),"./ion-range.entry.js":()=>import("./chunk-TEV6BISA.js"),"./ion-refresher_2.entry.js":()=>import("./chunk-HHKBO2BO.js"),"./ion-reorder_2.entry.js":()=>import("./chunk-ABV4IYZG.js"),"./ion-ripple-effect.entry.js":()=>import("./chunk-YRLFIA7R.js"),"./ion-route_4.entry.js":()=>import("./chunk-65DNBRFF.js"),"./ion-searchbar.entry.js":()=>import("./chunk-NTUSSCV2.js"),"./ion-segment-content.entry.js":()=>import("./chunk-XJ6JCEWP.js"),"./ion-segment-view.entry.js":()=>import("./chunk-DNH7GQSE.js"),"./ion-segment_2.entry.js":()=>import("./chunk-Q3HA4KTS.js"),"./ion-select-modal.entry.js":()=>import("./chunk-AQ7CK5N7.js"),"./ion-select_3.entry.js":()=>import("./chunk-WANUO4SQ.js"),"./ion-spinner.entry.js":()=>import("./chunk-PXN56W2Y.js"),"./ion-split-pane.entry.js":()=>import("./chunk-F5IPJOZY.js"),"./ion-tab-bar_2.entry.js":()=>import("./chunk-HVE4PZK7.js"),"./ion-tab_2.entry.js":()=>import("./chunk-N7NIM3KD.js"),"./ion-text.entry.js":()=>import("./chunk-7AJILR5U.js"),"./ion-textarea.entry.js":()=>import("./chunk-4IJXL3TR.js"),"./ion-toast.entry.js":()=>import("./chunk-BXOHDZCE.js"),"./ion-toggle.entry.js":()=>import("./chunk-N2UYXGAQ.js")});var Ze="ionic",j={allRenderFn:!1,appendChildSlotFix:!0,asyncLoading:!0,asyncQueue:!1,attachStyles:!0,cloneNodeFix:!0,cmpDidLoad:!0,cmpDidRender:!0,cmpDidUnload:!1,cmpDidUpdate:!0,cmpShouldUpdate:!1,cmpWillLoad:!0,cmpWillRender:!0,cmpWillUpdate:!1,connectedCallback:!0,constructableCSS:!0,cssAnnotations:!0,devTools:!1,disconnectedCallback:!0,element:!1,event:!0,experimentalScopedSlotChanges:!0,experimentalSlotFixes:!0,formAssociated:!1,hasRenderFn:!0,hostListener:!0,hostListenerTarget:!0,hostListenerTargetBody:!0,hostListenerTargetDocument:!0,hostListenerTargetParent:!1,hostListenerTargetWindow:!0,hotModuleReplacement:!1,hydrateClientSide:!0,hydrateServerSide:!1,hydratedAttribute:!1,hydratedClass:!0,hydratedSelectorName:"hydrated",initializeNextTick:!1,invisiblePrehydration:!0,isDebug:!1,isDev:!1,isTesting:!1,lazyLoad:!0,lifecycle:!0,lifecycleDOMEvents:!1,member:!0,method:!0,mode:!0,observeAttribute:!0,profile:!1,prop:!0,propBoolean:!0,propMutable:!0,propNumber:!0,propString:!0,reflect:!0,scoped:!0,scopedSlotTextContentFix:!0,scriptDataOpts:!1,shadowDelegatesFocus:!0,shadowDom:!0,slot:!0,slotChildNodesFix:!0,slotRelocation:!0,state:!0,style:!0,svg:!0,taskQueue:!0,transformTagName:!1,updatable:!0,vdomAttribute:!0,vdomClass:!0,vdomFunctional:!0,vdomKey:!0,vdomListener:!0,vdomPropOrAttr:!0,vdomRef:!0,vdomRender:!0,vdomStyle:!0,vdomText:!0,vdomXlink:!0,watchCallback:!0},Ve=Object.defineProperty,et=(e,t)=>{for(var s in t)Ve(e,s,{get:t[s],enumerable:!0})},as={isDev:!1,isBrowser:!0,isServer:!1,isTesting:!1},se=new WeakMap,x=e=>se.get(e),$s=(e,t)=>se.set(t.$lazyInstance$=e,t),tt=(e,t)=>{let s={$flags$:0,$hostElement$:e,$cmpMeta$:t,$instanceValues$:new Map};return s.$onInstancePromise$=new Promise(n=>s.$onInstanceResolve$=n),s.$onReadyPromise$=new Promise(n=>s.$onReadyResolve$=n),e["s-p"]=[],e["s-rc"]=[],se.set(e,s)},de=(e,t)=>t in e,C=(e,t)=>(0,console.error)(e,t),fe=new Map,st=(e,t,s)=>{let n=e.$tagName$.replace(/-/g,"_"),r=e.$lazyBundleId$;if(!r)return;let l=fe.get(r);if(l)return l[n];return Je(`./${r}.entry.js`).then(o=>(fe.set(r,o),o[n]),C)},H=new Map,Le=[],nt="r",rt="o",lt="s",it="t",Y="s-id",ot="sty-id",ue="c-id",at="{visibility:hidden}.hydrated{visibility:inherit}",ke="slot-fb{display:contents}slot-fb[hidden]{display:none}",he="http://www.w3.org/1999/xlink",E=typeof window<"u"?window:{},S=E.document||{head:{}},cs=E.HTMLElement||class{},p={$flags$:0,$resourcesUrl$:"",jmp:e=>e(),raf:e=>requestAnimationFrame(e),ael:(e,t,s,n)=>e.addEventListener(t,s,n),rel:(e,t,s,n)=>e.removeEventListener(t,s,n),ce:(e,t)=>new CustomEvent(e,t)},_e=j.shadowDom,$t=(()=>{let e=!1;try{S.addEventListener("e",null,Object.defineProperty({},"passive",{get(){e=!0}}))}catch{}return e})(),ct=e=>Promise.resolve(e),dt=(()=>{try{return new CSSStyleSheet,typeof new CSSStyleSheet().replaceSync=="function"}catch{}return!1})(),G=!1,X=[],Ae=[],Ce=(e,t)=>s=>{e.push(s),G||(G=!0,t&&p.$flags$&4?ne(K):p.raf(K))},pe=e=>{for(let t=0;t<e.length;t++)try{e[t](performance.now())}catch(s){C(s)}e.length=0},K=()=>{pe(X),pe(Ae),(G=X.length>0)&&p.raf(K)},ne=e=>ct().then(e),ds=Ce(X,!1),ft=Ce(Ae,!0),fs=e=>{let t=new URL(e,p.$resourcesUrl$);return t.origin!==E.location.origin?t.href:t.pathname},ve={},ut="http://www.w3.org/2000/svg",ht="http://www.w3.org/1999/xhtml",pt=e=>e!=null,re=e=>(e=typeof e,e==="object"||e==="function");function Ee(e){var t,s,n;return(n=(s=(t=e.head)==null?void 0:t.querySelector('meta[name="csp-nonce"]'))==null?void 0:s.getAttribute("content"))!=null?n:void 0}var vt={};et(vt,{err:()=>we,map:()=>gt,ok:()=>Q,unwrap:()=>yt,unwrapErr:()=>mt});var Q=e=>({isOk:!0,isErr:!1,value:e}),we=e=>({isOk:!1,isErr:!0,value:e});function gt(e,t){if(e.isOk){let s=t(e.value);return s instanceof Promise?s.then(n=>Q(n)):Q(s)}if(e.isErr){let s=e.value;return we(s)}throw"should never get here"}var yt=e=>{if(e.isOk)return e.value;throw e.value},mt=e=>{if(e.isErr)return e.value;throw e.value},N=(e,t="")=>()=>{},St=(e,t)=>()=>{},Ie=(e,t,...s)=>{let n=null,r=null,l=null,o=!1,a=!1,i=[],$=d=>{for(let f=0;f<d.length;f++)n=d[f],Array.isArray(n)?$(n):n!=null&&typeof n!="boolean"&&((o=typeof e!="function"&&!re(n))&&(n=String(n)),o&&a?i[i.length-1].$text$+=n:i.push(o?O(null,n):n),a=o)};if($(s),t){t.key&&(r=t.key),t.name&&(l=t.name);{let d=t.className||t.class;d&&(t.class=typeof d!="object"?d:Object.keys(d).filter(f=>d[f]).join(" "))}}if(typeof e=="function")return e(t===null?{}:t,i,xt);let c=O(e,null);return c.$attrs$=t,i.length>0&&(c.$children$=i),c.$key$=r,c.$name$=l,c},O=(e,t)=>{let s={$flags$:0,$tag$:e,$text$:t,$elm$:null,$children$:null};return s.$attrs$=null,s.$key$=null,s.$name$=null,s},bt={},Tt=e=>e&&e.$tag$===bt,xt={forEach:(e,t)=>e.map(ge).forEach(t),map:(e,t)=>e.map(ge).map(t).map(Nt)},ge=e=>({vattrs:e.$attrs$,vchildren:e.$children$,vkey:e.$key$,vname:e.$name$,vtag:e.$tag$,vtext:e.$text$}),Nt=e=>{if(typeof e.vtag=="function"){let s=$e({},e.vattrs);return e.vkey&&(s.key=e.vkey),e.vname&&(s.name=e.vname),Ie(e.vtag,s,...e.vchildren||[])}let t=O(e.vtag,e.vtext);return t.$attrs$=e.vattrs,t.$children$=e.vchildren,t.$key$=e.vkey,t.$name$=e.vname,t},Lt=(e,t,s,n)=>{let r=N("hydrateClient",t),l=e.shadowRoot,o=[],a=[],i=l?[]:null,$=n.$vnode$=O(t,null);p.$orgLocNodes$||Z(S.body,p.$orgLocNodes$=new Map),e[Y]=s,e.removeAttribute(Y),J($,o,a,i,e,e,s),o.map(c=>{let d=c.$hostId$+"."+c.$nodeId$,f=p.$orgLocNodes$.get(d),u=c.$elm$;f&&_e&&f["s-en"]===""&&f.parentNode.insertBefore(u,f.nextSibling),l||(u["s-hn"]=t,f&&(u["s-ol"]=f,u["s-ol"]["s-nr"]=u)),p.$orgLocNodes$.delete(d)}),l&&i.map(c=>{c&&l.appendChild(c)}),r()},J=(e,t,s,n,r,l,o)=>{let a,i,$,c;if(l.nodeType===1){if(a=l.getAttribute(ue),a&&(i=a.split("."),(i[0]===o||i[0]==="0")&&($={$flags$:0,$hostId$:i[0],$nodeId$:i[1],$depth$:i[2],$index$:i[3],$tag$:l.tagName.toLowerCase(),$elm$:l,$attrs$:null,$children$:null,$key$:null,$name$:null,$text$:null},t.push($),l.removeAttribute(ue),e.$children$||(e.$children$=[]),e.$children$[$.$index$]=$,e=$,n&&$.$depth$==="0"&&(n[$.$index$]=$.$elm$))),l.shadowRoot)for(c=l.shadowRoot.childNodes.length-1;c>=0;c--)J(e,t,s,n,r,l.shadowRoot.childNodes[c],o);for(c=l.childNodes.length-1;c>=0;c--)J(e,t,s,n,r,l.childNodes[c],o)}else if(l.nodeType===8)i=l.nodeValue.split("."),(i[1]===o||i[1]==="0")&&(a=i[0],$={$flags$:0,$hostId$:i[1],$nodeId$:i[2],$depth$:i[3],$index$:i[4],$elm$:l,$attrs$:null,$children$:null,$key$:null,$name$:null,$tag$:null,$text$:null},a===it?($.$elm$=l.nextSibling,$.$elm$&&$.$elm$.nodeType===3&&($.$text$=$.$elm$.textContent,t.push($),l.remove(),e.$children$||(e.$children$=[]),e.$children$[$.$index$]=$,n&&$.$depth$==="0"&&(n[$.$index$]=$.$elm$))):$.$hostId$===o&&(a===lt?($.$tag$="slot",i[5]?l["s-sn"]=$.$name$=i[5]:l["s-sn"]="",l["s-sr"]=!0,n&&($.$elm$=S.createElement($.$tag$),$.$name$&&$.$elm$.setAttribute("name",$.$name$),l.parentNode.insertBefore($.$elm$,l),l.remove(),$.$depth$==="0"&&(n[$.$index$]=$.$elm$)),s.push($),e.$children$||(e.$children$=[]),e.$children$[$.$index$]=$):a===nt&&(n?l.remove():(r["s-cr"]=l,l["s-cn"]=!0))));else if(e&&e.$tag$==="style"){let d=O(null,l.textContent);d.$elm$=l,d.$index$="0",e.$children$=[d]}},Z=(e,t)=>{if(e.nodeType===1){let s=0;if(e.shadowRoot)for(;s<e.shadowRoot.childNodes.length;s++)Z(e.shadowRoot.childNodes[s],t);for(s=0;s<e.childNodes.length;s++)Z(e.childNodes[s],t)}else if(e.nodeType===8){let s=e.nodeValue.split(".");s[0]===rt&&(t.set(s[1]+"."+s[2],e),e.nodeValue="",e["s-en"]=s[3])}},kt=e=>Le.map(t=>t(e)).find(t=>!!t),us=e=>Le.push(e),hs=e=>x(e).$modeName$,_t=(e,t)=>e!=null&&!re(e)?t&4?e==="false"?!1:e===""||!!e:t&2?parseFloat(e):t&1?String(e):e:e,At=e=>x(e).$hostElement$,ps=(e,t,s)=>{let n=At(e);return{emit:r=>je(n,t,{bubbles:!!(s&4),composed:!!(s&2),cancelable:!!(s&1),detail:r})}},je=(e,t,s)=>{let n=p.ce(t,s);return e.dispatchEvent(n),n},ye=new WeakMap,Ct=(e,t,s)=>{let n=H.get(e);dt&&s?(n=n||new CSSStyleSheet,typeof n=="string"?n=t:n.replaceSync(t)):n=t,H.set(e,n)},Oe=(e,t,s)=>{var n;let r=De(t,s),l=H.get(r);if(e=e.nodeType===11?e:S,l)if(typeof l=="string"){e=e.head||e;let o=ye.get(e),a;if(o||ye.set(e,o=new Set),!o.has(r)){if(e.host&&(a=e.querySelector(`[${ot}="${r}"]`)))a.innerHTML=l;else{a=S.createElement("style"),a.innerHTML=l;let i=(n=p.$nonce$)!=null?n:Ee(S);i!=null&&a.setAttribute("nonce",i),(!(t.$flags$&1)||t.$flags$&1&&e.nodeName!=="HEAD")&&e.insertBefore(a,e.querySelector("link"))}t.$flags$&4&&(a.innerHTML+=ke),o&&o.add(r)}}else e.adoptedStyleSheets.includes(l)||(e.adoptedStyleSheets=[...e.adoptedStyleSheets,l]);return r},Et=e=>{let t=e.$cmpMeta$,s=e.$hostElement$,n=t.$flags$,r=N("attachStyles",t.$tagName$),l=Oe(s.shadowRoot?s.shadowRoot:s.getRootNode(),t,e.$modeName$);n&10&&n&2&&(s["s-sc"]=l,s.classList.add(l+"-h"),n&2&&s.classList.add(l+"-s")),r()},De=(e,t)=>"sc-"+(t&&e.$flags$&32?e.$tagName$+"-"+t:e.$tagName$),me=(e,t,s,n,r,l)=>{if(s!==n){let o=de(e,t),a=t.toLowerCase();if(t==="class"){let i=e.classList,$=Se(s),c=Se(n);i.remove(...$.filter(d=>d&&!c.includes(d))),i.add(...c.filter(d=>d&&!$.includes(d)))}else if(t==="style"){for(let i in s)(!n||n[i]==null)&&(i.includes("-")?e.style.removeProperty(i):e.style[i]="");for(let i in n)(!s||n[i]!==s[i])&&(i.includes("-")?e.style.setProperty(i,n[i]):e.style[i]=n[i])}else if(t!=="key")if(t==="ref")n&&n(e);else if(!o&&t[0]==="o"&&t[1]==="n"){if(t[2]==="-"?t=t.slice(3):de(E,a)?t=a.slice(2):t=a[2]+t.slice(3),s||n){let i=t.endsWith(Re);t=t.replace(It,""),s&&p.rel(e,t,s,i),n&&p.ael(e,t,n,i)}}else{let i=re(n);if((o||i&&n!==null)&&!r)try{if(e.tagName.includes("-"))e[t]=n;else{let c=n??"";t==="list"?o=!1:(s==null||e[t]!=c)&&(e[t]=c)}}catch{}let $=!1;a!==(a=a.replace(/^xlink\:?/,""))&&(t=a,$=!0),n==null||n===!1?(n!==!1||e.getAttribute(t)==="")&&($?e.removeAttributeNS(he,t):e.removeAttribute(t)):(!o||l&4||r)&&!i&&(n=n===!0?"":n,$?e.setAttributeNS(he,t,n):e.setAttribute(t,n))}}},wt=/\s/,Se=e=>e?e.split(wt):[],Re="Capture",It=new RegExp(Re+"$"),He=(e,t,s)=>{let n=t.$elm$.nodeType===11&&t.$elm$.host?t.$elm$.host:t.$elm$,r=e&&e.$attrs$||ve,l=t.$attrs$||ve;for(let o of be(Object.keys(r)))o in l||me(n,o,r[o],void 0,s,t.$flags$);for(let o of be(Object.keys(l)))me(n,o,r[o],l[o],s,t.$flags$)};function be(e){return e.includes("ref")?[...e.filter(t=>t!=="ref"),"ref"]:e}var w,V,A,P=!1,U=!1,F=!1,b=!1,z=(e,t,s,n)=>{var r;let l=t.$children$[s],o=0,a,i,$;if(P||(F=!0,l.$tag$==="slot"&&(w&&n.classList.add(w+"-s"),l.$flags$|=l.$children$?2:1)),l.$text$!==null)a=l.$elm$=S.createTextNode(l.$text$);else if(l.$flags$&1)a=l.$elm$=S.createTextNode("");else{if(b||(b=l.$tag$==="svg"),a=l.$elm$=S.createElementNS(b?ut:ht,!P&&j.slotRelocation&&l.$flags$&2?"slot-fb":l.$tag$),b&&l.$tag$==="foreignObject"&&(b=!1),He(null,l,b),!!a.getRootNode().querySelector("body")&&j.scoped&&pt(w)&&a["s-si"]!==w&&a.classList.add(a["s-si"]=w),ie(a,n),l.$children$)for(o=0;o<l.$children$.length;++o)i=z(e,l,o,a),i&&a.appendChild(i);l.$tag$==="svg"?b=!1:a.tagName==="foreignObject"&&(b=!0)}return a["s-hn"]=A,l.$flags$&3&&(a["s-sr"]=!0,a["s-cr"]=V,a["s-sn"]=l.$name$||"",a["s-rf"]=(r=l.$attrs$)==null?void 0:r.ref,$=e&&e.$children$&&e.$children$[s],$&&$.$tag$===l.$tag$&&e.$elm$&&Pe(e.$elm$)),a},Pe=e=>{p.$flags$|=1;let t=e.closest(A.toLowerCase());if(t!=null){let s=Array.from(t.childNodes).find(r=>r["s-cr"]),n=Array.from(e.childNodes);for(let r of s?n.reverse():n)r["s-sh"]!=null&&(T(t,r,s??null),r["s-sh"]=void 0,F=!0)}p.$flags$&=-2},B=(e,t)=>{p.$flags$|=1;let s=Array.from(e.childNodes);if(e["s-sr"]&&j.experimentalSlotFixes){let n=e;for(;n=n.nextSibling;)n&&n["s-sn"]===e["s-sn"]&&n["s-sh"]===A&&s.push(n)}for(let n=s.length-1;n>=0;n--){let r=s[n];r["s-hn"]!==A&&r["s-ol"]&&(T(Be(r),r,le(r)),r["s-ol"].remove(),r["s-ol"]=void 0,r["s-sh"]=void 0,F=!0),t&&B(r,t)}p.$flags$&=-2},Ue=(e,t,s,n,r,l)=>{let o=e["s-cr"]&&e["s-cr"].parentNode||e,a;for(o.shadowRoot&&o.tagName===A&&(o=o.shadowRoot);r<=l;++r)n[r]&&(a=z(null,s,r,e),a&&(n[r].$elm$=a,T(o,a,le(t))))},ze=(e,t,s)=>{for(let n=t;n<=s;++n){let r=e[n];if(r){let l=r.$elm$;Me(r),l&&(U=!0,l["s-ol"]?l["s-ol"].remove():B(l,!0),l.remove())}}},jt=(e,t,s,n,r=!1)=>{let l=0,o=0,a=0,i=0,$=t.length-1,c=t[0],d=t[$],f=n.length-1,u=n[0],h=n[f],g,m;for(;l<=$&&o<=f;)if(c==null)c=t[++l];else if(d==null)d=t[--$];else if(u==null)u=n[++o];else if(h==null)h=n[--f];else if(R(c,u,r))I(c,u,r),c=t[++l],u=n[++o];else if(R(d,h,r))I(d,h,r),d=t[--$],h=n[--f];else if(R(c,h,r))(c.$tag$==="slot"||h.$tag$==="slot")&&B(c.$elm$.parentNode,!1),I(c,h,r),T(e,c.$elm$,d.$elm$.nextSibling),c=t[++l],h=n[--f];else if(R(d,u,r))(c.$tag$==="slot"||h.$tag$==="slot")&&B(d.$elm$.parentNode,!1),I(d,u,r),T(e,d.$elm$,c.$elm$),d=t[--$],u=n[++o];else{for(a=-1,i=l;i<=$;++i)if(t[i]&&t[i].$key$!==null&&t[i].$key$===u.$key$){a=i;break}a>=0?(m=t[a],m.$tag$!==u.$tag$?g=z(t&&t[o],s,a,e):(I(m,u,r),t[a]=void 0,g=m.$elm$),u=n[++o]):(g=z(t&&t[o],s,o,e),u=n[++o]),g&&T(Be(c.$elm$),g,le(c.$elm$))}l>$?Ue(e,n[f+1]==null?null:n[f+1].$elm$,s,n,o,f):o>f&&ze(t,l,$)},R=(e,t,s=!1)=>e.$tag$===t.$tag$?e.$tag$==="slot"?"$nodeId$"in e&&s&&e.$elm$.nodeType!==8?!1:e.$name$===t.$name$:s?!0:e.$key$===t.$key$:!1,le=e=>e&&e["s-ol"]||e,Be=e=>(e["s-ol"]?e["s-ol"]:e).parentNode,I=(e,t,s=!1)=>{let n=t.$elm$=e.$elm$,r=e.$children$,l=t.$children$,o=t.$tag$,a=t.$text$,i;a===null?(b=o==="svg"?!0:o==="foreignObject"?!1:b,o==="slot"&&!P?e.$name$!==t.$name$&&(t.$elm$["s-sn"]=t.$name$||"",Pe(t.$elm$.parentElement)):He(e,t,b),r!==null&&l!==null?jt(n,r,t,l,s):l!==null?(e.$text$!==null&&(n.textContent=""),Ue(n,null,t,l,0,l.length-1)):!s&&j.updatable&&r!==null&&ze(r,0,r.length-1),b&&o==="svg"&&(b=!1)):(i=n["s-cr"])?i.parentNode.textContent=a:e.$text$!==a&&(n.data=a)},M=e=>{let t=e.childNodes;for(let s of t)if(s.nodeType===1){if(s["s-sr"]){let n=s["s-sn"];s.hidden=!1;for(let r of t)if(r!==s){if(r["s-hn"]!==s["s-hn"]||n!==""){if(r.nodeType===1&&(n===r.getAttribute("slot")||n===r["s-sn"])||r.nodeType===3&&n===r["s-sn"]){s.hidden=!0;break}}else if(r.nodeType===1||r.nodeType===3&&r.textContent.trim()!==""){s.hidden=!0;break}}}M(s)}},k=[],Fe=e=>{let t,s,n;for(let r of e.childNodes){if(r["s-sr"]&&(t=r["s-cr"])&&t.parentNode){s=t.parentNode.childNodes;let l=r["s-sn"];for(n=s.length-1;n>=0;n--)if(t=s[n],!t["s-cn"]&&!t["s-nr"]&&t["s-hn"]!==r["s-hn"]&&(!t["s-sh"]||t["s-sh"]!==r["s-hn"]))if(Te(t,l)){let o=k.find(a=>a.$nodeToRelocate$===t);U=!0,t["s-sn"]=t["s-sn"]||l,o?(o.$nodeToRelocate$["s-sh"]=r["s-hn"],o.$slotRefNode$=r):(t["s-sh"]=r["s-hn"],k.push({$slotRefNode$:r,$nodeToRelocate$:t})),t["s-sr"]&&k.map(a=>{Te(a.$nodeToRelocate$,t["s-sn"])&&(o=k.find(i=>i.$nodeToRelocate$===t),o&&!a.$slotRefNode$&&(a.$slotRefNode$=o.$slotRefNode$))})}else k.some(o=>o.$nodeToRelocate$===t)||k.push({$nodeToRelocate$:t})}r.nodeType===1&&Fe(r)}},Te=(e,t)=>e.nodeType===1?e.getAttribute("slot")===null&&t===""||e.getAttribute("slot")===t:e["s-sn"]===t?!0:t==="",Me=e=>{e.$attrs$&&e.$attrs$.ref&&e.$attrs$.ref(null),e.$children$&&e.$children$.map(Me)},T=(e,t,s)=>{let n=e?.insertBefore(t,s);return ie(t,e),n},qe=e=>{let t=[];return e&&t.push(...e["s-scs"]||[],e["s-si"],e["s-sc"],...qe(e.parentElement)),t},ie=(e,t,s=!1)=>{var n;if(e&&t&&e.nodeType===1){let r=new Set(qe(t).filter(Boolean));if(r.size&&((n=e.classList)==null||n.add(...e["s-scs"]=[...r]),e["s-ol"]||s))for(let l of Array.from(e.childNodes))ie(l,e,!0)}},Ot=(e,t,s=!1)=>{var n,r,l,o,a;let i=e.$hostElement$,$=e.$cmpMeta$,c=e.$vnode$||O(null,null),d=Tt(t)?t:Ie(null,null,t);if(A=i.tagName,$.$attrsToReflect$&&(d.$attrs$=d.$attrs$||{},$.$attrsToReflect$.map(([f,u])=>d.$attrs$[u]=i[f])),s&&d.$attrs$)for(let f of Object.keys(d.$attrs$))i.hasAttribute(f)&&!["key","ref","style","class"].includes(f)&&(d.$attrs$[f]=i[f]);d.$tag$=null,d.$flags$|=4,e.$vnode$=d,d.$elm$=c.$elm$=i.shadowRoot||i,w=i["s-sc"],P=($.$flags$&1)!==0,V=i["s-cr"],U=!1,I(c,d,s);{if(p.$flags$|=1,F){Fe(d.$elm$);for(let f of k){let u=f.$nodeToRelocate$;if(!u["s-ol"]){let h=S.createTextNode("");h["s-nr"]=u,T(u.parentNode,u["s-ol"]=h,u)}}for(let f of k){let u=f.$nodeToRelocate$,h=f.$slotRefNode$;if(h){let g=h.parentNode,m=h.nextSibling;if(m&&m.nodeType===1){let v=(n=u["s-ol"])==null?void 0:n.previousSibling;for(;v;){let y=(r=v["s-nr"])!=null?r:null;if(y&&y["s-sn"]===u["s-sn"]&&g===y.parentNode){for(y=y.nextSibling;y===u||y?.["s-sr"];)y=y?.nextSibling;if(!y||!y["s-nr"]){m=y;break}}v=v.previousSibling}}(!m&&g!==u.parentNode||u.nextSibling!==m)&&u!==m&&(T(g,u,m),u.nodeType===1&&(u.hidden=(l=u["s-ih"])!=null?l:!1)),u&&typeof h["s-rf"]=="function"&&h["s-rf"](u)}else u.nodeType===1&&(s&&(u["s-ih"]=(o=u.hidden)!=null?o:!1),u.hidden=!0)}}U&&M(d.$elm$),p.$flags$&=-2,k.length=0}if($.$flags$&2)for(let f of d.$elm$.childNodes)f["s-hn"]!==A&&!f["s-sh"]&&(s&&f["s-ih"]==null&&(f["s-ih"]=(a=f.hidden)!=null?a:!1),f.hidden=!0);V=void 0},We=(e,t)=>{t&&!e.$onRenderResolve$&&t["s-p"]&&t["s-p"].push(new Promise(s=>e.$onRenderResolve$=s))},q=(e,t)=>{if(e.$flags$|=16,e.$flags$&4){e.$flags$|=512;return}return We(e,e.$ancestorComponent$),ft(()=>Dt(e,t))},Dt=(e,t)=>{let s=e.$hostElement$,n=N("scheduleUpdate",e.$cmpMeta$.$tagName$),r=e.$lazyInstance$;if(!r)throw new Error(`Can't render component <${s.tagName.toLowerCase()} /> with invalid Stencil runtime! Make sure this imported component is compiled with a \`externalRuntime: true\` flag. For more information, please refer to https://stenciljs.com/docs/custom-elements#externalruntime`);let l;return t&&(e.$flags$|=256,e.$queuedListeners$&&(e.$queuedListeners$.map(([o,a])=>_(r,o,a)),e.$queuedListeners$=void 0),l=_(r,"componentWillLoad")),l=xe(l,()=>_(r,"componentWillRender")),n(),xe(l,()=>Ht(e,r,t))},xe=(e,t)=>Rt(e)?e.then(t).catch(s=>{console.error(s),t()}):t(),Rt=e=>e instanceof Promise||e&&e.then&&typeof e.then=="function",Ht=(e,t,s)=>D(void 0,null,function*(){var n;let r=e.$hostElement$,l=N("update",e.$cmpMeta$.$tagName$),o=r["s-rc"];s&&Et(e);let a=N("render",e.$cmpMeta$.$tagName$);Pt(e,t,r,s),o&&(o.map(i=>i()),r["s-rc"]=void 0),a(),l();{let i=(n=r["s-p"])!=null?n:[],$=()=>Ut(e);i.length===0?$():(Promise.all(i).then($),e.$flags$|=4,i.length=0)}}),Pt=(e,t,s,n)=>{try{t=t.render&&t.render(),e.$flags$&=-17,e.$flags$|=2,Ot(e,t,n)}catch(r){C(r,e.$hostElement$)}return null},Ut=e=>{let t=e.$cmpMeta$.$tagName$,s=e.$hostElement$,n=N("postUpdate",t),r=e.$lazyInstance$,l=e.$ancestorComponent$;_(r,"componentDidRender"),e.$flags$&64?(_(r,"componentDidUpdate"),n()):(e.$flags$|=64,Ge(s),_(r,"componentDidLoad"),n(),e.$onReadyResolve$(s),l||Ye()),e.$onInstanceResolve$(s),e.$onRenderResolve$&&(e.$onRenderResolve$(),e.$onRenderResolve$=void 0),e.$flags$&512&&ne(()=>q(e,!1)),e.$flags$&=-517},vs=e=>{{let t=x(e),s=t.$hostElement$.isConnected;return s&&(t.$flags$&18)===2&&q(t,!1),s}},Ye=e=>{Ge(S.documentElement),ne(()=>je(E,"appload",{detail:{namespace:Ze}}))},_=(e,t,s)=>{if(e&&e[t])try{return e[t](s)}catch(n){C(n)}},Ge=e=>{var t;return e.classList.add((t=j.hydratedSelectorName)!=null?t:"hydrated")},zt=(e,t)=>x(e).$instanceValues$.get(t),Bt=(e,t,s,n)=>{let r=x(e);if(!r)throw new Error(`Couldn't find host element for "${n.$tagName$}" as it is unknown to this Stencil runtime. This usually happens when integrating a 3rd party Stencil component with another Stencil component or application. Please reach out to the maintainers of the 3rd party Stencil component or report this on the Stencil Discord server (https://chat.stenciljs.com) or comment on this similar [GitHub issue](https://github.com/ionic-team/stencil/issues/5457).`);let l=r.$hostElement$,o=r.$instanceValues$.get(t),a=r.$flags$,i=r.$lazyInstance$;s=_t(s,n.$members$[t][0]);let $=Number.isNaN(o)&&Number.isNaN(s),c=s!==o&&!$;if((!(a&8)||o===void 0)&&c&&(r.$instanceValues$.set(t,s),i)){if(n.$watchers$&&a&128){let d=n.$watchers$[t];d&&d.map(f=>{try{i[f](s,o,t)}catch(u){C(u,l)}})}(a&18)===2&&q(r,!1)}},Xe=(e,t,s)=>{var n,r;let l=e.prototype;if(t.$members$||t.$watchers$||e.watchers){e.watchers&&!t.$watchers$&&(t.$watchers$=e.watchers);let o=Object.entries((n=t.$members$)!=null?n:{});if(o.map(([a,[i]])=>{i&31||s&2&&i&32?Object.defineProperty(l,a,{get(){return zt(this,a)},set($){Bt(this,a,$,t)},configurable:!0,enumerable:!0}):s&1&&i&64&&Object.defineProperty(l,a,{value(...$){var c;let d=x(this);return(c=d?.$onInstancePromise$)==null?void 0:c.then(()=>{var f;return(f=d.$lazyInstance$)==null?void 0:f[a](...$)})}})}),s&1){let a=new Map;l.attributeChangedCallback=function(i,$,c){p.jmp(()=>{var d;let f=a.get(i);if(this.hasOwnProperty(f))c=this[f],delete this[f];else{if(l.hasOwnProperty(f)&&typeof this[f]=="number"&&this[f]==c)return;if(f==null){let u=x(this),h=u?.$flags$;if(h&&!(h&8)&&h&128&&c!==$){let g=u.$lazyInstance$,m=(d=t.$watchers$)==null?void 0:d[i];m?.forEach(v=>{g[v]!=null&&g[v].call(g,c,$,i)})}return}}this[f]=c===null&&typeof this[f]=="boolean"?!1:c})},e.observedAttributes=Array.from(new Set([...Object.keys((r=t.$watchers$)!=null?r:{}),...o.filter(([i,$])=>$[0]&15).map(([i,$])=>{var c;let d=$[1]||i;return a.set(d,i),$[0]&512&&((c=t.$attrsToReflect$)==null||c.push([i,d])),d})]))}}return e},Ft=(e,t,s,n)=>D(void 0,null,function*(){let r;if((t.$flags$&32)===0){if(t.$flags$|=32,s.$lazyBundleId$){let i=st(s);if(i&&"then"in i){let c=St();r=yield i,c()}else r=i;if(!r)throw new Error(`Constructor for "${s.$tagName$}#${t.$modeName$}" was not found`);r.isProxied||(s.$watchers$=r.watchers,Xe(r,s,2),r.isProxied=!0);let $=N("createInstance",s.$tagName$);t.$flags$|=8;try{new r(t)}catch(c){C(c)}t.$flags$&=-9,t.$flags$|=128,$(),ee(t.$lazyInstance$)}else{r=e.constructor;let i=e.localName;customElements.whenDefined(i).then(()=>t.$flags$|=128)}if(r&&r.style){let i;typeof r.style=="string"?i=r.style:typeof r.style!="string"&&(t.$modeName$=kt(e),t.$modeName$&&(i=r.style[t.$modeName$]));let $=De(s,t.$modeName$);if(!H.has($)){let c=N("registerStyles",s.$tagName$);Ct($,i,!!(s.$flags$&1)),c()}}}let l=t.$ancestorComponent$,o=()=>q(t,!0);l&&l["s-rc"]?l["s-rc"].push(o):o()}),ee=e=>{_(e,"connectedCallback")},Mt=e=>{if((p.$flags$&1)===0){let t=x(e),s=t.$cmpMeta$,n=N("connectedCallback",s.$tagName$);if(t.$flags$&1)Qe(e,t,s.$listeners$),t?.$lazyInstance$?ee(t.$lazyInstance$):t?.$onReadyPromise$&&t.$onReadyPromise$.then(()=>ee(t.$lazyInstance$));else{t.$flags$|=1;let r;if(r=e.getAttribute(Y),r){if(s.$flags$&1){let l=Oe(e.shadowRoot,s,e.getAttribute("s-mode"));e.classList.remove(l+"-h",l+"-s")}Lt(e,s.$tagName$,r,t)}r||s.$flags$&12&&qt(e);{let l=e;for(;l=l.parentNode||l.host;)if(l.nodeType===1&&l.hasAttribute("s-id")&&l["s-p"]||l["s-p"]){We(t,t.$ancestorComponent$=l);break}}s.$members$&&Object.entries(s.$members$).map(([l,[o]])=>{if(o&31&&e.hasOwnProperty(l)){let a=e[l];delete e[l],e[l]=a}}),Ft(e,t,s)}n()}},qt=e=>{let t=e["s-cr"]=S.createComment("");t["s-cn"]=!0,T(e,t,e.firstChild)},Ne=e=>{_(e,"disconnectedCallback")},Wt=e=>D(void 0,null,function*(){if((p.$flags$&1)===0){let t=x(e);t.$rmListeners$&&(t.$rmListeners$.map(s=>s()),t.$rmListeners$=void 0),t?.$lazyInstance$?Ne(t.$lazyInstance$):t?.$onReadyPromise$&&t.$onReadyPromise$.then(()=>Ne(t.$lazyInstance$))}}),Yt=(e,t)=>{Gt(e),Xt(e),Jt(e),Qt(e),es(e),Zt(e),Vt(e),ts(e),ss(e,t),Kt(e)},Gt=e=>{let t=e.cloneNode;e.cloneNode=function(s){let n=this,r=n.shadowRoot&&_e,l=t.call(n,r?s:!1);if(!r&&s){let o=0,a,i,$=["s-id","s-cr","s-lr","s-rc","s-sc","s-p","s-cn","s-sr","s-sn","s-hn","s-ol","s-nr","s-si","s-rf","s-scs"];for(;o<n.childNodes.length;o++)a=n.childNodes[o]["s-nr"],i=$.every(c=>!n.childNodes[o][c]),a&&(l.__appendChild?l.__appendChild(a.cloneNode(!0)):l.appendChild(a.cloneNode(!0))),i&&l.appendChild(n.childNodes[o].cloneNode(!0))}return l}},Xt=e=>{e.__appendChild=e.appendChild,e.appendChild=function(t){let s=t["s-sn"]=Ke(t),n=W(this.childNodes,s,this.tagName);if(n){let r=oe(n,s),l=r[r.length-1],o=T(l.parentNode,t,l.nextSibling);return M(this),o}return this.__appendChild(t)}},Kt=e=>{e.__removeChild=e.removeChild,e.removeChild=function(t){if(t&&typeof t["s-sn"]<"u"){let s=W(this.childNodes,t["s-sn"],this.tagName);if(s){let r=oe(s,t["s-sn"]).find(l=>l===t);if(r){r.remove(),M(this);return}}}return this.__removeChild(t)}},Qt=e=>{let t=e.prepend;e.prepend=function(...s){s.forEach(n=>{typeof n=="string"&&(n=this.ownerDocument.createTextNode(n));let r=n["s-sn"]=Ke(n),l=W(this.childNodes,r,this.tagName);if(l){let o=document.createTextNode("");o["s-nr"]=n,l["s-cr"].parentNode.__appendChild(o),n["s-ol"]=o;let i=oe(l,r)[0];return T(i.parentNode,n,i.nextSibling)}return n.nodeType===1&&n.getAttribute("slot")&&(n.hidden=!0),t.call(this,n)})}},Jt=e=>{e.append=function(...t){t.forEach(s=>{typeof s=="string"&&(s=this.ownerDocument.createTextNode(s)),this.appendChild(s)})}},Zt=e=>{let t=e.insertAdjacentHTML;e.insertAdjacentHTML=function(s,n){if(s!=="afterbegin"&&s!=="beforeend")return t.call(this,s,n);let r=this.ownerDocument.createElement("_"),l;if(r.innerHTML=n,s==="afterbegin")for(;l=r.firstChild;)this.prepend(l);else if(s==="beforeend")for(;l=r.firstChild;)this.append(l)}},Vt=e=>{e.insertAdjacentText=function(t,s){this.insertAdjacentHTML(t,s)}},es=e=>{let t=e.insertAdjacentElement;e.insertAdjacentElement=function(s,n){return s!=="afterbegin"&&s!=="beforeend"?t.call(this,s,n):s==="afterbegin"?(this.prepend(n),n):(s==="beforeend"&&this.append(n),n)}},ts=e=>{let t=Object.getOwnPropertyDescriptor(Node.prototype,"textContent");Object.defineProperty(e,"__textContent",t),Object.defineProperty(e,"textContent",{get(){return" "+te(this.childNodes).map(r=>{var l,o;let a=[],i=r.nextSibling;for(;i&&i["s-sn"]===r["s-sn"];)(i.nodeType===3||i.nodeType===1)&&a.push((o=(l=i.textContent)==null?void 0:l.trim())!=null?o:""),i=i.nextSibling;return a.filter($=>$!=="").join(" ")}).filter(r=>r!=="").join(" ")+" "},set(s){te(this.childNodes).forEach(r=>{let l=r.nextSibling;for(;l&&l["s-sn"]===r["s-sn"];){let o=l;l=l.nextSibling,o.remove()}if(r["s-sn"]===""){let o=this.ownerDocument.createTextNode(s);o["s-sn"]="",T(r.parentElement,o,r.nextSibling)}else r.remove()})}})},ss=(e,t)=>{class s extends Array{item(r){return this[r]}}if(t.$flags$&8){let n=e.__lookupGetter__("childNodes");Object.defineProperty(e,"children",{get(){return this.childNodes.map(r=>r.nodeType===1)}}),Object.defineProperty(e,"childElementCount",{get(){return e.children.length}}),Object.defineProperty(e,"childNodes",{get(){let r=n.call(this);if((p.$flags$&1)===0&&x(this).$flags$&2){let l=new s;for(let o=0;o<r.length;o++){let a=r[o]["s-nr"];a&&l.push(a)}return l}return s.from(r)}})}},te=e=>{let t=[];for(let s of Array.from(e))s["s-sr"]&&t.push(s),t.push(...te(s.childNodes));return t},Ke=e=>e["s-sn"]||e.nodeType===1&&e.getAttribute("slot")||"",W=(e,t,s)=>{let n=0,r;for(;n<e.length;n++)if(r=e[n],r["s-sr"]&&r["s-sn"]===t&&r["s-hn"]===s||(r=W(r.childNodes,t,s),r))return r;return null},oe=(e,t)=>{let s=[e];for(;(e=e.nextSibling)&&e["s-sn"]===t;)s.push(e);return s},gs=(e,t={})=>{var s;let n=N(),r=[],l=t.exclude||[],o=E.customElements,a=S.head,i=a.querySelector("meta[charset]"),$=S.createElement("style"),c=[],d,f=!0;Object.assign(p,t),p.$resourcesUrl$=new URL(t.resourcesUrl||"./",S.baseURI).href,p.$flags$|=2;let u=!1;if(e.map(h=>{h[1].map(g=>{var m;let v={$flags$:g[0],$tagName$:g[1],$members$:g[2],$listeners$:g[3]};v.$flags$&4&&(u=!0),v.$members$=g[2],v.$listeners$=g[3],v.$attrsToReflect$=[],v.$watchers$=(m=g[4])!=null?m:{};let y=v.$tagName$,ae=class extends HTMLElement{constructor(L){if(super(L),this.hasRegisteredEventListeners=!1,L=this,tt(L,v),v.$flags$&1){if(!L.shadowRoot)L.attachShadow({mode:"open",delegatesFocus:!!(v.$flags$&16)});else if(L.shadowRoot.mode!=="open")throw new Error(`Unable to re-use existing shadow root for ${v.$tagName$}! Mode is set to ${L.shadowRoot.mode} but Stencil only supports open shadow roots.`)}}connectedCallback(){let L=x(this);this.hasRegisteredEventListeners||(this.hasRegisteredEventListeners=!0,Qe(this,L,v.$listeners$)),d&&(clearTimeout(d),d=null),f?c.push(this):p.jmp(()=>Mt(this))}disconnectedCallback(){p.jmp(()=>Wt(this))}componentOnReady(){return x(this).$onReadyPromise$}};v.$flags$&2&&Yt(ae.prototype,v),v.$lazyBundleId$=h[0],!l.includes(y)&&!o.get(y)&&(r.push(y),o.define(y,Xe(ae,v,1)))})}),r.length>0&&(u&&($.textContent+=ke),$.textContent+=r.sort()+at,$.innerHTML.length)){$.setAttribute("data-styles","");let h=(s=p.$nonce$)!=null?s:Ee(S);h!=null&&$.setAttribute("nonce",h),a.insertBefore($,i?i.nextSibling:a.firstChild)}f=!1,c.length?c.map(h=>h.connectedCallback()):p.jmp(()=>d=setTimeout(Ye,30)),n()},Qe=(e,t,s,n)=>{s&&s.map(([r,l,o])=>{let a=rs(e,r),i=ns(t,o),$=ls(r);p.ael(a,l,i,$),(t.$rmListeners$=t.$rmListeners$||[]).push(()=>p.rel(a,l,i,$))})},ns=(e,t)=>s=>{var n;try{e.$flags$&256?(n=e.$lazyInstance$)==null||n[t](s):(e.$queuedListeners$=e.$queuedListeners$||[]).push([t,s])}catch(r){C(r)}},rs=(e,t)=>t&4?S:t&8?E:t&16?S.body:e,ls=e=>$t?{passive:(e&1)!==0,capture:(e&2)!==0}:(e&2)!==0;export{as as a,$s as b,ds as c,ft as d,fs as e,Ie as f,bt as g,us as h,hs as i,At as j,ps as k,vs as l,gs as m};
