{"version": 3, "sources": ["../../../../../../node_modules/@stencil/core/internal/client/shadow-css.js"], "sourcesContent": ["// src/utils/regular-expression.ts\nvar escapeRegExpSpecialCharacters = text => {\n  return text.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n};\n\n// src/utils/shadow-css.ts\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n *\n * This file is a port of shadowCSS from `webcomponents.js` to TypeScript.\n * https://github.com/webcomponents/webcomponentsjs/blob/4efecd7e0e/src/ShadowCSS/ShadowCSS.js\n * https://github.com/angular/angular/blob/master/packages/compiler/src/shadow_css.ts\n */\nvar safeSelector = selector => {\n  const placeholders = [];\n  let index = 0;\n  selector = selector.replace(/(\\[[^\\]]*\\])/g, (_, keep) => {\n    const replaceBy = `__ph-${index}__`;\n    placeholders.push(keep);\n    index++;\n    return replaceBy;\n  });\n  const content = selector.replace(/(:nth-[-\\w]+)(\\([^)]+\\))/g, (_, pseudo, exp) => {\n    const replaceBy = `__ph-${index}__`;\n    placeholders.push(exp);\n    index++;\n    return pseudo + replaceBy;\n  });\n  const ss = {\n    content,\n    placeholders\n  };\n  return ss;\n};\nvar restoreSafeSelector = (placeholders, content) => {\n  return content.replace(/__ph-(\\d+)__/g, (_, index) => placeholders[+index]);\n};\nvar _polyfillHost = \"-shadowcsshost\";\nvar _polyfillSlotted = \"-shadowcssslotted\";\nvar _polyfillHostContext = \"-shadowcsscontext\";\nvar _parenSuffix = \")(?:\\\\(((?:\\\\([^)(]*\\\\)|[^)(]*)+?)\\\\))?([^,{]*)\";\nvar _cssColonHostRe = new RegExp(\"(\" + _polyfillHost + _parenSuffix, \"gim\");\nvar _cssColonHostContextRe = new RegExp(\"(\" + _polyfillHostContext + _parenSuffix, \"gim\");\nvar _cssColonSlottedRe = new RegExp(\"(\" + _polyfillSlotted + _parenSuffix, \"gim\");\nvar _polyfillHostNoCombinator = _polyfillHost + \"-no-combinator\";\nvar _polyfillHostNoCombinatorRe = /-shadowcsshost-no-combinator([^\\s]*)/;\nvar _shadowDOMSelectorsRe = [/::shadow/g, /::content/g];\nvar _selectorReSuffix = \"([>\\\\s~+[.,{:][\\\\s\\\\S]*)?$\";\nvar _polyfillHostRe = /-shadowcsshost/gim;\nvar createSupportsRuleRe = selector => new RegExp(`((?<!(^@supports(.*)))|(?<={.*))(${selector}\\\\b)`, \"gim\");\nvar _colonSlottedRe = createSupportsRuleRe(\"::slotted\");\nvar _colonHostRe = createSupportsRuleRe(\":host\");\nvar _colonHostContextRe = createSupportsRuleRe(\":host-context\");\nvar _commentRe = /\\/\\*\\s*[\\s\\S]*?\\*\\//g;\nvar stripComments = input => {\n  return input.replace(_commentRe, \"\");\n};\nvar _commentWithHashRe = /\\/\\*\\s*#\\s*source(Mapping)?URL=[\\s\\S]+?\\*\\//g;\nvar extractCommentsWithHash = input => {\n  return input.match(_commentWithHashRe) || [];\n};\nvar _ruleRe = /(\\s*)([^;\\{\\}]+?)(\\s*)((?:{%BLOCK%}?\\s*;?)|(?:\\s*;))/g;\nvar _curlyRe = /([{}])/g;\nvar _selectorPartsRe = /(^.*?[^\\\\])??((:+)(.*)|$)/;\nvar OPEN_CURLY = \"{\";\nvar CLOSE_CURLY = \"}\";\nvar BLOCK_PLACEHOLDER = \"%BLOCK%\";\nvar processRules = (input, ruleCallback) => {\n  const inputWithEscapedBlocks = escapeBlocks(input);\n  let nextBlockIndex = 0;\n  return inputWithEscapedBlocks.escapedString.replace(_ruleRe, (...m) => {\n    const selector = m[2];\n    let content = \"\";\n    let suffix = m[4];\n    let contentPrefix = \"\";\n    if (suffix && suffix.startsWith(\"{\" + BLOCK_PLACEHOLDER)) {\n      content = inputWithEscapedBlocks.blocks[nextBlockIndex++];\n      suffix = suffix.substring(BLOCK_PLACEHOLDER.length + 1);\n      contentPrefix = \"{\";\n    }\n    const cssRule = {\n      selector,\n      content\n    };\n    const rule = ruleCallback(cssRule);\n    return `${m[1]}${rule.selector}${m[3]}${contentPrefix}${rule.content}${suffix}`;\n  });\n};\nvar escapeBlocks = input => {\n  const inputParts = input.split(_curlyRe);\n  const resultParts = [];\n  const escapedBlocks = [];\n  let bracketCount = 0;\n  let currentBlockParts = [];\n  for (let partIndex = 0; partIndex < inputParts.length; partIndex++) {\n    const part = inputParts[partIndex];\n    if (part === CLOSE_CURLY) {\n      bracketCount--;\n    }\n    if (bracketCount > 0) {\n      currentBlockParts.push(part);\n    } else {\n      if (currentBlockParts.length > 0) {\n        escapedBlocks.push(currentBlockParts.join(\"\"));\n        resultParts.push(BLOCK_PLACEHOLDER);\n        currentBlockParts = [];\n      }\n      resultParts.push(part);\n    }\n    if (part === OPEN_CURLY) {\n      bracketCount++;\n    }\n  }\n  if (currentBlockParts.length > 0) {\n    escapedBlocks.push(currentBlockParts.join(\"\"));\n    resultParts.push(BLOCK_PLACEHOLDER);\n  }\n  const strEscapedBlocks = {\n    escapedString: resultParts.join(\"\"),\n    blocks: escapedBlocks\n  };\n  return strEscapedBlocks;\n};\nvar insertPolyfillHostInCssText = cssText => {\n  cssText = cssText.replace(_colonHostContextRe, `$1${_polyfillHostContext}`).replace(_colonHostRe, `$1${_polyfillHost}`).replace(_colonSlottedRe, `$1${_polyfillSlotted}`);\n  return cssText;\n};\nvar convertColonRule = (cssText, regExp, partReplacer) => {\n  return cssText.replace(regExp, (...m) => {\n    if (m[2]) {\n      const parts = m[2].split(\",\");\n      const r = [];\n      for (let i = 0; i < parts.length; i++) {\n        const p = parts[i].trim();\n        if (!p) break;\n        r.push(partReplacer(_polyfillHostNoCombinator, p, m[3]));\n      }\n      return r.join(\",\");\n    } else {\n      return _polyfillHostNoCombinator + m[3];\n    }\n  });\n};\nvar colonHostPartReplacer = (host, part, suffix) => {\n  return host + part.replace(_polyfillHost, \"\") + suffix;\n};\nvar convertColonHost = cssText => {\n  return convertColonRule(cssText, _cssColonHostRe, colonHostPartReplacer);\n};\nvar colonHostContextPartReplacer = (host, part, suffix) => {\n  if (part.indexOf(_polyfillHost) > -1) {\n    return colonHostPartReplacer(host, part, suffix);\n  } else {\n    return host + part + suffix + \", \" + part + \" \" + host + suffix;\n  }\n};\nvar convertColonSlotted = (cssText, slotScopeId) => {\n  const slotClass = \".\" + slotScopeId + \" > \";\n  const selectors = [];\n  cssText = cssText.replace(_cssColonSlottedRe, (...m) => {\n    if (m[2]) {\n      const compound = m[2].trim();\n      const suffix = m[3];\n      const slottedSelector = slotClass + compound + suffix;\n      let prefixSelector = \"\";\n      for (let i = m[4] - 1; i >= 0; i--) {\n        const char = m[5][i];\n        if (char === \"}\" || char === \",\") {\n          break;\n        }\n        prefixSelector = char + prefixSelector;\n      }\n      const orgSelector = (prefixSelector + slottedSelector).trim();\n      const addedSelector = `${prefixSelector.trimEnd()}${slottedSelector.trim()}`.trim();\n      if (orgSelector !== addedSelector) {\n        const updatedSelector = `${addedSelector}, ${orgSelector}`;\n        selectors.push({\n          orgSelector,\n          updatedSelector\n        });\n      }\n      return slottedSelector;\n    } else {\n      return _polyfillHostNoCombinator + m[3];\n    }\n  });\n  return {\n    selectors,\n    cssText\n  };\n};\nvar convertColonHostContext = cssText => {\n  return convertColonRule(cssText, _cssColonHostContextRe, colonHostContextPartReplacer);\n};\nvar convertShadowDOMSelectors = cssText => {\n  return _shadowDOMSelectorsRe.reduce((result, pattern) => result.replace(pattern, \" \"), cssText);\n};\nvar makeScopeMatcher = scopeSelector2 => {\n  const lre = /\\[/g;\n  const rre = /\\]/g;\n  scopeSelector2 = scopeSelector2.replace(lre, \"\\\\[\").replace(rre, \"\\\\]\");\n  return new RegExp(\"^(\" + scopeSelector2 + \")\" + _selectorReSuffix, \"m\");\n};\nvar selectorNeedsScoping = (selector, scopeSelector2) => {\n  const re = makeScopeMatcher(scopeSelector2);\n  return !re.test(selector);\n};\nvar injectScopingSelector = (selector, scopingSelector) => {\n  return selector.replace(_selectorPartsRe, (_, before = \"\", _colonGroup, colon = \"\", after = \"\") => {\n    return before + scopingSelector + colon + after;\n  });\n};\nvar applySimpleSelectorScope = (selector, scopeSelector2, hostSelector) => {\n  _polyfillHostRe.lastIndex = 0;\n  if (_polyfillHostRe.test(selector)) {\n    const replaceBy = `.${hostSelector}`;\n    return selector.replace(_polyfillHostNoCombinatorRe, (_, selector2) => injectScopingSelector(selector2, replaceBy)).replace(_polyfillHostRe, replaceBy + \" \");\n  }\n  return scopeSelector2 + \" \" + selector;\n};\nvar applyStrictSelectorScope = (selector, scopeSelector2, hostSelector) => {\n  const isRe = /\\[is=([^\\]]*)\\]/g;\n  scopeSelector2 = scopeSelector2.replace(isRe, (_, ...parts) => parts[0]);\n  const className = \".\" + scopeSelector2;\n  const _scopeSelectorPart = p => {\n    let scopedP = p.trim();\n    if (!scopedP) {\n      return \"\";\n    }\n    if (p.indexOf(_polyfillHostNoCombinator) > -1) {\n      scopedP = applySimpleSelectorScope(p, scopeSelector2, hostSelector);\n    } else {\n      const t = p.replace(_polyfillHostRe, \"\");\n      if (t.length > 0) {\n        scopedP = injectScopingSelector(t, className);\n      }\n    }\n    return scopedP;\n  };\n  const safeContent = safeSelector(selector);\n  selector = safeContent.content;\n  let scopedSelector = \"\";\n  let startIndex = 0;\n  let res;\n  const sep = /( |>|\\+|~(?!=))\\s*/g;\n  const hasHost = selector.indexOf(_polyfillHostNoCombinator) > -1;\n  let shouldScope = !hasHost;\n  while ((res = sep.exec(selector)) !== null) {\n    const separator = res[1];\n    const part2 = selector.slice(startIndex, res.index).trim();\n    shouldScope = shouldScope || part2.indexOf(_polyfillHostNoCombinator) > -1;\n    const scopedPart = shouldScope ? _scopeSelectorPart(part2) : part2;\n    scopedSelector += `${scopedPart} ${separator} `;\n    startIndex = sep.lastIndex;\n  }\n  const part = selector.substring(startIndex);\n  shouldScope = shouldScope || part.indexOf(_polyfillHostNoCombinator) > -1;\n  scopedSelector += shouldScope ? _scopeSelectorPart(part) : part;\n  return restoreSafeSelector(safeContent.placeholders, scopedSelector);\n};\nvar scopeSelector = (selector, scopeSelectorText, hostSelector, slotSelector) => {\n  return selector.split(\",\").map(shallowPart => {\n    if (slotSelector && shallowPart.indexOf(\".\" + slotSelector) > -1) {\n      return shallowPart.trim();\n    }\n    if (selectorNeedsScoping(shallowPart, scopeSelectorText)) {\n      return applyStrictSelectorScope(shallowPart, scopeSelectorText, hostSelector).trim();\n    } else {\n      return shallowPart.trim();\n    }\n  }).join(\", \");\n};\nvar scopeSelectors = (cssText, scopeSelectorText, hostSelector, slotSelector) => {\n  return processRules(cssText, rule => {\n    let selector = rule.selector;\n    let content = rule.content;\n    if (rule.selector[0] !== \"@\") {\n      selector = scopeSelector(rule.selector, scopeSelectorText, hostSelector, slotSelector);\n    } else if (rule.selector.startsWith(\"@media\") || rule.selector.startsWith(\"@supports\") || rule.selector.startsWith(\"@page\") || rule.selector.startsWith(\"@document\")) {\n      content = scopeSelectors(rule.content, scopeSelectorText, hostSelector, slotSelector);\n    }\n    const cssRule = {\n      selector: selector.replace(/\\s{2,}/g, \" \").trim(),\n      content\n    };\n    return cssRule;\n  });\n};\nvar scopeCssText = (cssText, scopeId, hostScopeId, slotScopeId) => {\n  cssText = insertPolyfillHostInCssText(cssText);\n  cssText = convertColonHost(cssText);\n  cssText = convertColonHostContext(cssText);\n  const slotted = convertColonSlotted(cssText, slotScopeId);\n  cssText = slotted.cssText;\n  cssText = convertShadowDOMSelectors(cssText);\n  if (scopeId) {\n    cssText = scopeSelectors(cssText, scopeId, hostScopeId, slotScopeId);\n  }\n  cssText = replaceShadowCssHost(cssText, hostScopeId);\n  cssText = cssText.replace(/>\\s*\\*\\s+([^{, ]+)/gm, \" $1 \");\n  return {\n    cssText: cssText.trim(),\n    // We need to replace the shadow CSS host string in each of these selectors since we created\n    // them prior to the replacement happening in the components CSS text.\n    slottedSelectors: slotted.selectors.map(ref => ({\n      orgSelector: replaceShadowCssHost(ref.orgSelector, hostScopeId),\n      updatedSelector: replaceShadowCssHost(ref.updatedSelector, hostScopeId)\n    }))\n  };\n};\nvar replaceShadowCssHost = (cssText, hostScopeId) => {\n  return cssText.replace(/-shadowcsshost-no-combinator/g, `.${hostScopeId}`);\n};\nvar scopeCss = (cssText, scopeId) => {\n  const hostScopeId = scopeId + \"-h\";\n  const slotScopeId = scopeId + \"-s\";\n  const commentsWithHash = extractCommentsWithHash(cssText);\n  cssText = stripComments(cssText);\n  const scoped = scopeCssText(cssText, scopeId, hostScopeId, slotScopeId);\n  cssText = [scoped.cssText, ...commentsWithHash].join(\"\\n\");\n  scoped.slottedSelectors.forEach(slottedSelector => {\n    const regex = new RegExp(escapeRegExpSpecialCharacters(slottedSelector.orgSelector), \"g\");\n    cssText = cssText.replace(regex, slottedSelector.updatedSelector);\n  });\n  return cssText;\n};\nexport { scopeCss };"], "mappings": ";;;AACA,IAAI,gCAAgC,UAAQ;AAC1C,SAAO,KAAK,QAAQ,uBAAuB,MAAM;AACnD;AAcA,IAAI,eAAe,cAAY;AAC7B,QAAM,eAAe,CAAC;AACtB,MAAI,QAAQ;AACZ,aAAW,SAAS,QAAQ,iBAAiB,CAAC,GAAG,SAAS;AACxD,UAAM,YAAY,QAAQ,KAAK;AAC/B,iBAAa,KAAK,IAAI;AACtB;AACA,WAAO;AAAA,EACT,CAAC;AACD,QAAM,UAAU,SAAS,QAAQ,6BAA6B,CAAC,GAAG,QAAQ,QAAQ;AAChF,UAAM,YAAY,QAAQ,KAAK;AAC/B,iBAAa,KAAK,GAAG;AACrB;AACA,WAAO,SAAS;AAAA,EAClB,CAAC;AACD,QAAM,KAAK;AAAA,IACT;AAAA,IACA;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAI,sBAAsB,CAAC,cAAc,YAAY;AACnD,SAAO,QAAQ,QAAQ,iBAAiB,CAAC,GAAG,UAAU,aAAa,CAAC,KAAK,CAAC;AAC5E;AACA,IAAI,gBAAgB;AACpB,IAAI,mBAAmB;AACvB,IAAI,uBAAuB;AAC3B,IAAI,eAAe;AACnB,IAAI,kBAAkB,IAAI,OAAO,MAAM,gBAAgB,cAAc,KAAK;AAC1E,IAAI,yBAAyB,IAAI,OAAO,MAAM,uBAAuB,cAAc,KAAK;AACxF,IAAI,qBAAqB,IAAI,OAAO,MAAM,mBAAmB,cAAc,KAAK;AAChF,IAAI,4BAA4B,gBAAgB;AAChD,IAAI,8BAA8B;AAClC,IAAI,wBAAwB,CAAC,aAAa,YAAY;AACtD,IAAI,oBAAoB;AACxB,IAAI,kBAAkB;AACtB,IAAI,uBAAuB,cAAY,IAAI,OAAO,oCAAoC,QAAQ,QAAQ,KAAK;AAC3G,IAAI,kBAAkB,qBAAqB,WAAW;AACtD,IAAI,eAAe,qBAAqB,OAAO;AAC/C,IAAI,sBAAsB,qBAAqB,eAAe;AAC9D,IAAI,aAAa;AACjB,IAAI,gBAAgB,WAAS;AAC3B,SAAO,MAAM,QAAQ,YAAY,EAAE;AACrC;AACA,IAAI,qBAAqB;AACzB,IAAI,0BAA0B,WAAS;AACrC,SAAO,MAAM,MAAM,kBAAkB,KAAK,CAAC;AAC7C;AACA,IAAI,UAAU;AACd,IAAI,WAAW;AACf,IAAI,mBAAmB;AACvB,IAAI,aAAa;AACjB,IAAI,cAAc;AAClB,IAAI,oBAAoB;AACxB,IAAI,eAAe,CAAC,OAAO,iBAAiB;AAC1C,QAAM,yBAAyB,aAAa,KAAK;AACjD,MAAI,iBAAiB;AACrB,SAAO,uBAAuB,cAAc,QAAQ,SAAS,IAAI,MAAM;AACrE,UAAM,WAAW,EAAE,CAAC;AACpB,QAAI,UAAU;AACd,QAAI,SAAS,EAAE,CAAC;AAChB,QAAI,gBAAgB;AACpB,QAAI,UAAU,OAAO,WAAW,MAAM,iBAAiB,GAAG;AACxD,gBAAU,uBAAuB,OAAO,gBAAgB;AACxD,eAAS,OAAO,UAAU,kBAAkB,SAAS,CAAC;AACtD,sBAAgB;AAAA,IAClB;AACA,UAAM,UAAU;AAAA,MACd;AAAA,MACA;AAAA,IACF;AACA,UAAM,OAAO,aAAa,OAAO;AACjC,WAAO,GAAG,EAAE,CAAC,CAAC,GAAG,KAAK,QAAQ,GAAG,EAAE,CAAC,CAAC,GAAG,aAAa,GAAG,KAAK,OAAO,GAAG,MAAM;AAAA,EAC/E,CAAC;AACH;AACA,IAAI,eAAe,WAAS;AAC1B,QAAM,aAAa,MAAM,MAAM,QAAQ;AACvC,QAAM,cAAc,CAAC;AACrB,QAAM,gBAAgB,CAAC;AACvB,MAAI,eAAe;AACnB,MAAI,oBAAoB,CAAC;AACzB,WAAS,YAAY,GAAG,YAAY,WAAW,QAAQ,aAAa;AAClE,UAAM,OAAO,WAAW,SAAS;AACjC,QAAI,SAAS,aAAa;AACxB;AAAA,IACF;AACA,QAAI,eAAe,GAAG;AACpB,wBAAkB,KAAK,IAAI;AAAA,IAC7B,OAAO;AACL,UAAI,kBAAkB,SAAS,GAAG;AAChC,sBAAc,KAAK,kBAAkB,KAAK,EAAE,CAAC;AAC7C,oBAAY,KAAK,iBAAiB;AAClC,4BAAoB,CAAC;AAAA,MACvB;AACA,kBAAY,KAAK,IAAI;AAAA,IACvB;AACA,QAAI,SAAS,YAAY;AACvB;AAAA,IACF;AAAA,EACF;AACA,MAAI,kBAAkB,SAAS,GAAG;AAChC,kBAAc,KAAK,kBAAkB,KAAK,EAAE,CAAC;AAC7C,gBAAY,KAAK,iBAAiB;AAAA,EACpC;AACA,QAAM,mBAAmB;AAAA,IACvB,eAAe,YAAY,KAAK,EAAE;AAAA,IAClC,QAAQ;AAAA,EACV;AACA,SAAO;AACT;AACA,IAAI,8BAA8B,aAAW;AAC3C,YAAU,QAAQ,QAAQ,qBAAqB,KAAK,oBAAoB,EAAE,EAAE,QAAQ,cAAc,KAAK,aAAa,EAAE,EAAE,QAAQ,iBAAiB,KAAK,gBAAgB,EAAE;AACxK,SAAO;AACT;AACA,IAAI,mBAAmB,CAAC,SAAS,QAAQ,iBAAiB;AACxD,SAAO,QAAQ,QAAQ,QAAQ,IAAI,MAAM;AACvC,QAAI,EAAE,CAAC,GAAG;AACR,YAAM,QAAQ,EAAE,CAAC,EAAE,MAAM,GAAG;AAC5B,YAAM,IAAI,CAAC;AACX,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,cAAM,IAAI,MAAM,CAAC,EAAE,KAAK;AACxB,YAAI,CAAC,EAAG;AACR,UAAE,KAAK,aAAa,2BAA2B,GAAG,EAAE,CAAC,CAAC,CAAC;AAAA,MACzD;AACA,aAAO,EAAE,KAAK,GAAG;AAAA,IACnB,OAAO;AACL,aAAO,4BAA4B,EAAE,CAAC;AAAA,IACxC;AAAA,EACF,CAAC;AACH;AACA,IAAI,wBAAwB,CAAC,MAAM,MAAM,WAAW;AAClD,SAAO,OAAO,KAAK,QAAQ,eAAe,EAAE,IAAI;AAClD;AACA,IAAI,mBAAmB,aAAW;AAChC,SAAO,iBAAiB,SAAS,iBAAiB,qBAAqB;AACzE;AACA,IAAI,+BAA+B,CAAC,MAAM,MAAM,WAAW;AACzD,MAAI,KAAK,QAAQ,aAAa,IAAI,IAAI;AACpC,WAAO,sBAAsB,MAAM,MAAM,MAAM;AAAA,EACjD,OAAO;AACL,WAAO,OAAO,OAAO,SAAS,OAAO,OAAO,MAAM,OAAO;AAAA,EAC3D;AACF;AACA,IAAI,sBAAsB,CAAC,SAAS,gBAAgB;AAClD,QAAM,YAAY,MAAM,cAAc;AACtC,QAAM,YAAY,CAAC;AACnB,YAAU,QAAQ,QAAQ,oBAAoB,IAAI,MAAM;AACtD,QAAI,EAAE,CAAC,GAAG;AACR,YAAM,WAAW,EAAE,CAAC,EAAE,KAAK;AAC3B,YAAM,SAAS,EAAE,CAAC;AAClB,YAAM,kBAAkB,YAAY,WAAW;AAC/C,UAAI,iBAAiB;AACrB,eAAS,IAAI,EAAE,CAAC,IAAI,GAAG,KAAK,GAAG,KAAK;AAClC,cAAM,OAAO,EAAE,CAAC,EAAE,CAAC;AACnB,YAAI,SAAS,OAAO,SAAS,KAAK;AAChC;AAAA,QACF;AACA,yBAAiB,OAAO;AAAA,MAC1B;AACA,YAAM,eAAe,iBAAiB,iBAAiB,KAAK;AAC5D,YAAM,gBAAgB,GAAG,eAAe,QAAQ,CAAC,GAAG,gBAAgB,KAAK,CAAC,GAAG,KAAK;AAClF,UAAI,gBAAgB,eAAe;AACjC,cAAM,kBAAkB,GAAG,aAAa,KAAK,WAAW;AACxD,kBAAU,KAAK;AAAA,UACb;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT,OAAO;AACL,aAAO,4BAA4B,EAAE,CAAC;AAAA,IACxC;AAAA,EACF,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAI,0BAA0B,aAAW;AACvC,SAAO,iBAAiB,SAAS,wBAAwB,4BAA4B;AACvF;AACA,IAAI,4BAA4B,aAAW;AACzC,SAAO,sBAAsB,OAAO,CAAC,QAAQ,YAAY,OAAO,QAAQ,SAAS,GAAG,GAAG,OAAO;AAChG;AACA,IAAI,mBAAmB,oBAAkB;AACvC,QAAM,MAAM;AACZ,QAAM,MAAM;AACZ,mBAAiB,eAAe,QAAQ,KAAK,KAAK,EAAE,QAAQ,KAAK,KAAK;AACtE,SAAO,IAAI,OAAO,OAAO,iBAAiB,MAAM,mBAAmB,GAAG;AACxE;AACA,IAAI,uBAAuB,CAAC,UAAU,mBAAmB;AACvD,QAAM,KAAK,iBAAiB,cAAc;AAC1C,SAAO,CAAC,GAAG,KAAK,QAAQ;AAC1B;AACA,IAAI,wBAAwB,CAAC,UAAU,oBAAoB;AACzD,SAAO,SAAS,QAAQ,kBAAkB,CAAC,GAAG,SAAS,IAAI,aAAa,QAAQ,IAAI,QAAQ,OAAO;AACjG,WAAO,SAAS,kBAAkB,QAAQ;AAAA,EAC5C,CAAC;AACH;AACA,IAAI,2BAA2B,CAAC,UAAU,gBAAgB,iBAAiB;AACzE,kBAAgB,YAAY;AAC5B,MAAI,gBAAgB,KAAK,QAAQ,GAAG;AAClC,UAAM,YAAY,IAAI,YAAY;AAClC,WAAO,SAAS,QAAQ,6BAA6B,CAAC,GAAG,cAAc,sBAAsB,WAAW,SAAS,CAAC,EAAE,QAAQ,iBAAiB,YAAY,GAAG;AAAA,EAC9J;AACA,SAAO,iBAAiB,MAAM;AAChC;AACA,IAAI,2BAA2B,CAAC,UAAU,gBAAgB,iBAAiB;AACzE,QAAM,OAAO;AACb,mBAAiB,eAAe,QAAQ,MAAM,CAAC,MAAM,UAAU,MAAM,CAAC,CAAC;AACvE,QAAM,YAAY,MAAM;AACxB,QAAM,qBAAqB,OAAK;AAC9B,QAAI,UAAU,EAAE,KAAK;AACrB,QAAI,CAAC,SAAS;AACZ,aAAO;AAAA,IACT;AACA,QAAI,EAAE,QAAQ,yBAAyB,IAAI,IAAI;AAC7C,gBAAU,yBAAyB,GAAG,gBAAgB,YAAY;AAAA,IACpE,OAAO;AACL,YAAM,IAAI,EAAE,QAAQ,iBAAiB,EAAE;AACvC,UAAI,EAAE,SAAS,GAAG;AAChB,kBAAU,sBAAsB,GAAG,SAAS;AAAA,MAC9C;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,cAAc,aAAa,QAAQ;AACzC,aAAW,YAAY;AACvB,MAAI,iBAAiB;AACrB,MAAI,aAAa;AACjB,MAAI;AACJ,QAAM,MAAM;AACZ,QAAM,UAAU,SAAS,QAAQ,yBAAyB,IAAI;AAC9D,MAAI,cAAc,CAAC;AACnB,UAAQ,MAAM,IAAI,KAAK,QAAQ,OAAO,MAAM;AAC1C,UAAM,YAAY,IAAI,CAAC;AACvB,UAAM,QAAQ,SAAS,MAAM,YAAY,IAAI,KAAK,EAAE,KAAK;AACzD,kBAAc,eAAe,MAAM,QAAQ,yBAAyB,IAAI;AACxE,UAAM,aAAa,cAAc,mBAAmB,KAAK,IAAI;AAC7D,sBAAkB,GAAG,UAAU,IAAI,SAAS;AAC5C,iBAAa,IAAI;AAAA,EACnB;AACA,QAAM,OAAO,SAAS,UAAU,UAAU;AAC1C,gBAAc,eAAe,KAAK,QAAQ,yBAAyB,IAAI;AACvE,oBAAkB,cAAc,mBAAmB,IAAI,IAAI;AAC3D,SAAO,oBAAoB,YAAY,cAAc,cAAc;AACrE;AACA,IAAI,gBAAgB,CAAC,UAAU,mBAAmB,cAAc,iBAAiB;AAC/E,SAAO,SAAS,MAAM,GAAG,EAAE,IAAI,iBAAe;AAC5C,QAAI,gBAAgB,YAAY,QAAQ,MAAM,YAAY,IAAI,IAAI;AAChE,aAAO,YAAY,KAAK;AAAA,IAC1B;AACA,QAAI,qBAAqB,aAAa,iBAAiB,GAAG;AACxD,aAAO,yBAAyB,aAAa,mBAAmB,YAAY,EAAE,KAAK;AAAA,IACrF,OAAO;AACL,aAAO,YAAY,KAAK;AAAA,IAC1B;AAAA,EACF,CAAC,EAAE,KAAK,IAAI;AACd;AACA,IAAI,iBAAiB,CAAC,SAAS,mBAAmB,cAAc,iBAAiB;AAC/E,SAAO,aAAa,SAAS,UAAQ;AACnC,QAAI,WAAW,KAAK;AACpB,QAAI,UAAU,KAAK;AACnB,QAAI,KAAK,SAAS,CAAC,MAAM,KAAK;AAC5B,iBAAW,cAAc,KAAK,UAAU,mBAAmB,cAAc,YAAY;AAAA,IACvF,WAAW,KAAK,SAAS,WAAW,QAAQ,KAAK,KAAK,SAAS,WAAW,WAAW,KAAK,KAAK,SAAS,WAAW,OAAO,KAAK,KAAK,SAAS,WAAW,WAAW,GAAG;AACpK,gBAAU,eAAe,KAAK,SAAS,mBAAmB,cAAc,YAAY;AAAA,IACtF;AACA,UAAM,UAAU;AAAA,MACd,UAAU,SAAS,QAAQ,WAAW,GAAG,EAAE,KAAK;AAAA,MAChD;AAAA,IACF;AACA,WAAO;AAAA,EACT,CAAC;AACH;AACA,IAAI,eAAe,CAAC,SAAS,SAAS,aAAa,gBAAgB;AACjE,YAAU,4BAA4B,OAAO;AAC7C,YAAU,iBAAiB,OAAO;AAClC,YAAU,wBAAwB,OAAO;AACzC,QAAM,UAAU,oBAAoB,SAAS,WAAW;AACxD,YAAU,QAAQ;AAClB,YAAU,0BAA0B,OAAO;AAC3C,MAAI,SAAS;AACX,cAAU,eAAe,SAAS,SAAS,aAAa,WAAW;AAAA,EACrE;AACA,YAAU,qBAAqB,SAAS,WAAW;AACnD,YAAU,QAAQ,QAAQ,wBAAwB,MAAM;AACxD,SAAO;AAAA,IACL,SAAS,QAAQ,KAAK;AAAA;AAAA;AAAA,IAGtB,kBAAkB,QAAQ,UAAU,IAAI,UAAQ;AAAA,MAC9C,aAAa,qBAAqB,IAAI,aAAa,WAAW;AAAA,MAC9D,iBAAiB,qBAAqB,IAAI,iBAAiB,WAAW;AAAA,IACxE,EAAE;AAAA,EACJ;AACF;AACA,IAAI,uBAAuB,CAAC,SAAS,gBAAgB;AACnD,SAAO,QAAQ,QAAQ,iCAAiC,IAAI,WAAW,EAAE;AAC3E;AACA,IAAI,WAAW,CAAC,SAAS,YAAY;AACnC,QAAM,cAAc,UAAU;AAC9B,QAAM,cAAc,UAAU;AAC9B,QAAM,mBAAmB,wBAAwB,OAAO;AACxD,YAAU,cAAc,OAAO;AAC/B,QAAM,SAAS,aAAa,SAAS,SAAS,aAAa,WAAW;AACtE,YAAU,CAAC,OAAO,SAAS,GAAG,gBAAgB,EAAE,KAAK,IAAI;AACzD,SAAO,iBAAiB,QAAQ,qBAAmB;AACjD,UAAM,QAAQ,IAAI,OAAO,8BAA8B,gBAAgB,WAAW,GAAG,GAAG;AACxF,cAAU,QAAQ,QAAQ,OAAO,gBAAgB,eAAe;AAAA,EAClE,CAAC;AACD,SAAO;AACT;", "names": []}