import{a as w,d as y,e as x,i as P,m as b,s as I}from"./chunk-QCXYQNJC.js";function A(){if(typeof process>"u"){var n=typeof window<"u"?window:{},r=5e3,e=Date.now(),t=!1;n.document.addEventListener("deviceready",function(){console.log("Ionic Native: deviceready event fired after "+(Date.now()-e)+" ms"),t=!0}),setTimeout(function(){!t&&n.cordova&&console.warn("Ionic Native: deviceready did not fire within "+r+"ms. This can happen when plugins are in an inconsistent state. Try removing plugins from plugins/ and reinstalling them.")},r)}}var T={error:"cordova_not_available"},C={error:"plugin_not_installed"};function p(n){var r=function(){if(Promise)return new Promise(function(u,i){n(u,i)});console.error("No Promise support or polyfill found. To enable Ionic Native support, please add the es6-promise polyfill before this script, or run with a library like Angular or on a recent browser.")};if(typeof window<"u"&&window.angular){var e=window.document,t=window.angular.element(e.querySelector("[ng-app]")||e.body).injector();if(t){var o=t.get("$q");return o(function(u,i){n(u,i)})}console.warn("Angular 1 was detected but $q couldn't be retrieved. This is usually when the app is not bootstrapped on the html or body tag. Falling back to native promises which won't trigger an automatic digest when promises resolve.")}return r()}function F(n,r,e,t){t===void 0&&(t={});var o,u,i=p(function(a,d){t.destruct?o=l(n,r,e,t,function(){for(var v=[],f=0;f<arguments.length;f++)v[f]=arguments[f];return a(v)},function(){for(var v=[],f=0;f<arguments.length;f++)v[f]=arguments[f];return d(v)}):o=l(n,r,e,t,a,d),u=d});return o&&o.error&&(i.catch(function(){}),typeof u=="function"&&u(o.error)),i}function N(n,r,e,t){return t===void 0&&(t={}),p(function(o,u){var i=l(n,r,e,t);i?i.error?u(i.error):i.then&&i.then(o).catch(u):u({error:"unexpected_error"})})}function S(n,r,e,t){return t===void 0&&(t={}),new w(function(o){var u;return t.destruct?u=l(n,r,e,t,function(){for(var i=[],a=0;a<arguments.length;a++)i[a]=arguments[a];return o.next(i)},function(){for(var i=[],a=0;a<arguments.length;a++)i[a]=arguments[a];return o.error(i)}):u=l(n,r,e,t,o.next.bind(o),o.error.bind(o)),u&&u.error&&(o.error(u.error),o.complete()),function(){try{if(t.clearFunction)return t.clearWithArgs?l(n,t.clearFunction,e,t,o.next.bind(o),o.error.bind(o)):l(n,t.clearFunction,[])}catch(i){console.warn("Unable to clear the previous observable watch for",n.constructor.getPluginName(),r),console.warn(i)}}})}function M(n,r){return r=typeof window<"u"&&r?R(window,r):r||(typeof window<"u"?window:{}),P(r,n)}function s(n,r,e){var t,o;typeof n=="string"?t=n:(t=n.constructor.getPluginRef(),e=n.constructor.getPluginName(),o=n.constructor.getPluginInstallName());var u=g(t);return!u||r&&typeof u[r]>"u"?typeof window>"u"||!window.cordova?(D(e,r),T):(q(e,o,r),C):!0}function L(n,r,e,t){if(r===void 0&&(r={}),r.sync)return n;if(r.callbackOrder==="reverse")n.unshift(t),n.unshift(e);else if(r.callbackStyle==="node")n.push(function(a,d){a?t(a):e(d)});else if(r.callbackStyle==="object"&&r.successName&&r.errorName){var o={};o[r.successName]=e,o[r.errorName]=t,n.push(o)}else if(typeof r.successIndex<"u"||typeof r.errorIndex<"u"){var u=function(){r.successIndex>n.length?n[r.successIndex]=e:n.splice(r.successIndex,0,e)},i=function(){r.errorIndex>n.length?n[r.errorIndex]=t:n.splice(r.errorIndex,0,t)};r.successIndex>r.errorIndex?(i(),u()):(u(),i())}else n.push(e),n.push(t);return n}function l(n,r,e,t,o,u){t===void 0&&(t={}),e=L(e,t,o,u);var i=s(n,r);if(i===!0){var a=g(n.constructor.getPluginRef());return a[r].apply(a,e)}else return i}function g(n){return typeof window<"u"?R(window,n):null}function R(n,r){for(var e=r.split("."),t=n,o=0;o<e.length;o++){if(!t)return null;t=t[e[o]]}return t}function q(n,r,e){console.warn(e?"Native: tried calling "+n+"."+e+", but the "+n+" plugin is not installed.":"Native: tried accessing the "+n+" plugin but it's not installed."),r&&console.warn("Install the "+n+" plugin: 'ionic cordova plugin add "+r+"'")}function D(n,r){typeof process>"u"&&console.warn(r?"Native: tried calling "+n+"."+r+", but Cordova is not available. Make sure to include cordova.js or run in a device/simulator":"Native: tried accessing the "+n+" plugin but Cordova is not available. Make sure to include cordova.js or run in a device/simulator")}var m=function(n,r,e){return e===void 0&&(e={}),function(){for(var t=[],o=0;o<arguments.length;o++)t[o]=arguments[o];return e.sync?l(n,r,t,e):e.observable?S(n,r,t,e):e.eventObservable&&e.event?M(e.event,e.element):e.otherPromise?N(n,r,t,e):F(n,r,t,e)}};function k(n,r){for(var e=r.split("."),t=n,o=0;o<e.length;o++){if(!t)return null;t=t[e[o]]}return t}var h=function(){function n(){}return n.installed=function(){var r=s(this.pluginRef)===!0;return r},n.getPlugin=function(){return typeof window<"u"?k(window,this.pluginRef):null},n.getPluginName=function(){var r=this.pluginName;return r},n.getPluginRef=function(){var r=this.pluginRef;return r},n.getPluginInstallName=function(){var r=this.plugin;return r},n.getSupportedPlatforms=function(){var r=this.platforms;return r},n.pluginName="",n.pluginRef="",n.plugin="",n.repo="",n.platforms=[],n.install="",n}();function c(n,r,e,t){return m(n,r,e).apply(this,t)}A();var vr=function(n){y(r,n);function r(){return n!==null&&n.apply(this,arguments)||this}return r.prototype.getAPNSToken=function(){return c(this,"getAPNSToken",{},arguments)},r.prototype.getToken=function(){return c(this,"getToken",{},arguments)},r.prototype.onTokenRefresh=function(){return c(this,"onTokenRefresh",{observable:!0},arguments)},r.prototype.subscribeToTopic=function(e){return c(this,"subscribeToTopic",{},arguments)},r.prototype.unsubscribeFromTopic=function(e){return c(this,"unsubscribeFromTopic",{},arguments)},r.prototype.hasPermission=function(){return c(this,"hasPermission",{},arguments)},r.prototype.onNotification=function(){return c(this,"onNotification",{observable:!0,successIndex:0,errorIndex:2},arguments)},r.prototype.clearAllNotifications=function(){return c(this,"clearAllNotifications",{},arguments)},r.prototype.requestPushPermissionIOS=function(e){return c(this,"requestPushPermissionIOS",{},arguments)},r.prototype.createNotificationChannelAndroid=function(e){return c(this,"createNotificationChannelAndroid",{},arguments)},r.\u0275fac=(()=>{let e;return function(o){return(e||(e=I(r)))(o||r)}})(),r.\u0275prov=b({token:r,factory:r.\u0275fac}),r.pluginName="FCM",r.plugin="cordova-plugin-fcm-with-dependecy-updated",r.pluginRef="FCMPlugin",r.repo="https://github.com/andrehtissot/cordova-plugin-fcm-with-dependecy-updated",r.platforms=["Android","iOS"],r=x([],r),r}(h);export{vr as a};
