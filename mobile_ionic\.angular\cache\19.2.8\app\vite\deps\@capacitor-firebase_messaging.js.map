{"version": 3, "sources": ["../../../../../../node_modules/@capacitor-firebase/messaging/dist/esm/definitions.js", "../../../../../../node_modules/@capacitor-firebase/messaging/dist/esm/index.js"], "sourcesContent": ["/// <reference types=\"@capacitor/cli\" />\n/**\n * The importance level.\n *\n * For more details, see the [Android Developer Docs](https://developer.android.com/reference/android/app/NotificationManager#IMPORTANCE_DEFAULT)\n *\n * @since 1.4.0\n */\nexport var Importance;\n(function (Importance) {\n  /**\n   * @since 1.4.0\n   */\n  Importance[Importance[\"Min\"] = 1] = \"Min\";\n  /**\n   * @since 1.4.0\n   */\n  Importance[Importance[\"Low\"] = 2] = \"Low\";\n  /**\n   * @since 1.4.0\n   */\n  Importance[Importance[\"Default\"] = 3] = \"Default\";\n  /**\n   * @since 1.4.0\n   */\n  Importance[Importance[\"High\"] = 4] = \"High\";\n  /**\n   * @since 1.4.0\n   */\n  Importance[Importance[\"Max\"] = 5] = \"Max\";\n})(Importance || (Importance = {}));\n/**\n * The notification visibility.\n *\n * For more details, see the [Android Developer Docs](https://developer.android.com/reference/androidx/core/app/NotificationCompat#VISIBILITY_PRIVATE())\n *\n * @since 1.4.0\n */\nexport var Visibility;\n(function (Visibility) {\n  /**\n   * @since 1.4.0\n   */\n  Visibility[Visibility[\"Secret\"] = -1] = \"Secret\";\n  /**\n   * @since 1.4.0\n   */\n  Visibility[Visibility[\"Private\"] = 0] = \"Private\";\n  /**\n   * @since 1.4.0\n   */\n  Visibility[Visibility[\"Public\"] = 1] = \"Public\";\n})(Visibility || (Visibility = {}));\n", "import { registerPlugin } from '@capacitor/core';\nconst FirebaseMessaging = registerPlugin('FirebaseMessaging', {\n  web: () => import('./web').then(m => new m.FirebaseMessagingWeb())\n});\nexport * from './definitions';\nexport { FirebaseMessaging };\n"], "mappings": ";;;;;;AAQO,IAAI;AAAA,CACV,SAAUA,aAAY;AAIrB,EAAAA,YAAWA,YAAW,KAAK,IAAI,CAAC,IAAI;AAIpC,EAAAA,YAAWA,YAAW,KAAK,IAAI,CAAC,IAAI;AAIpC,EAAAA,YAAWA,YAAW,SAAS,IAAI,CAAC,IAAI;AAIxC,EAAAA,YAAWA,YAAW,MAAM,IAAI,CAAC,IAAI;AAIrC,EAAAA,YAAWA,YAAW,KAAK,IAAI,CAAC,IAAI;AACtC,GAAG,eAAe,aAAa,CAAC,EAAE;AAQ3B,IAAI;AAAA,CACV,SAAUC,aAAY;AAIrB,EAAAA,YAAWA,YAAW,QAAQ,IAAI,EAAE,IAAI;AAIxC,EAAAA,YAAWA,YAAW,SAAS,IAAI,CAAC,IAAI;AAIxC,EAAAA,YAAWA,YAAW,QAAQ,IAAI,CAAC,IAAI;AACzC,GAAG,eAAe,aAAa,CAAC,EAAE;;;ACnDlC,IAAM,oBAAoB,eAAe,qBAAqB;AAAA,EAC5D,KAAK,MAAM,OAAO,mBAAO,EAAE,KAAK,OAAK,IAAI,EAAE,qBAAqB,CAAC;AACnE,CAAC;", "names": ["Importance", "Visibility"]}