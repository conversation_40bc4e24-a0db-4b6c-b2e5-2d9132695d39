1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="io.ionic.starter"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:20:5-67
11-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:20:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:21:5-79
12-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:21:22-76
13    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
13-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:22:5-79
13-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:22:22-76
14    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
14-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:23:5-81
14-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:23:22-78
15    <uses-permission android:name="android.permission.WAKE_LOCK" />
15-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:24:5-68
15-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:24:22-65
16    <uses-permission android:name="android.permission.VIBRATE" />
16-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:25:5-66
16-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:25:22-63
17    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
17-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:26:5-77
17-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:26:22-74
18    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
18-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:27:5-81
18-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:27:22-78
19    <!-- Required by older versions of Google Play services to create IID tokens -->
20    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
20-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:26:5-82
20-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:26:22-79
21
22    <permission
22-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
23        android:name="io.ionic.starter.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
23-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
24        android:protectionLevel="signature" />
24-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
25
26    <uses-permission android:name="io.ionic.starter.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
26-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
26-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
27
28    <application
28-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:3:5-19:19
29        android:allowBackup="true"
29-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:3:18-44
30        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
30-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
31        android:debuggable="true"
32        android:extractNativeLibs="false"
33        android:icon="@mipmap/alerto_launcher"
33-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:3:45-83
34        android:label="@string/app_name"
34-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:3:84-116
35        android:roundIcon="@mipmap/alerto_launcher_round"
35-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:3:117-166
36        android:supportsRtl="true"
36-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:3:167-193
37        android:theme="@style/AppTheme"
37-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:3:194-225
38        android:usesCleartextTraffic="true" >
38-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:3:226-261
39        <activity
39-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:4:9-9:20
40            android:name="io.ionic.starter.MainActivity"
40-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:4:247-275
41            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|smallestScreenSize|screenLayout|uiMode|navigation"
41-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:4:19-146
42            android:exported="true"
42-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:4:147-170
43            android:label="@string/title_activity_main"
43-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:4:171-214
44            android:launchMode="singleTask"
44-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:4:215-246
45            android:theme="@style/AppTheme.NoActionBarLaunch" >
45-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:4:276-325
46            <intent-filter>
46-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:5:13-8:29
47                <action android:name="android.intent.action.MAIN" />
47-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:6:17-69
47-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:6:25-66
48
49                <category android:name="android.intent.category.LAUNCHER" />
49-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:7:17-77
49-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:7:27-74
50            </intent-filter>
51        </activity>
52
53        <provider
54            android:name="androidx.core.content.FileProvider"
54-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:10:131-180
55            android:authorities="io.ionic.starter.fileprovider"
55-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:10:19-70
56            android:exported="false"
56-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:10:71-95
57            android:grantUriPermissions="true" >
57-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:10:96-130
58            <meta-data
58-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:11:13-112
59                android:name="android.support.FILE_PROVIDER_PATHS"
59-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:11:24-74
60                android:resource="@xml/file_paths" />
60-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:11:75-109
61        </provider>
62
63        <service
63-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:13:9-17:19
64            android:name="io.capawesome.capacitorjs.plugins.firebase.messaging.FirebaseMessagingService"
64-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:13:43-135
65            android:exported="false"
65-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:13:18-42
66            android:stopWithTask="false" >
66-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:13:136-164
67            <intent-filter>
67-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:14:13-16:29
68                <action android:name="com.google.firebase.MESSAGING_EVENT" />
68-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:15:17-78
68-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:15:25-75
69            </intent-filter>
70        </service>
71
72        <meta-data
72-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:18:9-155
73            android:name="com.google.firebase.messaging.default_notification_channel_id"
73-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:18:20-96
74            android:value="@string/default_notification_channel_id" />
74-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:18:97-152
75
76        <service
76-->[:capacitor-firebase-messaging] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:9-16:19
77            android:name="io.capawesome.capacitorjs.plugins.firebase.messaging.MessagingService"
77-->[:capacitor-firebase-messaging] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-97
78            android:exported="false" >
78-->[:capacitor-firebase-messaging] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
79            <intent-filter>
79-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:14:13-16:29
80                <action android:name="com.google.firebase.MESSAGING_EVENT" />
80-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:15:17-78
80-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:15:25-75
81            </intent-filter>
82        </service>
83
84        <receiver android:name="com.capacitorjs.plugins.localnotifications.TimedNotificationPublisher" />
84-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-106
84-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:19-103
85        <receiver android:name="com.capacitorjs.plugins.localnotifications.NotificationDismissReceiver" />
85-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-107
85-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:19-104
86        <receiver
86-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-23:20
87            android:name="com.capacitorjs.plugins.localnotifications.LocalNotificationRestoreReceiver"
87-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-103
88            android:directBootAware="true"
88-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-43
89            android:exported="false" >
89-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-37
90            <intent-filter>
90-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-22:29
91                <action android:name="android.intent.action.LOCKED_BOOT_COMPLETED" />
91-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-86
91-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:25-83
92                <action android:name="android.intent.action.BOOT_COMPLETED" />
92-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-79
92-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:25-76
93                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
93-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:17-82
93-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:25-79
94            </intent-filter>
95        </receiver>
96
97        <service android:name="de.appplant.cordova.plugin.background.ForegroundService" />
97-->[:capacitor-cordova-android-plugins] C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-91
97-->[:capacitor-cordova-android-plugins] C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:18-88
98
99        <receiver
99-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:29:9-40:20
100            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
100-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:30:13-78
101            android:exported="true"
101-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:31:13-36
102            android:permission="com.google.android.c2dm.permission.SEND" >
102-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:32:13-73
103            <intent-filter>
103-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:33:13-35:29
104                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
104-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:34:17-81
104-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:34:25-78
105            </intent-filter>
106
107            <meta-data
107-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:37:13-39:40
108                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
108-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:38:17-92
109                android:value="true" />
109-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:39:17-37
110        </receiver>
111        <!--
112             FirebaseMessagingService performs security checks at runtime,
113             but set to not exported to explicitly avoid allowing another app to call it.
114        -->
115        <service
115-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:46:9-53:19
116            android:name="com.google.firebase.messaging.FirebaseMessagingService"
116-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:47:13-82
117            android:directBootAware="true"
117-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:48:13-43
118            android:exported="false" >
118-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:49:13-37
119            <intent-filter android:priority="-500" >
119-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:14:13-16:29
120                <action android:name="com.google.firebase.MESSAGING_EVENT" />
120-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:15:17-78
120-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:15:25-75
121            </intent-filter>
122        </service>
123        <service
123-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:54:9-63:19
124            android:name="com.google.firebase.components.ComponentDiscoveryService"
124-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:55:13-84
125            android:directBootAware="true"
125-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f546ee03c2308c684eeebc8faf0e1407\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
126            android:exported="false" >
126-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:56:13-37
127            <meta-data
127-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:57:13-59:85
128                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
128-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:58:17-122
129                android:value="com.google.firebase.components.ComponentRegistrar" />
129-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:59:17-82
130            <meta-data
130-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:60:13-62:85
131                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
131-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:61:17-119
132                android:value="com.google.firebase.components.ComponentRegistrar" />
132-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c04d43294058e70b8ad79d5184e7401\transformed\firebase-messaging-24.1.0\AndroidManifest.xml:62:17-82
133            <meta-data
133-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d121929483b8667b8bd7f522bf5e661\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
134                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
134-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d121929483b8667b8bd7f522bf5e661\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
135                android:value="com.google.firebase.components.ComponentRegistrar" />
135-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d121929483b8667b8bd7f522bf5e661\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
136            <meta-data
136-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d121929483b8667b8bd7f522bf5e661\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
137                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
137-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d121929483b8667b8bd7f522bf5e661\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
138                android:value="com.google.firebase.components.ComponentRegistrar" />
138-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d121929483b8667b8bd7f522bf5e661\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
139            <meta-data
139-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\324fc306ed84dc357040da54cc5f1fbc\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
140                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
140-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\324fc306ed84dc357040da54cc5f1fbc\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
141                android:value="com.google.firebase.components.ComponentRegistrar" />
141-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\324fc306ed84dc357040da54cc5f1fbc\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
142            <meta-data
142-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f546ee03c2308c684eeebc8faf0e1407\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
143                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
143-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f546ee03c2308c684eeebc8faf0e1407\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
144                android:value="com.google.firebase.components.ComponentRegistrar" />
144-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f546ee03c2308c684eeebc8faf0e1407\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
145            <meta-data
145-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d59858b3709795c3a4e2c9928bb49778\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
146                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
146-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d59858b3709795c3a4e2c9928bb49778\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
147                android:value="com.google.firebase.components.ComponentRegistrar" />
147-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d59858b3709795c3a4e2c9928bb49778\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
148        </service>
149
150        <activity
150-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3897ee7a3a7e64eb47ff9b7bb8256b24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
151            android:name="com.google.android.gms.common.api.GoogleApiActivity"
151-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3897ee7a3a7e64eb47ff9b7bb8256b24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
152            android:exported="false"
152-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3897ee7a3a7e64eb47ff9b7bb8256b24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
153            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
153-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3897ee7a3a7e64eb47ff9b7bb8256b24\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
154
155        <provider
155-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f546ee03c2308c684eeebc8faf0e1407\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
156            android:name="com.google.firebase.provider.FirebaseInitProvider"
156-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f546ee03c2308c684eeebc8faf0e1407\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
157            android:authorities="io.ionic.starter.firebaseinitprovider"
157-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f546ee03c2308c684eeebc8faf0e1407\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
158            android:directBootAware="true"
158-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f546ee03c2308c684eeebc8faf0e1407\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
159            android:exported="false"
159-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f546ee03c2308c684eeebc8faf0e1407\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
160            android:initOrder="100" />
160-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f546ee03c2308c684eeebc8faf0e1407\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
161        <provider
161-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
162            android:name="androidx.startup.InitializationProvider"
162-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
163            android:authorities="io.ionic.starter.androidx-startup"
163-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
164            android:exported="false" >
164-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
165            <meta-data
165-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
166                android:name="androidx.emoji2.text.EmojiCompatInitializer"
166-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
167                android:value="androidx.startup" />
167-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
168            <meta-data
168-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
169                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
169-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
170                android:value="androidx.startup" />
170-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
171            <meta-data
171-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
172                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
172-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
173                android:value="androidx.startup" />
173-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
174        </provider>
175
176        <meta-data
176-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\697a983ff8b6be23efe7df3e3bbc5a94\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
177            android:name="com.google.android.gms.version"
177-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\697a983ff8b6be23efe7df3e3bbc5a94\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
178            android:value="@integer/google_play_services_version" />
178-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\697a983ff8b6be23efe7df3e3bbc5a94\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
179
180        <receiver
180-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
181            android:name="androidx.profileinstaller.ProfileInstallReceiver"
181-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
182            android:directBootAware="false"
182-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
183            android:enabled="true"
183-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
184            android:exported="true"
184-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
185            android:permission="android.permission.DUMP" >
185-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
186            <intent-filter>
186-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
187                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
187-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
187-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
188            </intent-filter>
189            <intent-filter>
189-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
190                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
190-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
190-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
191            </intent-filter>
192            <intent-filter>
192-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
193                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
193-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
193-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
194            </intent-filter>
195            <intent-filter>
195-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
196                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
196-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
196-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
197            </intent-filter>
198        </receiver>
199
200        <service
200-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c53ce620ea6072f75d375d7efaf4f97b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
201            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
201-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c53ce620ea6072f75d375d7efaf4f97b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
202            android:exported="false" >
202-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c53ce620ea6072f75d375d7efaf4f97b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
203            <meta-data
203-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c53ce620ea6072f75d375d7efaf4f97b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
204                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
204-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c53ce620ea6072f75d375d7efaf4f97b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
205                android:value="cct" />
205-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c53ce620ea6072f75d375d7efaf4f97b\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
206        </service>
207        <service
207-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e166bb1d6f54168e00acc5493c1d998b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
208            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
208-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e166bb1d6f54168e00acc5493c1d998b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
209            android:exported="false"
209-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e166bb1d6f54168e00acc5493c1d998b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
210            android:permission="android.permission.BIND_JOB_SERVICE" >
210-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e166bb1d6f54168e00acc5493c1d998b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
211        </service>
212
213        <receiver
213-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e166bb1d6f54168e00acc5493c1d998b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
214            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
214-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e166bb1d6f54168e00acc5493c1d998b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
215            android:exported="false" />
215-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e166bb1d6f54168e00acc5493c1d998b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
216    </application>
217
218</manifest>
