{"version": 3, "sources": ["../../../../../../node_modules/@capacitor/core/dist/index.js"], "sourcesContent": ["/*! Capacitor: https://capacitorjs.com/ - MIT License */\nvar ExceptionCode;\n(function (ExceptionCode) {\n  /**\n   * API is not implemented.\n   *\n   * This usually means the API can't be used because it is not implemented for\n   * the current platform.\n   */\n  ExceptionCode[\"Unimplemented\"] = \"UNIMPLEMENTED\";\n  /**\n   * API is not available.\n   *\n   * This means the API can't be used right now because:\n   *   - it is currently missing a prerequisite, such as network connectivity\n   *   - it requires a particular platform or browser version\n   */\n  ExceptionCode[\"Unavailable\"] = \"UNAVAILABLE\";\n})(ExceptionCode || (ExceptionCode = {}));\nclass CapacitorException extends Error {\n  constructor(message, code, data) {\n    super(message);\n    this.message = message;\n    this.code = code;\n    this.data = data;\n  }\n}\nconst getPlatformId = win => {\n  var _a, _b;\n  if (win === null || win === void 0 ? void 0 : win.androidBridge) {\n    return 'android';\n  } else if ((_b = (_a = win === null || win === void 0 ? void 0 : win.webkit) === null || _a === void 0 ? void 0 : _a.messageHandlers) === null || _b === void 0 ? void 0 : _b.bridge) {\n    return 'ios';\n  } else {\n    return 'web';\n  }\n};\nconst createCapacitor = win => {\n  const capCustomPlatform = win.CapacitorCustomPlatform || null;\n  const cap = win.Capacitor || {};\n  const Plugins = cap.Plugins = cap.Plugins || {};\n  const getPlatform = () => {\n    return capCustomPlatform !== null ? capCustomPlatform.name : getPlatformId(win);\n  };\n  const isNativePlatform = () => getPlatform() !== 'web';\n  const isPluginAvailable = pluginName => {\n    const plugin = registeredPlugins.get(pluginName);\n    if (plugin === null || plugin === void 0 ? void 0 : plugin.platforms.has(getPlatform())) {\n      // JS implementation available for the current platform.\n      return true;\n    }\n    if (getPluginHeader(pluginName)) {\n      // Native implementation available.\n      return true;\n    }\n    return false;\n  };\n  const getPluginHeader = pluginName => {\n    var _a;\n    return (_a = cap.PluginHeaders) === null || _a === void 0 ? void 0 : _a.find(h => h.name === pluginName);\n  };\n  const handleError = err => win.console.error(err);\n  const registeredPlugins = new Map();\n  const registerPlugin = (pluginName, jsImplementations = {}) => {\n    const registeredPlugin = registeredPlugins.get(pluginName);\n    if (registeredPlugin) {\n      console.warn(`Capacitor plugin \"${pluginName}\" already registered. Cannot register plugins twice.`);\n      return registeredPlugin.proxy;\n    }\n    const platform = getPlatform();\n    const pluginHeader = getPluginHeader(pluginName);\n    let jsImplementation;\n    const loadPluginImplementation = async () => {\n      if (!jsImplementation && platform in jsImplementations) {\n        jsImplementation = typeof jsImplementations[platform] === 'function' ? jsImplementation = await jsImplementations[platform]() : jsImplementation = jsImplementations[platform];\n      } else if (capCustomPlatform !== null && !jsImplementation && 'web' in jsImplementations) {\n        jsImplementation = typeof jsImplementations['web'] === 'function' ? jsImplementation = await jsImplementations['web']() : jsImplementation = jsImplementations['web'];\n      }\n      return jsImplementation;\n    };\n    const createPluginMethod = (impl, prop) => {\n      var _a, _b;\n      if (pluginHeader) {\n        const methodHeader = pluginHeader === null || pluginHeader === void 0 ? void 0 : pluginHeader.methods.find(m => prop === m.name);\n        if (methodHeader) {\n          if (methodHeader.rtype === 'promise') {\n            return options => cap.nativePromise(pluginName, prop.toString(), options);\n          } else {\n            return (options, callback) => cap.nativeCallback(pluginName, prop.toString(), options, callback);\n          }\n        } else if (impl) {\n          return (_a = impl[prop]) === null || _a === void 0 ? void 0 : _a.bind(impl);\n        }\n      } else if (impl) {\n        return (_b = impl[prop]) === null || _b === void 0 ? void 0 : _b.bind(impl);\n      } else {\n        throw new CapacitorException(`\"${pluginName}\" plugin is not implemented on ${platform}`, ExceptionCode.Unimplemented);\n      }\n    };\n    const createPluginMethodWrapper = prop => {\n      let remove;\n      const wrapper = (...args) => {\n        const p = loadPluginImplementation().then(impl => {\n          const fn = createPluginMethod(impl, prop);\n          if (fn) {\n            const p = fn(...args);\n            remove = p === null || p === void 0 ? void 0 : p.remove;\n            return p;\n          } else {\n            throw new CapacitorException(`\"${pluginName}.${prop}()\" is not implemented on ${platform}`, ExceptionCode.Unimplemented);\n          }\n        });\n        if (prop === 'addListener') {\n          p.remove = async () => remove();\n        }\n        return p;\n      };\n      // Some flair ✨\n      wrapper.toString = () => `${prop.toString()}() { [capacitor code] }`;\n      Object.defineProperty(wrapper, 'name', {\n        value: prop,\n        writable: false,\n        configurable: false\n      });\n      return wrapper;\n    };\n    const addListener = createPluginMethodWrapper('addListener');\n    const removeListener = createPluginMethodWrapper('removeListener');\n    const addListenerNative = (eventName, callback) => {\n      const call = addListener({\n        eventName\n      }, callback);\n      const remove = async () => {\n        const callbackId = await call;\n        removeListener({\n          eventName,\n          callbackId\n        }, callback);\n      };\n      const p = new Promise(resolve => call.then(() => resolve({\n        remove\n      })));\n      p.remove = async () => {\n        console.warn(`Using addListener() without 'await' is deprecated.`);\n        await remove();\n      };\n      return p;\n    };\n    const proxy = new Proxy({}, {\n      get(_, prop) {\n        switch (prop) {\n          // https://github.com/facebook/react/issues/20030\n          case '$$typeof':\n            return undefined;\n          case 'toJSON':\n            return () => ({});\n          case 'addListener':\n            return pluginHeader ? addListenerNative : addListener;\n          case 'removeListener':\n            return removeListener;\n          default:\n            return createPluginMethodWrapper(prop);\n        }\n      }\n    });\n    Plugins[pluginName] = proxy;\n    registeredPlugins.set(pluginName, {\n      name: pluginName,\n      proxy,\n      platforms: new Set([...Object.keys(jsImplementations), ...(pluginHeader ? [platform] : [])])\n    });\n    return proxy;\n  };\n  // Add in convertFileSrc for web, it will already be available in native context\n  if (!cap.convertFileSrc) {\n    cap.convertFileSrc = filePath => filePath;\n  }\n  cap.getPlatform = getPlatform;\n  cap.handleError = handleError;\n  cap.isNativePlatform = isNativePlatform;\n  cap.isPluginAvailable = isPluginAvailable;\n  cap.registerPlugin = registerPlugin;\n  cap.Exception = CapacitorException;\n  cap.DEBUG = !!cap.DEBUG;\n  cap.isLoggingEnabled = !!cap.isLoggingEnabled;\n  return cap;\n};\nconst initCapacitorGlobal = win => win.Capacitor = createCapacitor(win);\nconst Capacitor = /*#__PURE__*/initCapacitorGlobal(typeof globalThis !== 'undefined' ? globalThis : typeof self !== 'undefined' ? self : typeof window !== 'undefined' ? window : typeof global !== 'undefined' ? global : {});\nconst registerPlugin = Capacitor.registerPlugin;\n\n/**\n * Base class web plugins should extend.\n */\nclass WebPlugin {\n  constructor() {\n    this.listeners = {};\n    this.retainedEventArguments = {};\n    this.windowListeners = {};\n  }\n  addListener(eventName, listenerFunc) {\n    let firstListener = false;\n    const listeners = this.listeners[eventName];\n    if (!listeners) {\n      this.listeners[eventName] = [];\n      firstListener = true;\n    }\n    this.listeners[eventName].push(listenerFunc);\n    // If we haven't added a window listener for this event and it requires one,\n    // go ahead and add it\n    const windowListener = this.windowListeners[eventName];\n    if (windowListener && !windowListener.registered) {\n      this.addWindowListener(windowListener);\n    }\n    if (firstListener) {\n      this.sendRetainedArgumentsForEvent(eventName);\n    }\n    const remove = async () => this.removeListener(eventName, listenerFunc);\n    const p = Promise.resolve({\n      remove\n    });\n    return p;\n  }\n  async removeAllListeners() {\n    this.listeners = {};\n    for (const listener in this.windowListeners) {\n      this.removeWindowListener(this.windowListeners[listener]);\n    }\n    this.windowListeners = {};\n  }\n  notifyListeners(eventName, data, retainUntilConsumed) {\n    const listeners = this.listeners[eventName];\n    if (!listeners) {\n      if (retainUntilConsumed) {\n        let args = this.retainedEventArguments[eventName];\n        if (!args) {\n          args = [];\n        }\n        args.push(data);\n        this.retainedEventArguments[eventName] = args;\n      }\n      return;\n    }\n    listeners.forEach(listener => listener(data));\n  }\n  hasListeners(eventName) {\n    return !!this.listeners[eventName].length;\n  }\n  registerWindowListener(windowEventName, pluginEventName) {\n    this.windowListeners[pluginEventName] = {\n      registered: false,\n      windowEventName,\n      pluginEventName,\n      handler: event => {\n        this.notifyListeners(pluginEventName, event);\n      }\n    };\n  }\n  unimplemented(msg = 'not implemented') {\n    return new Capacitor.Exception(msg, ExceptionCode.Unimplemented);\n  }\n  unavailable(msg = 'not available') {\n    return new Capacitor.Exception(msg, ExceptionCode.Unavailable);\n  }\n  async removeListener(eventName, listenerFunc) {\n    const listeners = this.listeners[eventName];\n    if (!listeners) {\n      return;\n    }\n    const index = listeners.indexOf(listenerFunc);\n    this.listeners[eventName].splice(index, 1);\n    // If there are no more listeners for this type of event,\n    // remove the window listener\n    if (!this.listeners[eventName].length) {\n      this.removeWindowListener(this.windowListeners[eventName]);\n    }\n  }\n  addWindowListener(handle) {\n    window.addEventListener(handle.windowEventName, handle.handler);\n    handle.registered = true;\n  }\n  removeWindowListener(handle) {\n    if (!handle) {\n      return;\n    }\n    window.removeEventListener(handle.windowEventName, handle.handler);\n    handle.registered = false;\n  }\n  sendRetainedArgumentsForEvent(eventName) {\n    const args = this.retainedEventArguments[eventName];\n    if (!args) {\n      return;\n    }\n    delete this.retainedEventArguments[eventName];\n    args.forEach(arg => {\n      this.notifyListeners(eventName, arg);\n    });\n  }\n}\nconst WebView = /*#__PURE__*/registerPlugin('WebView');\n/******** END WEB VIEW PLUGIN ********/\n/******** COOKIES PLUGIN ********/\n/**\n * Safely web encode a string value (inspired by js-cookie)\n * @param str The string value to encode\n */\nconst encode = str => encodeURIComponent(str).replace(/%(2[346B]|5E|60|7C)/g, decodeURIComponent).replace(/[()]/g, escape);\n/**\n * Safely web decode a string value (inspired by js-cookie)\n * @param str The string value to decode\n */\nconst decode = str => str.replace(/(%[\\dA-F]{2})+/gi, decodeURIComponent);\nclass CapacitorCookiesPluginWeb extends WebPlugin {\n  async getCookies() {\n    const cookies = document.cookie;\n    const cookieMap = {};\n    cookies.split(';').forEach(cookie => {\n      if (cookie.length <= 0) return;\n      // Replace first \"=\" with CAP_COOKIE to prevent splitting on additional \"=\"\n      let [key, value] = cookie.replace(/=/, 'CAP_COOKIE').split('CAP_COOKIE');\n      key = decode(key).trim();\n      value = decode(value).trim();\n      cookieMap[key] = value;\n    });\n    return cookieMap;\n  }\n  async setCookie(options) {\n    try {\n      // Safely Encoded Key/Value\n      const encodedKey = encode(options.key);\n      const encodedValue = encode(options.value);\n      // Clean & sanitize options\n      const expires = `; expires=${(options.expires || '').replace('expires=', '')}`; // Default is \"; expires=\"\n      const path = (options.path || '/').replace('path=', ''); // Default is \"path=/\"\n      const domain = options.url != null && options.url.length > 0 ? `domain=${options.url}` : '';\n      document.cookie = `${encodedKey}=${encodedValue || ''}${expires}; path=${path}; ${domain};`;\n    } catch (error) {\n      return Promise.reject(error);\n    }\n  }\n  async deleteCookie(options) {\n    try {\n      document.cookie = `${options.key}=; Max-Age=0`;\n    } catch (error) {\n      return Promise.reject(error);\n    }\n  }\n  async clearCookies() {\n    try {\n      const cookies = document.cookie.split(';') || [];\n      for (const cookie of cookies) {\n        document.cookie = cookie.replace(/^ +/, '').replace(/=.*/, `=;expires=${new Date().toUTCString()};path=/`);\n      }\n    } catch (error) {\n      return Promise.reject(error);\n    }\n  }\n  async clearAllCookies() {\n    try {\n      await this.clearCookies();\n    } catch (error) {\n      return Promise.reject(error);\n    }\n  }\n}\nconst CapacitorCookies = registerPlugin('CapacitorCookies', {\n  web: () => new CapacitorCookiesPluginWeb()\n});\n// UTILITY FUNCTIONS\n/**\n * Read in a Blob value and return it as a base64 string\n * @param blob The blob value to convert to a base64 string\n */\nconst readBlobAsBase64 = async blob => new Promise((resolve, reject) => {\n  const reader = new FileReader();\n  reader.onload = () => {\n    const base64String = reader.result;\n    // remove prefix \"data:application/pdf;base64,\"\n    resolve(base64String.indexOf(',') >= 0 ? base64String.split(',')[1] : base64String);\n  };\n  reader.onerror = error => reject(error);\n  reader.readAsDataURL(blob);\n});\n/**\n * Normalize an HttpHeaders map by lowercasing all of the values\n * @param headers The HttpHeaders object to normalize\n */\nconst normalizeHttpHeaders = (headers = {}) => {\n  const originalKeys = Object.keys(headers);\n  const loweredKeys = Object.keys(headers).map(k => k.toLocaleLowerCase());\n  const normalized = loweredKeys.reduce((acc, key, index) => {\n    acc[key] = headers[originalKeys[index]];\n    return acc;\n  }, {});\n  return normalized;\n};\n/**\n * Builds a string of url parameters that\n * @param params A map of url parameters\n * @param shouldEncode true if you should encodeURIComponent() the values (true by default)\n */\nconst buildUrlParams = (params, shouldEncode = true) => {\n  if (!params) return null;\n  const output = Object.entries(params).reduce((accumulator, entry) => {\n    const [key, value] = entry;\n    let encodedValue;\n    let item;\n    if (Array.isArray(value)) {\n      item = '';\n      value.forEach(str => {\n        encodedValue = shouldEncode ? encodeURIComponent(str) : str;\n        item += `${key}=${encodedValue}&`;\n      });\n      // last character will always be \"&\" so slice it off\n      item.slice(0, -1);\n    } else {\n      encodedValue = shouldEncode ? encodeURIComponent(value) : value;\n      item = `${key}=${encodedValue}`;\n    }\n    return `${accumulator}&${item}`;\n  }, '');\n  // Remove initial \"&\" from the reduce\n  return output.substr(1);\n};\n/**\n * Build the RequestInit object based on the options passed into the initial request\n * @param options The Http plugin options\n * @param extra Any extra RequestInit values\n */\nconst buildRequestInit = (options, extra = {}) => {\n  const output = Object.assign({\n    method: options.method || 'GET',\n    headers: options.headers\n  }, extra);\n  // Get the content-type\n  const headers = normalizeHttpHeaders(options.headers);\n  const type = headers['content-type'] || '';\n  // If body is already a string, then pass it through as-is.\n  if (typeof options.data === 'string') {\n    output.body = options.data;\n  }\n  // Build request initializers based off of content-type\n  else if (type.includes('application/x-www-form-urlencoded')) {\n    const params = new URLSearchParams();\n    for (const [key, value] of Object.entries(options.data || {})) {\n      params.set(key, value);\n    }\n    output.body = params.toString();\n  } else if (type.includes('multipart/form-data') || options.data instanceof FormData) {\n    const form = new FormData();\n    if (options.data instanceof FormData) {\n      options.data.forEach((value, key) => {\n        form.append(key, value);\n      });\n    } else {\n      for (const key of Object.keys(options.data)) {\n        form.append(key, options.data[key]);\n      }\n    }\n    output.body = form;\n    const headers = new Headers(output.headers);\n    headers.delete('content-type'); // content-type will be set by `window.fetch` to includy boundary\n    output.headers = headers;\n  } else if (type.includes('application/json') || typeof options.data === 'object') {\n    output.body = JSON.stringify(options.data);\n  }\n  return output;\n};\n// WEB IMPLEMENTATION\nclass CapacitorHttpPluginWeb extends WebPlugin {\n  /**\n   * Perform an Http request given a set of options\n   * @param options Options to build the HTTP request\n   */\n  async request(options) {\n    const requestInit = buildRequestInit(options, options.webFetchExtra);\n    const urlParams = buildUrlParams(options.params, options.shouldEncodeUrlParams);\n    const url = urlParams ? `${options.url}?${urlParams}` : options.url;\n    const response = await fetch(url, requestInit);\n    const contentType = response.headers.get('content-type') || '';\n    // Default to 'text' responseType so no parsing happens\n    let {\n      responseType = 'text'\n    } = response.ok ? options : {};\n    // If the response content-type is json, force the response to be json\n    if (contentType.includes('application/json')) {\n      responseType = 'json';\n    }\n    let data;\n    let blob;\n    switch (responseType) {\n      case 'arraybuffer':\n      case 'blob':\n        blob = await response.blob();\n        data = await readBlobAsBase64(blob);\n        break;\n      case 'json':\n        data = await response.json();\n        break;\n      case 'document':\n      case 'text':\n      default:\n        data = await response.text();\n    }\n    // Convert fetch headers to Capacitor HttpHeaders\n    const headers = {};\n    response.headers.forEach((value, key) => {\n      headers[key] = value;\n    });\n    return {\n      data,\n      headers,\n      status: response.status,\n      url: response.url\n    };\n  }\n  /**\n   * Perform an Http GET request given a set of options\n   * @param options Options to build the HTTP request\n   */\n  async get(options) {\n    return this.request(Object.assign(Object.assign({}, options), {\n      method: 'GET'\n    }));\n  }\n  /**\n   * Perform an Http POST request given a set of options\n   * @param options Options to build the HTTP request\n   */\n  async post(options) {\n    return this.request(Object.assign(Object.assign({}, options), {\n      method: 'POST'\n    }));\n  }\n  /**\n   * Perform an Http PUT request given a set of options\n   * @param options Options to build the HTTP request\n   */\n  async put(options) {\n    return this.request(Object.assign(Object.assign({}, options), {\n      method: 'PUT'\n    }));\n  }\n  /**\n   * Perform an Http PATCH request given a set of options\n   * @param options Options to build the HTTP request\n   */\n  async patch(options) {\n    return this.request(Object.assign(Object.assign({}, options), {\n      method: 'PATCH'\n    }));\n  }\n  /**\n   * Perform an Http DELETE request given a set of options\n   * @param options Options to build the HTTP request\n   */\n  async delete(options) {\n    return this.request(Object.assign(Object.assign({}, options), {\n      method: 'DELETE'\n    }));\n  }\n}\nconst CapacitorHttp = registerPlugin('CapacitorHttp', {\n  web: () => new CapacitorHttpPluginWeb()\n});\n/******** END HTTP PLUGIN ********/\n\nexport { Capacitor, CapacitorCookies, CapacitorException, CapacitorHttp, ExceptionCode, WebPlugin, WebView, buildRequestInit, registerPlugin };\n"], "mappings": ";;;;;AACA,IAAI;AAAA,CACH,SAAUA,gBAAe;AAOxB,EAAAA,eAAc,eAAe,IAAI;AAQjC,EAAAA,eAAc,aAAa,IAAI;AACjC,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AACxC,IAAM,qBAAN,cAAiC,MAAM;AAAA,EACrC,YAAY,SAAS,MAAM,MAAM;AAC/B,UAAM,OAAO;AACb,SAAK,UAAU;AACf,SAAK,OAAO;AACZ,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAM,gBAAgB,SAAO;AAC3B,MAAI,IAAI;AACR,MAAI,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,eAAe;AAC/D,WAAO;AAAA,EACT,YAAY,MAAM,KAAK,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,qBAAqB,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ;AACpL,WAAO;AAAA,EACT,OAAO;AACL,WAAO;AAAA,EACT;AACF;AACA,IAAM,kBAAkB,SAAO;AAC7B,QAAM,oBAAoB,IAAI,2BAA2B;AACzD,QAAM,MAAM,IAAI,aAAa,CAAC;AAC9B,QAAM,UAAU,IAAI,UAAU,IAAI,WAAW,CAAC;AAC9C,QAAM,cAAc,MAAM;AACxB,WAAO,sBAAsB,OAAO,kBAAkB,OAAO,cAAc,GAAG;AAAA,EAChF;AACA,QAAM,mBAAmB,MAAM,YAAY,MAAM;AACjD,QAAM,oBAAoB,gBAAc;AACtC,UAAM,SAAS,kBAAkB,IAAI,UAAU;AAC/C,QAAI,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,UAAU,IAAI,YAAY,CAAC,GAAG;AAEvF,aAAO;AAAA,IACT;AACA,QAAI,gBAAgB,UAAU,GAAG;AAE/B,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACA,QAAM,kBAAkB,gBAAc;AACpC,QAAI;AACJ,YAAQ,KAAK,IAAI,mBAAmB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,OAAK,EAAE,SAAS,UAAU;AAAA,EACzG;AACA,QAAM,cAAc,SAAO,IAAI,QAAQ,MAAM,GAAG;AAChD,QAAM,oBAAoB,oBAAI,IAAI;AAClC,QAAMC,kBAAiB,CAAC,YAAY,oBAAoB,CAAC,MAAM;AAC7D,UAAM,mBAAmB,kBAAkB,IAAI,UAAU;AACzD,QAAI,kBAAkB;AACpB,cAAQ,KAAK,qBAAqB,UAAU,sDAAsD;AAClG,aAAO,iBAAiB;AAAA,IAC1B;AACA,UAAM,WAAW,YAAY;AAC7B,UAAM,eAAe,gBAAgB,UAAU;AAC/C,QAAI;AACJ,UAAM,2BAA2B,MAAY;AAC3C,UAAI,CAAC,oBAAoB,YAAY,mBAAmB;AACtD,2BAAmB,OAAO,kBAAkB,QAAQ,MAAM,aAAa,mBAAmB,MAAM,kBAAkB,QAAQ,EAAE,IAAI,mBAAmB,kBAAkB,QAAQ;AAAA,MAC/K,WAAW,sBAAsB,QAAQ,CAAC,oBAAoB,SAAS,mBAAmB;AACxF,2BAAmB,OAAO,kBAAkB,KAAK,MAAM,aAAa,mBAAmB,MAAM,kBAAkB,KAAK,EAAE,IAAI,mBAAmB,kBAAkB,KAAK;AAAA,MACtK;AACA,aAAO;AAAA,IACT;AACA,UAAM,qBAAqB,CAAC,MAAM,SAAS;AACzC,UAAI,IAAI;AACR,UAAI,cAAc;AAChB,cAAM,eAAe,iBAAiB,QAAQ,iBAAiB,SAAS,SAAS,aAAa,QAAQ,KAAK,OAAK,SAAS,EAAE,IAAI;AAC/H,YAAI,cAAc;AAChB,cAAI,aAAa,UAAU,WAAW;AACpC,mBAAO,aAAW,IAAI,cAAc,YAAY,KAAK,SAAS,GAAG,OAAO;AAAA,UAC1E,OAAO;AACL,mBAAO,CAAC,SAAS,aAAa,IAAI,eAAe,YAAY,KAAK,SAAS,GAAG,SAAS,QAAQ;AAAA,UACjG;AAAA,QACF,WAAW,MAAM;AACf,kBAAQ,KAAK,KAAK,IAAI,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI;AAAA,QAC5E;AAAA,MACF,WAAW,MAAM;AACf,gBAAQ,KAAK,KAAK,IAAI,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI;AAAA,MAC5E,OAAO;AACL,cAAM,IAAI,mBAAmB,IAAI,UAAU,kCAAkC,QAAQ,IAAI,cAAc,aAAa;AAAA,MACtH;AAAA,IACF;AACA,UAAM,4BAA4B,UAAQ;AACxC,UAAI;AACJ,YAAM,UAAU,IAAI,SAAS;AAC3B,cAAM,IAAI,yBAAyB,EAAE,KAAK,UAAQ;AAChD,gBAAM,KAAK,mBAAmB,MAAM,IAAI;AACxC,cAAI,IAAI;AACN,kBAAMC,KAAI,GAAG,GAAG,IAAI;AACpB,qBAASA,OAAM,QAAQA,OAAM,SAAS,SAASA,GAAE;AACjD,mBAAOA;AAAA,UACT,OAAO;AACL,kBAAM,IAAI,mBAAmB,IAAI,UAAU,IAAI,IAAI,6BAA6B,QAAQ,IAAI,cAAc,aAAa;AAAA,UACzH;AAAA,QACF,CAAC;AACD,YAAI,SAAS,eAAe;AAC1B,YAAE,SAAS,MAAS;AAAG,0BAAO;AAAA;AAAA,QAChC;AACA,eAAO;AAAA,MACT;AAEA,cAAQ,WAAW,MAAM,GAAG,KAAK,SAAS,CAAC;AAC3C,aAAO,eAAe,SAAS,QAAQ;AAAA,QACrC,OAAO;AAAA,QACP,UAAU;AAAA,QACV,cAAc;AAAA,MAChB,CAAC;AACD,aAAO;AAAA,IACT;AACA,UAAM,cAAc,0BAA0B,aAAa;AAC3D,UAAM,iBAAiB,0BAA0B,gBAAgB;AACjE,UAAM,oBAAoB,CAAC,WAAW,aAAa;AACjD,YAAM,OAAO,YAAY;AAAA,QACvB;AAAA,MACF,GAAG,QAAQ;AACX,YAAM,SAAS,MAAY;AACzB,cAAM,aAAa,MAAM;AACzB,uBAAe;AAAA,UACb;AAAA,UACA;AAAA,QACF,GAAG,QAAQ;AAAA,MACb;AACA,YAAM,IAAI,IAAI,QAAQ,aAAW,KAAK,KAAK,MAAM,QAAQ;AAAA,QACvD;AAAA,MACF,CAAC,CAAC,CAAC;AACH,QAAE,SAAS,MAAY;AACrB,gBAAQ,KAAK,oDAAoD;AACjE,cAAM,OAAO;AAAA,MACf;AACA,aAAO;AAAA,IACT;AACA,UAAM,QAAQ,IAAI,MAAM,CAAC,GAAG;AAAA,MAC1B,IAAI,GAAG,MAAM;AACX,gBAAQ,MAAM;AAAA;AAAA,UAEZ,KAAK;AACH,mBAAO;AAAA,UACT,KAAK;AACH,mBAAO,OAAO,CAAC;AAAA,UACjB,KAAK;AACH,mBAAO,eAAe,oBAAoB;AAAA,UAC5C,KAAK;AACH,mBAAO;AAAA,UACT;AACE,mBAAO,0BAA0B,IAAI;AAAA,QACzC;AAAA,MACF;AAAA,IACF,CAAC;AACD,YAAQ,UAAU,IAAI;AACtB,sBAAkB,IAAI,YAAY;AAAA,MAChC,MAAM;AAAA,MACN;AAAA,MACA,WAAW,oBAAI,IAAI,CAAC,GAAG,OAAO,KAAK,iBAAiB,GAAG,GAAI,eAAe,CAAC,QAAQ,IAAI,CAAC,CAAE,CAAC;AAAA,IAC7F,CAAC;AACD,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,IAAI,gBAAgB;AACvB,QAAI,iBAAiB,cAAY;AAAA,EACnC;AACA,MAAI,cAAc;AAClB,MAAI,cAAc;AAClB,MAAI,mBAAmB;AACvB,MAAI,oBAAoB;AACxB,MAAI,iBAAiBD;AACrB,MAAI,YAAY;AAChB,MAAI,QAAQ,CAAC,CAAC,IAAI;AAClB,MAAI,mBAAmB,CAAC,CAAC,IAAI;AAC7B,SAAO;AACT;AACA,IAAM,sBAAsB,SAAO,IAAI,YAAY,gBAAgB,GAAG;AACtE,IAAM,YAAyB,oBAAoB,OAAO,eAAe,cAAc,aAAa,OAAO,SAAS,cAAc,OAAO,OAAO,WAAW,cAAc,SAAS,OAAO,WAAW,cAAc,SAAS,CAAC,CAAC;AAC7N,IAAM,iBAAiB,UAAU;AAKjC,IAAM,YAAN,MAAgB;AAAA,EACd,cAAc;AACZ,SAAK,YAAY,CAAC;AAClB,SAAK,yBAAyB,CAAC;AAC/B,SAAK,kBAAkB,CAAC;AAAA,EAC1B;AAAA,EACA,YAAY,WAAW,cAAc;AACnC,QAAI,gBAAgB;AACpB,UAAM,YAAY,KAAK,UAAU,SAAS;AAC1C,QAAI,CAAC,WAAW;AACd,WAAK,UAAU,SAAS,IAAI,CAAC;AAC7B,sBAAgB;AAAA,IAClB;AACA,SAAK,UAAU,SAAS,EAAE,KAAK,YAAY;AAG3C,UAAM,iBAAiB,KAAK,gBAAgB,SAAS;AACrD,QAAI,kBAAkB,CAAC,eAAe,YAAY;AAChD,WAAK,kBAAkB,cAAc;AAAA,IACvC;AACA,QAAI,eAAe;AACjB,WAAK,8BAA8B,SAAS;AAAA,IAC9C;AACA,UAAM,SAAS,MAAS;AAAG,kBAAK,eAAe,WAAW,YAAY;AAAA;AACtE,UAAM,IAAI,QAAQ,QAAQ;AAAA,MACxB;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACM,qBAAqB;AAAA;AACzB,WAAK,YAAY,CAAC;AAClB,iBAAW,YAAY,KAAK,iBAAiB;AAC3C,aAAK,qBAAqB,KAAK,gBAAgB,QAAQ,CAAC;AAAA,MAC1D;AACA,WAAK,kBAAkB,CAAC;AAAA,IAC1B;AAAA;AAAA,EACA,gBAAgB,WAAW,MAAM,qBAAqB;AACpD,UAAM,YAAY,KAAK,UAAU,SAAS;AAC1C,QAAI,CAAC,WAAW;AACd,UAAI,qBAAqB;AACvB,YAAI,OAAO,KAAK,uBAAuB,SAAS;AAChD,YAAI,CAAC,MAAM;AACT,iBAAO,CAAC;AAAA,QACV;AACA,aAAK,KAAK,IAAI;AACd,aAAK,uBAAuB,SAAS,IAAI;AAAA,MAC3C;AACA;AAAA,IACF;AACA,cAAU,QAAQ,cAAY,SAAS,IAAI,CAAC;AAAA,EAC9C;AAAA,EACA,aAAa,WAAW;AACtB,WAAO,CAAC,CAAC,KAAK,UAAU,SAAS,EAAE;AAAA,EACrC;AAAA,EACA,uBAAuB,iBAAiB,iBAAiB;AACvD,SAAK,gBAAgB,eAAe,IAAI;AAAA,MACtC,YAAY;AAAA,MACZ;AAAA,MACA;AAAA,MACA,SAAS,WAAS;AAChB,aAAK,gBAAgB,iBAAiB,KAAK;AAAA,MAC7C;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc,MAAM,mBAAmB;AACrC,WAAO,IAAI,UAAU,UAAU,KAAK,cAAc,aAAa;AAAA,EACjE;AAAA,EACA,YAAY,MAAM,iBAAiB;AACjC,WAAO,IAAI,UAAU,UAAU,KAAK,cAAc,WAAW;AAAA,EAC/D;AAAA,EACM,eAAe,WAAW,cAAc;AAAA;AAC5C,YAAM,YAAY,KAAK,UAAU,SAAS;AAC1C,UAAI,CAAC,WAAW;AACd;AAAA,MACF;AACA,YAAM,QAAQ,UAAU,QAAQ,YAAY;AAC5C,WAAK,UAAU,SAAS,EAAE,OAAO,OAAO,CAAC;AAGzC,UAAI,CAAC,KAAK,UAAU,SAAS,EAAE,QAAQ;AACrC,aAAK,qBAAqB,KAAK,gBAAgB,SAAS,CAAC;AAAA,MAC3D;AAAA,IACF;AAAA;AAAA,EACA,kBAAkB,QAAQ;AACxB,WAAO,iBAAiB,OAAO,iBAAiB,OAAO,OAAO;AAC9D,WAAO,aAAa;AAAA,EACtB;AAAA,EACA,qBAAqB,QAAQ;AAC3B,QAAI,CAAC,QAAQ;AACX;AAAA,IACF;AACA,WAAO,oBAAoB,OAAO,iBAAiB,OAAO,OAAO;AACjE,WAAO,aAAa;AAAA,EACtB;AAAA,EACA,8BAA8B,WAAW;AACvC,UAAM,OAAO,KAAK,uBAAuB,SAAS;AAClD,QAAI,CAAC,MAAM;AACT;AAAA,IACF;AACA,WAAO,KAAK,uBAAuB,SAAS;AAC5C,SAAK,QAAQ,SAAO;AAClB,WAAK,gBAAgB,WAAW,GAAG;AAAA,IACrC,CAAC;AAAA,EACH;AACF;AACA,IAAM,UAAuB,eAAe,SAAS;AAOrD,IAAM,SAAS,SAAO,mBAAmB,GAAG,EAAE,QAAQ,wBAAwB,kBAAkB,EAAE,QAAQ,SAAS,MAAM;AAKzH,IAAM,SAAS,SAAO,IAAI,QAAQ,oBAAoB,kBAAkB;AACxE,IAAM,4BAAN,cAAwC,UAAU;AAAA,EAC1C,aAAa;AAAA;AACjB,YAAM,UAAU,SAAS;AACzB,YAAM,YAAY,CAAC;AACnB,cAAQ,MAAM,GAAG,EAAE,QAAQ,YAAU;AACnC,YAAI,OAAO,UAAU,EAAG;AAExB,YAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,KAAK,YAAY,EAAE,MAAM,YAAY;AACvE,cAAM,OAAO,GAAG,EAAE,KAAK;AACvB,gBAAQ,OAAO,KAAK,EAAE,KAAK;AAC3B,kBAAU,GAAG,IAAI;AAAA,MACnB,CAAC;AACD,aAAO;AAAA,IACT;AAAA;AAAA,EACM,UAAU,SAAS;AAAA;AACvB,UAAI;AAEF,cAAM,aAAa,OAAO,QAAQ,GAAG;AACrC,cAAM,eAAe,OAAO,QAAQ,KAAK;AAEzC,cAAM,UAAU,cAAc,QAAQ,WAAW,IAAI,QAAQ,YAAY,EAAE,CAAC;AAC5E,cAAM,QAAQ,QAAQ,QAAQ,KAAK,QAAQ,SAAS,EAAE;AACtD,cAAM,SAAS,QAAQ,OAAO,QAAQ,QAAQ,IAAI,SAAS,IAAI,UAAU,QAAQ,GAAG,KAAK;AACzF,iBAAS,SAAS,GAAG,UAAU,IAAI,gBAAgB,EAAE,GAAG,OAAO,UAAU,IAAI,KAAK,MAAM;AAAA,MAC1F,SAAS,OAAO;AACd,eAAO,QAAQ,OAAO,KAAK;AAAA,MAC7B;AAAA,IACF;AAAA;AAAA,EACM,aAAa,SAAS;AAAA;AAC1B,UAAI;AACF,iBAAS,SAAS,GAAG,QAAQ,GAAG;AAAA,MAClC,SAAS,OAAO;AACd,eAAO,QAAQ,OAAO,KAAK;AAAA,MAC7B;AAAA,IACF;AAAA;AAAA,EACM,eAAe;AAAA;AACnB,UAAI;AACF,cAAM,UAAU,SAAS,OAAO,MAAM,GAAG,KAAK,CAAC;AAC/C,mBAAW,UAAU,SAAS;AAC5B,mBAAS,SAAS,OAAO,QAAQ,OAAO,EAAE,EAAE,QAAQ,OAAO,cAAa,oBAAI,KAAK,GAAE,YAAY,CAAC,SAAS;AAAA,QAC3G;AAAA,MACF,SAAS,OAAO;AACd,eAAO,QAAQ,OAAO,KAAK;AAAA,MAC7B;AAAA,IACF;AAAA;AAAA,EACM,kBAAkB;AAAA;AACtB,UAAI;AACF,cAAM,KAAK,aAAa;AAAA,MAC1B,SAAS,OAAO;AACd,eAAO,QAAQ,OAAO,KAAK;AAAA,MAC7B;AAAA,IACF;AAAA;AACF;AACA,IAAM,mBAAmB,eAAe,oBAAoB;AAAA,EAC1D,KAAK,MAAM,IAAI,0BAA0B;AAC3C,CAAC;AAMD,IAAM,mBAAmB,CAAM,SAAK;AAAG,aAAI,QAAQ,CAAC,SAAS,WAAW;AACtE,UAAM,SAAS,IAAI,WAAW;AAC9B,WAAO,SAAS,MAAM;AACpB,YAAM,eAAe,OAAO;AAE5B,cAAQ,aAAa,QAAQ,GAAG,KAAK,IAAI,aAAa,MAAM,GAAG,EAAE,CAAC,IAAI,YAAY;AAAA,IACpF;AACA,WAAO,UAAU,WAAS,OAAO,KAAK;AACtC,WAAO,cAAc,IAAI;AAAA,EAC3B,CAAC;AAAA;AAKD,IAAM,uBAAuB,CAAC,UAAU,CAAC,MAAM;AAC7C,QAAM,eAAe,OAAO,KAAK,OAAO;AACxC,QAAM,cAAc,OAAO,KAAK,OAAO,EAAE,IAAI,OAAK,EAAE,kBAAkB,CAAC;AACvE,QAAM,aAAa,YAAY,OAAO,CAAC,KAAK,KAAK,UAAU;AACzD,QAAI,GAAG,IAAI,QAAQ,aAAa,KAAK,CAAC;AACtC,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACL,SAAO;AACT;AAMA,IAAM,iBAAiB,CAAC,QAAQ,eAAe,SAAS;AACtD,MAAI,CAAC,OAAQ,QAAO;AACpB,QAAM,SAAS,OAAO,QAAQ,MAAM,EAAE,OAAO,CAAC,aAAa,UAAU;AACnE,UAAM,CAAC,KAAK,KAAK,IAAI;AACrB,QAAI;AACJ,QAAI;AACJ,QAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,aAAO;AACP,YAAM,QAAQ,SAAO;AACnB,uBAAe,eAAe,mBAAmB,GAAG,IAAI;AACxD,gBAAQ,GAAG,GAAG,IAAI,YAAY;AAAA,MAChC,CAAC;AAED,WAAK,MAAM,GAAG,EAAE;AAAA,IAClB,OAAO;AACL,qBAAe,eAAe,mBAAmB,KAAK,IAAI;AAC1D,aAAO,GAAG,GAAG,IAAI,YAAY;AAAA,IAC/B;AACA,WAAO,GAAG,WAAW,IAAI,IAAI;AAAA,EAC/B,GAAG,EAAE;AAEL,SAAO,OAAO,OAAO,CAAC;AACxB;AAMA,IAAM,mBAAmB,CAAC,SAAS,QAAQ,CAAC,MAAM;AAChD,QAAM,SAAS,OAAO,OAAO;AAAA,IAC3B,QAAQ,QAAQ,UAAU;AAAA,IAC1B,SAAS,QAAQ;AAAA,EACnB,GAAG,KAAK;AAER,QAAM,UAAU,qBAAqB,QAAQ,OAAO;AACpD,QAAM,OAAO,QAAQ,cAAc,KAAK;AAExC,MAAI,OAAO,QAAQ,SAAS,UAAU;AACpC,WAAO,OAAO,QAAQ;AAAA,EACxB,WAES,KAAK,SAAS,mCAAmC,GAAG;AAC3D,UAAM,SAAS,IAAI,gBAAgB;AACnC,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,QAAQ,QAAQ,CAAC,CAAC,GAAG;AAC7D,aAAO,IAAI,KAAK,KAAK;AAAA,IACvB;AACA,WAAO,OAAO,OAAO,SAAS;AAAA,EAChC,WAAW,KAAK,SAAS,qBAAqB,KAAK,QAAQ,gBAAgB,UAAU;AACnF,UAAM,OAAO,IAAI,SAAS;AAC1B,QAAI,QAAQ,gBAAgB,UAAU;AACpC,cAAQ,KAAK,QAAQ,CAAC,OAAO,QAAQ;AACnC,aAAK,OAAO,KAAK,KAAK;AAAA,MACxB,CAAC;AAAA,IACH,OAAO;AACL,iBAAW,OAAO,OAAO,KAAK,QAAQ,IAAI,GAAG;AAC3C,aAAK,OAAO,KAAK,QAAQ,KAAK,GAAG,CAAC;AAAA,MACpC;AAAA,IACF;AACA,WAAO,OAAO;AACd,UAAME,WAAU,IAAI,QAAQ,OAAO,OAAO;AAC1C,IAAAA,SAAQ,OAAO,cAAc;AAC7B,WAAO,UAAUA;AAAA,EACnB,WAAW,KAAK,SAAS,kBAAkB,KAAK,OAAO,QAAQ,SAAS,UAAU;AAChF,WAAO,OAAO,KAAK,UAAU,QAAQ,IAAI;AAAA,EAC3C;AACA,SAAO;AACT;AAEA,IAAM,yBAAN,cAAqC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvC,QAAQ,SAAS;AAAA;AACrB,YAAM,cAAc,iBAAiB,SAAS,QAAQ,aAAa;AACnE,YAAM,YAAY,eAAe,QAAQ,QAAQ,QAAQ,qBAAqB;AAC9E,YAAM,MAAM,YAAY,GAAG,QAAQ,GAAG,IAAI,SAAS,KAAK,QAAQ;AAChE,YAAM,WAAW,MAAM,MAAM,KAAK,WAAW;AAC7C,YAAM,cAAc,SAAS,QAAQ,IAAI,cAAc,KAAK;AAE5D,UAAI;AAAA,QACF,eAAe;AAAA,MACjB,IAAI,SAAS,KAAK,UAAU,CAAC;AAE7B,UAAI,YAAY,SAAS,kBAAkB,GAAG;AAC5C,uBAAe;AAAA,MACjB;AACA,UAAI;AACJ,UAAI;AACJ,cAAQ,cAAc;AAAA,QACpB,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,MAAM,SAAS,KAAK;AAC3B,iBAAO,MAAM,iBAAiB,IAAI;AAClC;AAAA,QACF,KAAK;AACH,iBAAO,MAAM,SAAS,KAAK;AAC3B;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AAAA,QACL;AACE,iBAAO,MAAM,SAAS,KAAK;AAAA,MAC/B;AAEA,YAAM,UAAU,CAAC;AACjB,eAAS,QAAQ,QAAQ,CAAC,OAAO,QAAQ;AACvC,gBAAQ,GAAG,IAAI;AAAA,MACjB,CAAC;AACD,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA,QAAQ,SAAS;AAAA,QACjB,KAAK,SAAS;AAAA,MAChB;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKM,IAAI,SAAS;AAAA;AACjB,aAAO,KAAK,QAAQ,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG;AAAA,QAC5D,QAAQ;AAAA,MACV,CAAC,CAAC;AAAA,IACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKM,KAAK,SAAS;AAAA;AAClB,aAAO,KAAK,QAAQ,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG;AAAA,QAC5D,QAAQ;AAAA,MACV,CAAC,CAAC;AAAA,IACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKM,IAAI,SAAS;AAAA;AACjB,aAAO,KAAK,QAAQ,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG;AAAA,QAC5D,QAAQ;AAAA,MACV,CAAC,CAAC;AAAA,IACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKM,MAAM,SAAS;AAAA;AACnB,aAAO,KAAK,QAAQ,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG;AAAA,QAC5D,QAAQ;AAAA,MACV,CAAC,CAAC;AAAA,IACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKM,OAAO,SAAS;AAAA;AACpB,aAAO,KAAK,QAAQ,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG;AAAA,QAC5D,QAAQ;AAAA,MACV,CAAC,CAAC;AAAA,IACJ;AAAA;AACF;AACA,IAAM,gBAAgB,eAAe,iBAAiB;AAAA,EACpD,KAAK,MAAM,IAAI,uBAAuB;AACxC,CAAC;", "names": ["ExceptionCode", "registerPlugin", "p", "headers"]}