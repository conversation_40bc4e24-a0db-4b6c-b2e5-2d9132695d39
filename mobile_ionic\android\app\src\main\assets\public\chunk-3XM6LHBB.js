import{a as G,b as H,c as K}from"./chunk-LEWYLK2X.js";import{a as j}from"./chunk-EOTJZDZP.js";import{a as O}from"./chunk-FULEFYAM.js";import"./chunk-AGHLTJ5J.js";import{$ as k,$a as T,A as b,Ab as B,Cb as U,D as v,Db as V,F as g,G as f,H as C,J as P,M as w,Na as $,O as L,Oa as N,Pa as I,Qa as _,Wa as D,X as x,ab as E,ca as F,ea as A,g as y,p as c,ub as S,vb as R,y as M,zb as z}from"./chunk-QCXYQNJC.js";import"./chunk-6WVAEWPV.js";import"./chunk-HYNAH5QB.js";import"./chunk-5AIHQZWU.js";import"./chunk-4PQ5B4D2.js";import"./chunk-HC6MZPB3.js";import"./chunk-SV2ZKNWA.js";import"./chunk-EPGIQT2W.js";import"./chunk-RS5W3JWO.js";import"./chunk-DY4KE6AI.js";import"./chunk-GIGBYVJT.js";import"./chunk-KGEDUKSE.js";import"./chunk-MCRJI3T3.js";import"./chunk-OBBPMR2I.js";import"./chunk-AMQPVFGX.js";import"./chunk-KKCAABTQ.js";import"./chunk-OFX7WKKZ.js";import"./chunk-F4H6ZFEG.js";import"./chunk-NMYJD6OP.js";import"./chunk-XXJXE6HG.js";import"./chunk-SV7S5NYR.js";import"./chunk-WTCPO44B.js";import"./chunk-QVY4QQUF.js";import"./chunk-2HRRFJKF.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-BAKMWPBW.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import{f as J,g as l}from"./chunk-2R6CW7ES.js";var r=J(K());var lt=(()=>{class m{constructor(){this.userMarker=null,this.evacuationCenters=[],this.userLocation=null,this.newCenterId=null,this.highlightCenter=!1,this.centerLat=null,this.centerLng=null,this.loadingCtrl=c(B),this.toastCtrl=c(U),this.alertCtrl=c(z),this.http=c(k),this.router=c(A),this.route=c(F),this.mapboxRouting=c(H),this.offlineStorage=c(j)}ngOnInit(){console.log("\u{1F535} FLOOD MAP: Component initialized..."),this.route.queryParams.subscribe(t=>{t.newCenterId&&(this.newCenterId=t.newCenterId,this.highlightCenter=t.highlightCenter==="true",this.centerLat=t.centerLat?parseFloat(t.centerLat):null,this.centerLng=t.centerLng?parseFloat(t.centerLng):null,console.log("\u{1F535} FLOOD MAP: New center to highlight:",this.newCenterId))})}ngAfterViewInit(){return l(this,null,function*(){console.log("\u{1F535} FLOOD MAP: View initialized, loading map..."),setTimeout(()=>l(this,null,function*(){yield this.loadFloodMap()}),100)})}loadFloodMap(){return l(this,null,function*(){let t=yield this.loadingCtrl.create({message:"Loading flood evacuation centers...",spinner:"crescent"});yield t.present();try{let o=yield G.getCurrentPosition({enableHighAccuracy:!0,timeout:2e4}),e=o.coords.latitude,n=o.coords.longitude;console.log(`\u{1F535} FLOOD MAP: User location [${e}, ${n}]`),this.userLocation={lat:e,lng:n},this.initializeMap(e,n),yield this.loadFloodCenters(e,n),yield t.dismiss(),yield(yield this.toastCtrl.create({message:`\u{1F535} Showing ${this.evacuationCenters.length} flood evacuation centers`,duration:3e3,color:"primary",position:"top"})).present()}catch(o){yield t.dismiss(),console.error("\u{1F535} FLOOD MAP: Error loading map",o),yield(yield this.alertCtrl.create({header:"Location Error",message:"Unable to get your location. Please enable GPS and try again.",buttons:[{text:"Retry",handler:()=>this.loadFloodMap()},{text:"Go Back",handler:()=>this.router.navigate(["/tabs/home"])}]})).present()}})}initializeMap(t,o){if(console.log(`\u{1F535} FLOOD MAP: Initializing map at [${t}, ${o}]`),!document.getElementById("flood-map"))throw console.error("\u{1F535} FLOOD MAP: Container #flood-map not found!"),new Error("Map container not found. Please ensure the view is properly loaded.");this.map&&this.map.remove(),this.map=r.map("flood-map").setView([t,o],13),r.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{attribution:"OpenStreetMap contributors"}).addTo(this.map),this.userMarker=r.marker([t,o],{icon:r.icon({iconUrl:"assets/Location.png",iconSize:[30,30],iconAnchor:[15,30]})}).addTo(this.map),this.userMarker.bindPopup("\u{1F4CD} You are here!").openPopup()}loadFloodCenters(t,o){return l(this,null,function*(){try{console.log("\u{1F535} FLOOD MAP: Fetching flood centers...");let e=[];if(this.offlineStorage.isOfflineMode()||!navigator.onLine){if(console.log("\u{1F504} Loading flood centers from offline storage"),e=yield this.offlineStorage.getEvacuationCenters(),console.log("\u{1F4F1} OFFLINE DATA:",e),e.length===0){console.warn("\u26A0\uFE0F No cached evacuation centers found"),yield(yield this.alertCtrl.create({header:"No Offline Data",message:"No offline evacuation data available. Please sync data when online.",buttons:["OK"]})).present();return}}else try{e=yield y(this.http.get(`${O.apiUrl}/evacuation-centers`)),console.log("\u{1F535} FLOOD MAP: Total centers received from API:",e?.length||0)}catch(a){if(console.error("\u274C API failed, falling back to offline data:",a),e=yield this.offlineStorage.getEvacuationCenters(),e.length===0){yield(yield this.alertCtrl.create({header:"Connection Error",message:"Cannot connect to server and no offline data available. Please check your connection or sync data when online.",buttons:["OK"]})).present();return}}if(this.evacuationCenters=e.filter(a=>a.disaster_type==="Flood"),console.log(`\u{1F535} FLOOD MAP: Filtered to ${this.evacuationCenters.length} flood centers`),this.evacuationCenters.length===0){yield(yield this.alertCtrl.create({header:"No Flood Centers",message:"No flood evacuation centers found in the database.",buttons:["OK"]})).present();return}let n=this.offlineStorage.isOfflineMode()||!navigator.onLine;if(this.evacuationCenters.forEach(a=>{let i=Number(a.latitude),s=Number(a.longitude);if(!isNaN(i)&&!isNaN(s)){let d=r.marker([i,s],{icon:r.icon({iconUrl:"assets/forFlood.png",iconSize:[40,40],iconAnchor:[20,40],popupAnchor:[0,-40]})}),p=this.calculateDistance(t,o,i,s);d.on("click",()=>{n?this.showOfflineMarkerInfo(a,p):this.showTransportationOptions(a)});let u=this.newCenterId&&a.id.toString()===this.newCenterId,h=n?"<p><em>\u{1F4F1} Offline Mode - Limited functionality</em></p>":"<p><em>Click marker for route options</em></p>";d.bindPopup(`
            <div class="evacuation-popup">
              <h3>\u{1F535} ${a.name} ${u?"\u2B50 NEW!":""}</h3>
              <p><strong>Type:</strong> Flood Center</p>
              <p><strong>Distance:</strong> ${(p/1e3).toFixed(2)} km</p>
              <p><strong>Capacity:</strong> ${a.capacity||"N/A"}</p>
              ${h}
              ${u?"<p><strong>\u{1F195} Recently Added!</strong></p>":""}
            </div>
          `),u&&(d.openPopup(),this.map.setView([i,s],15),this.toastCtrl.create({message:`\u{1F195} New flood evacuation center: ${a.name}`,duration:5e3,color:"primary",position:"top"}).then(q=>q.present())),d.addTo(this.map),console.log(`\u{1F535} Added flood marker: ${a.name}`)}}),n?console.log("\u{1F535} Offline mode: Showing markers only (no routing)"):(console.log("\u{1F535} Online mode: Auto-routing to 2 nearest flood centers..."),yield this.routeToTwoNearestCenters()),this.evacuationCenters.length>0){let a=r.latLngBounds([]);a.extend([t,o]),this.evacuationCenters.forEach(i=>{a.extend([Number(i.latitude),Number(i.longitude)])}),this.map.fitBounds(a,{padding:[50,50]})}}catch(e){console.error("\u{1F535} FLOOD MAP: Error loading centers",e),yield(yield this.toastCtrl.create({message:"Error loading flood centers. Please check your connection.",duration:3e3,color:"danger"})).present()}})}routeToTwoNearestCenters(){return l(this,null,function*(){if(!this.userLocation||this.evacuationCenters.length===0){console.log("\u{1F535} FLOOD MAP: No user location or evacuation centers available");return}try{console.log("\u{1F535} FLOOD MAP: Finding 2 nearest flood centers...");let t=this.getTwoNearestCenters(this.userLocation.lat,this.userLocation.lng);if(t.length===0)return;this.clearRoutes(),yield this.calculateRoutes(t)}catch(t){console.error("\u{1F535} FLOOD MAP: Error calculating routes",t)}})}getTwoNearestCenters(t,o){return[...this.evacuationCenters].sort((n,a)=>{let i=this.calculateDistance(t,o,Number(n.latitude),Number(n.longitude)),s=this.calculateDistance(t,o,Number(a.latitude),Number(a.longitude));return i-s}).slice(0,2)}clearRoutes(){this.map.eachLayer(t=>{t instanceof r.GeoJSON&&this.map.removeLayer(t)})}calculateRoutes(t){return l(this,null,function*(){for(let o of t)yield this.calculateRoute(o,"walking")})}calculateRoute(t,o){return l(this,null,function*(){try{if(!this.userLocation){console.error("\u{1F535} FLOOD MAP: No user location available for routing");return}let e=yield fetch(`https://api.mapbox.com/directions/v5/mapbox/${o}/${this.userLocation.lng},${this.userLocation.lat};${t.longitude},${t.latitude}?geometries=geojson&access_token=${O.mapboxAccessToken}`);if(!e.ok)throw new Error(`HTTP error! status: ${e.status}`);let n=yield e.json();if(n.routes&&n.routes.length>0){let i={type:"Feature",geometry:n.routes[0].geometry,properties:{}};r.geoJSON(i,{style:{color:"#0066CC",weight:4,opacity:.8}}).addTo(this.map),console.log(`\u{1F535} FLOOD MAP: Route added to ${t.name}`)}}catch(e){console.error("\u{1F535} FLOOD MAP: Error calculating route:",e)}})}showOfflineMarkerInfo(t,o){return l(this,null,function*(){yield(yield this.alertCtrl.create({header:`\u{1F4F1} ${t.name}`,message:`
        <div style="text-align: left;">
          <p><strong>Type:</strong> Flood Center</p>
          <p><strong>Distance:</strong> ${(o/1e3).toFixed(2)} km</p>
          <p><strong>Address:</strong> ${t.address||"N/A"}</p>
          <p><strong>Capacity:</strong> ${t.capacity||"N/A"}</p>
          <p><strong>Status:</strong> ${t.status||"N/A"}</p>
          <br>
          <p><em>\u{1F4F1} Offline Mode: Routing not available. Use external navigation apps for directions.</em></p>
        </div>
      `,buttons:[{text:"Open in Maps",handler:()=>{this.openInExternalMaps(t)}},{text:"Close",role:"cancel"}]})).present()})}openInExternalMaps(t){return l(this,null,function*(){let o=Number(t.latitude),e=Number(t.longitude),n=`https://www.google.com/maps/dir/?api=1&destination=${o},${e}&travelmode=walking`;try{window.open(n,"_system")}catch(a){console.error("Error opening external maps:",a),yield(yield this.toastCtrl.create({message:"Could not open external maps app",duration:3e3,color:"danger"})).present()}})}showTransportationOptions(t){return l(this,null,function*(){if(this.offlineStorage.isOfflineMode()||!navigator.onLine){let n=this.calculateDistance(this.userLocation?.lat||0,this.userLocation?.lng||0,Number(t.latitude),Number(t.longitude));yield this.showOfflineMarkerInfo(t,n);return}yield(yield this.alertCtrl.create({header:`Route to ${t.name}`,message:"Choose your transportation mode:",buttons:[{text:"\u{1F6B6}\u200D\u2642\uFE0F Walk",handler:()=>{this.routeToCenter(t,"walking")}},{text:"\u{1F6B4}\u200D\u2642\uFE0F Cycle",handler:()=>{this.routeToCenter(t,"cycling")}},{text:"\u{1F697} Drive",handler:()=>{this.routeToCenter(t,"driving")}},{text:"Cancel",role:"cancel"}]})).present()})}routeToCenter(t,o){return l(this,null,function*(){if(!this.userLocation)return;if(this.offlineStorage.isOfflineMode()||!navigator.onLine){console.log("\u{1F535} Offline mode: Cannot calculate routes"),yield(yield this.toastCtrl.create({message:"\u{1F4F1} Offline mode: Routing not available. Use external navigation apps.",duration:4e3,color:"warning"})).present(),yield this.openInExternalMaps(t);return}try{this.clearRoutes();let n="walking";switch(o){case"walking":n="walking";break;case"cycling":n="cycling";break;case"driving":n="driving";break}let a=yield fetch(`https://api.mapbox.com/directions/v5/mapbox/${n}/${this.userLocation.lng},${this.userLocation.lat};${t.longitude},${t.latitude}?geometries=geojson&access_token=${O.mapboxAccessToken}`);if(a.ok){let i=yield a.json();if(i&&i.routes&&i.routes.length>0){let s=i.routes[0],p=r.polyline(s.geometry.coordinates.map(h=>[h[1],h[0]]),{color:"#0066CC",weight:5,opacity:.8});p.addTo(this.map),yield(yield this.toastCtrl.create({message:`\u{1F535} Route: ${(s.distance/1e3).toFixed(2)}km, ${(s.duration/60).toFixed(0)}min via ${o}`,duration:4e3,color:"primary"})).present(),this.map.fitBounds(p.getBounds(),{padding:[50,50]})}}}catch(n){console.error("\u{1F535} FLOOD MAP: Error calculating individual route:",n),yield(yield this.toastCtrl.create({message:"Error calculating route. Please try again.",duration:3e3,color:"danger"})).present()}})}calculateDistance(t,o,e,n){let i=t*Math.PI/180,s=e*Math.PI/180,d=(e-t)*Math.PI/180,p=(n-o)*Math.PI/180,u=Math.sin(d/2)*Math.sin(d/2)+Math.cos(i)*Math.cos(s)*Math.sin(p/2)*Math.sin(p/2);return 6371e3*(2*Math.atan2(Math.sqrt(u),Math.sqrt(1-u)))}goBack(){this.router.navigate(["/tabs/home"])}ionViewWillLeave(){this.map&&this.map.remove()}static{this.\u0275fac=function(o){return new(o||m)}}static{this.\u0275cmp=b({type:m,selectors:[["app-flood-map"]],decls:18,vars:3,consts:[[3,"translucent"],["color","primary"],["slot","start"],[3,"click"],["name","chevron-back-outline"],[3,"fullscreen"],["id","flood-map",2,"height","100%","width","100%"],[1,"floating-info"],[1,"info-row"],["name","water","color","primary"],[1,"info-text"]],template:function(o,e){o&1&&(g(0,"ion-header",0)(1,"ion-toolbar",1)(2,"ion-buttons",2)(3,"ion-button",3),P("click",function(){return e.goBack()}),C(4,"ion-icon",4),f()(),g(5,"ion-title"),w(6,"\u{1F535} Flood Evacuation Centers"),f()()(),g(7,"ion-content",5),C(8,"div",6),g(9,"div",7)(10,"ion-card")(11,"ion-card-content")(12,"div",8),C(13,"ion-icon",9),g(14,"span"),w(15),f()(),g(16,"div",10),w(17," Showing evacuation centers specifically for flood disasters "),f()()()()()),o&2&&(v("translucent",!0),M(7),v("fullscreen",!0),M(8),L("Flood Centers: ",e.evacuationCenters.length,""))},dependencies:[V,$,N,I,_,D,T,E,S,R,x],styles:["#flood-map[_ngcontent-%COMP%]{height:100%;width:100%;z-index:1}.floating-info[_ngcontent-%COMP%]{position:absolute;top:20px;right:20px;z-index:1000;max-width:250px}.floating-info[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{margin:0;box-shadow:0 4px 8px #0003;border-radius:12px;background:#fffffff2;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.floating-info[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:12px}.floating-info[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:600;color:var(--ion-color-primary);margin-bottom:4px}.floating-info[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.floating-info[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%]{font-size:12px;color:var(--ion-color-medium);line-height:1.3}ion-toolbar[_ngcontent-%COMP%]{--background: var(--ion-color-primary);--color: white}ion-title[_ngcontent-%COMP%]{font-weight:600}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]{text-align:center;min-width:200px}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 8px;color:var(--ion-color-primary);font-size:16px;font-weight:600}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:4px 0;font-size:14px}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:var(--ion-color-dark)}"]})}}return m})();export{lt as FloodMapPage};
