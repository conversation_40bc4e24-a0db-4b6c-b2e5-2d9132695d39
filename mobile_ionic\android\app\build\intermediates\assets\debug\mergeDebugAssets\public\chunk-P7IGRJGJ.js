import{c as P}from"./chunk-LEWYLK2X.js";import{a as T}from"./chunk-EOTJZDZP.js";import{$ as g,m as u,o as p}from"./chunk-QCXYQNJC.js";import{f as I,g as h}from"./chunk-2R6CW7ES.js";var M=I(P());var x=(()=>{class m{constructor(e,t){this.http=e,this.offlineStorage=t,this.TILE_CACHE_SIZE=1e3,this.CACHE_EXPIRY_DAYS=30,this.PHILIPPINES_BOUNDS={north:21,south:4.5,east:127,west:116}}createOfflineTileLayer(){let e=M.tileLayer("",{attribution:"\xA9 OpenStreetMap contributors (Offline Mode)",maxZoom:18,minZoom:8});return e.createTile=(t,a)=>{let o=document.createElement("img");return this.getTileFromCache(t.z,t.x,t.y).then(i=>{i?(o.src=`data:image/png;base64,${i.tile_data}`,a(null,o)):(o.src=this.createPlaceholderTile(t),a(null,o))}).catch(i=>{console.error("Error loading cached tile:",i),o.src=this.createPlaceholderTile(t),a(null,o)}),o},e}preloadMapTiles(e,t,a=50,o){return h(this,null,function*(){console.log("\u{1F5FA}\uFE0F Starting map tile preload...");let i=[10,11,12,13,14,15],l=0,s=0;for(let r of i){let n=this.calculateTileBounds(e,t,a,r);l+=(n.maxX-n.minX+1)*(n.maxY-n.minY+1)}console.log(`\u{1F4CA} Total tiles to download: ${l}`);for(let r of i){let n=this.calculateTileBounds(e,t,a,r);for(let c=n.minX;c<=n.maxX;c++)for(let f=n.minY;f<=n.maxY;f++)try{yield this.downloadAndCacheTile(r,c,f),s++,o&&o(s,l),yield this.delay(100)}catch(d){console.warn(`Failed to cache tile ${r}/${c}/${f}:`,d),s++}}console.log("\u2705 Map tile preload completed")})}downloadAndCacheTile(e,t,a){return h(this,null,function*(){let o=yield this.getTileFromCache(e,t,a);if(o&&!this.isTileExpired(o.created_at))return;let i=`https://tile.openstreetmap.org/${e}/${t}/${a}.png`;try{let l=yield this.http.get(i,{responseType:"blob"}).toPromise();if(l){let s=yield this.blobToBase64(l);yield this.saveTileToCache(e,t,a,s)}}catch(l){throw new Error(`Failed to download tile: ${l}`)}})}getTileFromCache(e,t,a){return h(this,null,function*(){let o=yield this.offlineStorage.getMapTile(e,t,a);return o?{z:o.z,x:o.x,y:o.y,tile_data:o.tile_data,created_at:o.created_at}:null})}saveTileToCache(e,t,a,o){return h(this,null,function*(){yield this.offlineStorage.saveMapTile(e,t,a,o)})}calculateTileBounds(e,t,a,o){let i=e*Math.PI/180,l=Math.pow(2,o),s=a/111,r=a/(111*Math.cos(i)),n=Math.max(e-s,this.PHILIPPINES_BOUNDS.south),c=Math.min(e+s,this.PHILIPPINES_BOUNDS.north),f=Math.max(t-r,this.PHILIPPINES_BOUNDS.west),d=Math.min(t+r,this.PHILIPPINES_BOUNDS.east);return{minX:Math.floor((f+180)/360*l),maxX:Math.floor((d+180)/360*l),minY:Math.floor((1-Math.log(Math.tan(c*Math.PI/180)+1/Math.cos(c*Math.PI/180))/Math.PI)/2*l),maxY:Math.floor((1-Math.log(Math.tan(n*Math.PI/180)+1/Math.cos(n*Math.PI/180))/Math.PI)/2*l)}}createPlaceholderTile(e){let t=document.createElement("canvas");t.width=256,t.height=256;let a=t.getContext("2d");return a&&(a.fillStyle="#f0f0f0",a.fillRect(0,0,256,256),a.strokeStyle="#ccc",a.strokeRect(0,0,256,256),a.fillStyle="#999",a.font="12px Arial",a.textAlign="center",a.fillText("Offline Mode",128,120),a.fillText(`${e.z}/${e.x}/${e.y}`,128,140)),t.toDataURL()}isTileExpired(e){let t=new Date(e);return(new Date().getTime()-t.getTime())/(1e3*60*60*24)>this.CACHE_EXPIRY_DAYS}blobToBase64(e){return new Promise((t,a)=>{let o=new FileReader;o.onload=()=>{let i=o.result;t(i.split(",")[1])},o.onerror=a,o.readAsDataURL(e)})}delay(e){return new Promise(t=>setTimeout(t,e))}cleanupOldTiles(){return h(this,null,function*(){console.log("\u{1F9F9} Cleaning up old map tiles..."),console.log("\u2705 Tile cleanup completed")})}getCacheStats(){return h(this,null,function*(){let e=this.offlineStorage.getStorageInfo(),a=Math.floor(e.used/15e3),o=this.formatBytes(e.used);return{tileCount:a,sizeEstimate:o}})}formatBytes(e){if(e===0)return"0 Bytes";let t=1024,a=["Bytes","KB","MB","GB"],o=Math.floor(Math.log(e)/Math.log(t));return parseFloat((e/Math.pow(t,o)).toFixed(2))+" "+a[o]}static{this.\u0275fac=function(t){return new(t||m)(p(g),p(T))}}static{this.\u0275prov=u({token:m,factory:m.\u0275fac,providedIn:"root"})}}return m})();export{x as a};
